import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/vip/order_success_resp.dart';
import 'package:pieces_ai/app/model/vip/user_coupon.dart';
import 'package:pieces_ai/app/model/vip/vip_order_resp.dart';
import 'package:pieces_ai/app/model/vip/vip_ways.dart';
import 'package:pieces_ai/utils/http_utils/http_util.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';

import '../../../../authentication/models/user.dart' as pieces_user;
import '../../../utils/http_utils/task_result.dart';

const String getVipWays = '/user/vip/way';
const String getApplePayOrderPath = '/user/apple/pay/order';
const String applePayValidatePath = '/user/apple/pay/notify';
const String getUserCouponPath = '/user/query/user/coupon';
const String codeToCouponPath = '/user/coupon/use_code';
const String queryPayResultPath = '/user/query/pay/apply/web';
const String unRegisterPath = '/user/unregister';

const String vipWayUrl = '/movie/nest/vip/way';
const String vipOrderUrl = '/movie/nest/vip/order';
const String vipNotifyUrl = '/movie/nest/order/notify';

var logger = Logger(printer: PrettyPrinter());

class HttpsVipRepository {
  Future<List<VipWays>> getVipWaysInfo() async {
    // 获取VIP套餐方式
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      "way": Platform.isIOS ? 1 : 2,
    };

    // 发送POST请求
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + vipWayUrl,
      data: param,
    );

    // PostgrestList postgrestList =
    //     await Supabase.instance.client.from("t_vip_way").select("*");

    logger.d("获取VIP套餐请求参数：${param.toString()}");
    logger.d("获取VIP套餐响应值：${result}");

    // 检查响应数据是否有效
    if (result.data != null && result.data['code'] == 200) {
      logger.d("获取 VIP 套餐成功，返回数据：${result.data['data'].toString()}");

      // 解析返回的数据为 VipWays 列表
      List<dynamic> dataList = result.data['data'];
      List<VipWays> vipWaysData = dataList.map((item) {
        return VipWays.fromJson(item);
      }).toList();

      return vipWaysData;
    } else {
      logger.e("获取 VIP 套餐失败，错误信息：${result.data['message'] ?? '未知错误'}");
      throw Exception("获取 VIP 套餐失败：${result.data['message'] ?? '未知错误'}");
    }

    // List<VipWays> vipWaysData = [];
    // for (var item in postgrestList) {
    //   Discount discount = Discount(
    //       desc: item['discount']['desc'], hint: item['discount']['hint']);
    //   VipWays vipWays = VipWays(
    //       desc: item['desc'],
    //       discount: discount,
    //       id: item['id'],
    //       iosPay: item['ios_pay'],
    //       originPrice: item['origin_price'],
    //       price: item['price'],
    //       title: item['title'],
    //       way: item['way']);
    //   vipWaysData.add(vipWays);
    // }
    // return vipWaysData;
  }

  /// 生成 VIP 订单方法
  Future<VipOrderResp?> generateVipOrder(String cpack) async {
    try {
      // 构建请求参数
      Map<String, dynamic> param = await HttpUtil.withBaseParam();
      param['spec'] = {
        "cpack": cpack,
      };

      // 发送 POST 请求
      var result = await HttpUtil.instance.client.post(
        HttpUtil.apiBaseUrl + vipOrderUrl,
        data: param,
      );

      logger.d("生成 VIP 订单请求参数：${param.toString()}");
      logger.d("生成 VIP 订单响应值：${result}");

      // 检查响应数据
      if (result.data != null && result.data['code'] == 200) {
        logger.d("生成 VIP 订单成功，返回数据：${result.data['data'].toString()}");

        try {
          // 尝试从响应数据直接解析为VipOrderResp对象
          final data = result.data['data'];
          return VipOrderResp.fromJson(data);
        } catch (e) {
          // 兼容老接口，如果没有order_pack字段，使用空字符串
          String orderInfo = result.data['data']['order_info'] ?? "";
          String orderPack = result.data['data']['order_pack'] ?? "";
          
          // 如果order_pack为空，但orderInfo不为空，则使用orderInfo作为orderPack
          if (orderPack.isEmpty && orderInfo.isNotEmpty) {
            // 使用安全的用户ID格式（避免非法字符）
            orderPack = 'user_${orderInfo.hashCode.abs()}';
          }
          
          logger.d("解析 VIP 订单数据，orderInfo: $orderInfo, orderPack: $orderPack");
          return VipOrderResp(orderInfo: orderInfo, orderPack: orderPack);
        }
      } else {
        logger.e("生成 VIP 订单失败，错误信息：${result.data['message']}");
        return null;
      }
    } catch (e, stackTrace) {
      logger.e("生成 VIP 订单异常：$e");
      logger.e("【异常堆栈】：$stackTrace");
      return null;
    }
  }

  /// 发送订单成功通知
  Future<OrderSuccessResp> googlePayValidate(
      String orderInfo, String transactionId, String payInfo, {bool isAutoRenewing = false, String? gpaOrderId}) async {
    try {
      // 根据isAutoRenewing判断购买类型
      String buyType = isAutoRenewing ? "subs" : "inapps";
      
      // 构建请求参数
      Map<String, dynamic> param = await HttpUtil.withBaseParam();
      param['spec'] = {
        "order_info": orderInfo,
        "transaction_id": transactionId,
        "pay_info": payInfo,
        "buy_type": buyType, // 添加购买类型参数
      };
      
      // 如果提供了GPA订单ID，则添加到请求参数中
      if (gpaOrderId != null && gpaOrderId.isNotEmpty) {
        param['spec']["gpa"] = gpaOrderId;
        logger.d("【支付流程】- 谷歌订单ID(GPA): $gpaOrderId");
      }

      logger.d("【支付流程】发送谷歌支付验证请求，详细参数：");
      logger.d("【支付流程】- 订单信息: $orderInfo");
      logger.d("【支付流程】- 交易ID: $transactionId");
      logger.d("【支付流程】- 支付商品信息: $payInfo");
      logger.d("【支付流程】- 购买类型: $buyType (自动续费: $isAutoRenewing)");
      logger.d("【支付流程】- 完整请求参数: $param");

      // 发送 POST 请求
      var result = await HttpUtil.instance.client.post(
        HttpUtil.apiBaseUrl + vipNotifyUrl,
        data: param,
      );

      logger.d("【支付流程】发送订单通知请求参数：$param");
      logger.d("【支付流程】发送订单通知响应状态码：${result.statusCode}");
      logger.d("【支付流程】发送订单通知响应Headers：${result.headers}");
      logger.d("【支付流程】发送订单通知完整响应对象：${result.toString()}");
      logger.d("【支付流程】发送订单通知响应数据：${result.data.toString()}");

      // 检查响应数据
      if (result.data != null && result.data['code'] == 200) {
        logger.d("【支付流程】发送订单通知成功，返回数据：${result.data['data']}");

        // 解析并返回订单成功的响应结果
        final data = result.data['data'];
        if (data != null) {
          OrderSuccessResp resp = OrderSuccessResp.fromJson(data);
          logger.d("【支付流程】解析后的订单成功响应：$resp");
          logger.d("【支付流程】VIP到期时间：${resp.endTime}");
          return resp;
        } else {
          logger.e("【支付流程】发送订单通知成功，但未返回有效数据");
          throw Exception("发送订单通知成功，但未返回有效数据");
        }
      } else {
        logger.e("【支付流程】发送订单通知失败，错误码：${result.data?['code']}，错误信息：${result.data?['message']}");
        throw Exception("发送订单通知失败：${result.data?['message'] ?? '未知错误'}");
      }
    } catch (e, stackTrace) {
      logger.e("【支付流程】发送订单通知异常：$e");
      logger.e("【支付流程】异常堆栈：$stackTrace");
      throw e;
    }
  }

  ///删除账户
  Future<int> deleteAccount(String aes) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      'aes': aes,
    };
    param['app'] = {'ver_code': ********}; //传这个服务器大于这个版本号不会自动拼接
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + unRegisterPath,
      data: param,
    );
    // print("获取风格数据返回：" + result.data.toString());
    if (result.data != null) {
      if (result.data['code'] == 200) {
        logger.d("删除注销账户返回" + result.data['data'].toString());
        return 200;
      } else {
        return 500; // Handle the case where the API call was not successful
      }
    }
    return 500;
  }

  ///获取苹果支付的的ProductId
  Future<String> getApplePayOrder(String productId, int vid) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      'bizType': 60,
      'productId': productId,
      'vid': vid,
    };
    param['app'] = {'ver_code': ********}; //传这个服务器大于这个版本号不会自动拼接
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + getApplePayOrderPath,
      data: param,
    );
    logger.d("请求生成支付订单：${param.toString()}");
    if (result.data != null) {
      if (result.data['code'] == 200) {
        logger.d("生成IOS订单，获取到结果" + result.data['data'].toString());
        String orderInfo = result.data['data']['orderInfo'];
        return orderInfo;
      } else {
        return ""; // Handle the case where the API call was not successful
      }
    }
    return "";
  }

  ///服务器验证苹果支付是否完成
  Future<int> applePayValidate(
      String vpack, String payInfo, String transaction_id) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      'vpack': vpack,
      'payInfo': payInfo,
      'transaction_id': transaction_id,
    };
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + applePayValidatePath,
      data: param,
    );
    logger.d("请求生成支付订单：${param.toString()}");
    logger.d("获取IOS支付结果返回：" + result.data.toString());
    if (result.data != null) {
      if (result.data['code'] == 200) {
        logger.d(
            "IOS支付服务器验证是否成功，购买商品是否生效，获取到结果" + result.data['data'].toString());
        String data = result.data['data'];
        return 200;
      } else {
        return 500; // Handle the case where the API call was not successful
      }
    }
    return 500;
  }

  Future<List<UserCoupon>> getUserCoupons() async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + getUserCouponPath,
      data: param,
    );
    if (result.data != null) {
      if (result.data['code'] == 200) {
        debugPrint("拿到优惠券数据" + result.data['data'].toString());
        List<dynamic> userCouponDy = result.data['data'];
        List<UserCoupon> UserCouponList = [];
        userCouponDy.forEach((element) {
          UserCoupon userCoupon = UserCoupon.fromJson(element);
          UserCouponList.add(userCoupon);
        });
        return UserCouponList;
      } else {
        return [];
      }
    }
    return [];
  }

  ///券码兑换优惠券
  Future<TaskResult<UserCoupon>> codeToCoupons(String code) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {'code': code};
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + codeToCouponPath,
      data: param,
    );
    if (result.data != null) {
      if (result.data['code'] == 200) {
        debugPrint("兑换成功" + result.data['data'].toString());
        // UserCoupon userCoupon = UserCoupon.fromJson(result.data['data']);
        return TaskResult<UserCoupon>(data: null, success: true);
      } else {
        debugPrint("兑换失败" + result.data['data'].toString());
        return TaskResult<UserCoupon>(
            data: null, success: false, msg: result.data['msg']);
      }
    }
    return TaskResult<UserCoupon>(
        data: null, success: false, msg: result.statusCode.toString());
  }

  ///查询二维码扫码支付结果
  Future<Map<String, dynamic>> queryPayResult(
      String order_id, int product_type) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['order_id'] = order_id;
    param['product_type'] = product_type;
    var result = await HttpUtil.instance.client.get(
      HttpUtil.apiBaseUrl + queryPayResultPath,
      queryParameters: param,
    );
    if (result.data != null) {
      if (result.data['code'] == 200) {
        debugPrint("查询支付结果成功：" + result.data.toString());
        Map<String, dynamic> resultMapData = result.data['data'];
        return resultMapData;
      } else {
        return {}; // Handle the case where the API call was not successful
      }
    }
    return {};
  }
}
