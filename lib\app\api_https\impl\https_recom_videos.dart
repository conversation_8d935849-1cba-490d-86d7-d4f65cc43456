import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/VideoType.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../utils/http_utils/http_util.dart';
import '../../model/videos/video_resp.dart';
import '../ai_recom_videos_repository.dart';

const String getRecomVideosUrl = '/recom/list';
const String getTypeVideoList = '/filter/list';
var logger = Logger(printer: PrettyPrinter(methodCount: 0));

///定一个枚举，分别是new_release,banner,recom,high_score四个字符串
enum WorkType {
  new_release,
  banner,
  recom,
  high_score,
}

class HttpsRecomVideos extends RecomVideosRepository {
  @override
  Future<List<VideoRespVo>> loadTypeData(int page, int count, int type_id,
      String region, int year, double score, int sort) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      "page": page,
      "count": count,
      "type_id": type_id,
      "upack": "str",
      "region": region,
      "year": year,
      "score": score,
      "sort": sort
    };
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + getTypeVideoList,
      data: param,
    );
    logger.d("拿到分类数据的请求参数：${param.toString()}");

    if (result.data != null) {
      if (result.data['code'] == 200) {
        logger.d("拿到分类数据：" +
            result.data['data']['work']['work_list'].length.toString());
        List<VideoRespVo> newReleaseList = [];
        for (var item in result.data['data']['work']['work_list']) {
          newReleaseList.add(VideoRespVo.fromJson(item));
        }
        return newReleaseList;
      } else {
        return [];
      }
    }
    return [];
  }

  @override
  Future<Map<String, List<VideoRespVo>>> loadRecomVideoList(
      VideoType videoType) async {
    //查询status=1的数据
    // Calculate the offset based on the page parameter
    // int offset = page * 10;

    // Query data with pagination
    // PostgrestList postgrestList = await Supabase.instance.client
    //     .from("video")
    //     .select()
    //     .eq('status', 1)
    //     .order('year', ascending: false)
    //     .range(offset, offset + 9); // Fetch 10 items per page
    //
    // List<VideoRespVo> recomVideoData = [];
    // for (var item in postgrestList) {
    //   recomVideoData.add(VideoRespVo.fromJson(item));
    // }
    // return recomVideoData;
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      "scenario": videoType.id == 1 ? 0 : 1,
      // "scenario": 0,
      'page': 0,
      "count": 10,
      "type_id": videoType.id == 1 ? 0 : videoType.id,
      "upack": "str"
    };
    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + getRecomVideosUrl,
      data: param,
    );
    logger.d("拿到推荐视频数据请求${param.toString()}");

    if (result.data != null) {
      if (result.data['code'] == 200) {
        Map<String, List<VideoRespVo>> recomVideoList = {};
        //获取data中的work对象
        var work = result.data['data']['work'];
        logger.d("拿到推荐视频数据" + work.length.toString());
        //分别从work中获取,new_release,banner,recom,high_score四个集合，每个集合中的是一组VideoRespVo对象
        List<VideoRespVo> newReleaseList = [];
        List<VideoRespVo> bannerList = [];
        List<VideoRespVo> recomList = [];
        List<VideoRespVo> highScoreList = [];
        recomVideoList[WorkType.new_release.name] = newReleaseList;
        recomVideoList[WorkType.banner.name] = bannerList;
        recomVideoList[WorkType.recom.name] = recomList;
        recomVideoList[WorkType.high_score.name] = highScoreList;
        for (var item in work[WorkType.new_release.name]) {
          newReleaseList.add(VideoRespVo.fromJson(item));
        }
        for (var item in work[WorkType.banner.name]) {
          bannerList.add(VideoRespVo.fromJson(item));
        }
        //判断是否包含recom字段
        if (work.containsKey(WorkType.recom.name)) {
          for (var item in work[WorkType.recom.name]) {
            recomList.add(VideoRespVo.fromJson(item));
          }
        }
        for (var item in work[WorkType.high_score.name]) {
          highScoreList.add(VideoRespVo.fromJson(item));
        }
        return recomVideoList;
      } else {
        return {};
      }
    }
    return {};
  }

  @override
  Future<List<VideoType>> loadVideoTypeList() async {
    // Query data with pagination
    PostgrestList postgrestList = await Supabase.instance.client
        .from("video_types")
        .select()
        .eq('status', 0)
        .order('sort', ascending: true);

    List<VideoType> videoTypeList = [];
    for (var item in postgrestList) {
      videoTypeList.add(VideoType.fromJson(item));
    }
    return videoTypeList;
  }

  @override
  Future<List<VideoRespVo>> loadSearchVideoList(
      int page, int count, String words) async {
    // Calculate the offset based on the page parameter
    int offset = page * count;
    logger.d("搜索关键字：$words,page: $page,count: $count");
    // Build the query with the provided parameters
    var query = Supabase.instance.client
        .from("video")
        .select()
        // .eq('type_id', type_id)
        // .eq('region', region)
        //模糊查询'description', 'title'字段
        // .ilike('description', '%$words%')
        //搜索忽略大小写
        .or('description.ilike.%$words%,name.ilike.%$words%')
        .eq('status', 1) //只查询status=1的数据
        //按year字段排序
        .order('year', ascending: false)
        .order('id', ascending: true) // 添加唯一字段排序
        .range(offset, offset + count - 1); // Fetch 10 items per page

    // Execute the query
    PostgrestList postgrestList = await query;

    // Convert the result to a list of VideoRespVo
    List<VideoRespVo> filteredVideoList = [];
    for (var item in postgrestList) {
      final videoRespVo = VideoRespVo.fromJson(item);
      // logger.d("${videoRespVo.id} ${videoRespVo.name}");
      filteredVideoList.add(videoRespVo);
    }

    logger.d("搜索到的视频数据：" + filteredVideoList.length.toString());
    return filteredVideoList;
  }

  Future<Duration> loadPlaybackPosition(int movieId, String authToken) async {
    try {
      // 查询数据库中的播放位置
      final response = await Supabase.instance.client
          .from('t_watch_history')
          .select('position')
          .eq('access_token', authToken)
          .eq('movie_id', movieId)
          .single();

      if (response['position'] != null) {
        final positionInSeconds = response['position'] as int;
        return Duration(seconds: positionInSeconds); // 返回 Duration 类型的播放位置
      }
    } catch (e) {
      print('Error loading playback position: $e');
    }
    return Duration(seconds: 0); // 如果没有找到记录，返回 0 秒的 Duration
  }

  // 添加观看记录方法
  Future<void> addViewHistory(
      int movieId, int position, int progress, String authToken) async {
    try {
      // 获取当前时间
      final currentTime = DateTime.now();

      logger.d("添加观看历史记录${authToken} $movieId $position");
      // 使用 upsert 方法尝试更新记录，如果不存在则插入新记录
      await Supabase.instance.client.from('t_watch_history').upsert({
        'access_token': authToken,
        'movie_id': movieId,
        'position': position,
        'progress': progress,
        'update_time': currentTime.toUtc().toIso8601String(),
      }, onConflict: 'access_token,movie_id') // 指定冲突列
          .match({'access_token': authToken, 'movie_id': movieId}); // 匹配条件
    } catch (e) {
      // 处理错误
      logger.d('Error recording watch history: $e');
      rethrow;
    }
  }

  // 添加收藏的方法
  Future<void> addCollection(int movieId, String authToken) async {
    try {
      // 获取当前时间
      final currentTime = DateTime.now();

      logger.d("添加收藏记录${authToken} $movieId");

      await Supabase.instance.client.from('t_collection').upsert({
        'access_token': authToken,
        'movie_id': movieId,
        'update_time': currentTime.toUtc().toIso8601String(),
      }, onConflict: 'access_token,movie_id').match(
          {'access_token': authToken, 'movie_id': movieId});
    } catch (e) {
      logger.d('Error recording collection: $e');
      rethrow;
    }
  }

  // 删除收藏的方法
  Future<void> deleteCollection(int movieId, String authToken) async {
    try {
      logger.d("删除收藏记录${authToken} $movieId");

      await Supabase.instance.client
          .from('t_collection')
          .delete()
          .match({'access_token': authToken, 'movie_id': movieId});
    } catch (e) {
      logger.d('Error deleting collection: $e');
      rethrow;
    }
  }

  // 获取收藏状态的方法
  Future<int> getCollectionStatus(int movieId, String authToken) async {
    try {
      final response = await Supabase.instance.client
          .from('t_collection')
          .count(CountOption.exact)
          .eq('access_token', authToken)
          .eq('movie_id', movieId);

      logger.d('获取收藏状态: $response');

      return response;
    } catch (e) {
      logger.d('Error getting collection status: $e');
      return 0;
    }
  }
}
