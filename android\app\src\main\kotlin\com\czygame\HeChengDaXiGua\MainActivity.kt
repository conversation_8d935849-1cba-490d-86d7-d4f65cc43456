package com.czygame.HeChengDaXiGua

import io.flutter.embedding.android.FlutterActivity
import android.content.Intent
import android.util.Log
import com.singular.flutter_sdk.SingularBridge // 添加 Singular 的 Bridge 类

class MainActivity : FlutterActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    // 添加以下代码处理深度链接
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        
        try {
            // 启用Singular深度链接处理
            SingularBridge.onNewIntent(intent)
            
            // 记录intent的接收
            Log.d(TAG, "接收到新Intent: ${intent.action}")
        } catch (e: Exception) {
            // 捕获任何可能的异常，防止应用崩溃
            Log.e(TAG, "处理Intent时发生异常: ${e.message}", e)
        }
    }
}