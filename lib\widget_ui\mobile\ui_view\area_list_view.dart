import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';

import '../theme/fitness_app_theme.dart';

///首页区域的item电影视图
class AreaListView extends StatefulWidget {
  const AreaListView(
      {Key? key,
      required this.videoRespVoList})
      : super(key: key);

  final List<VideoRespVo> videoRespVoList;

  @override
  _AreaListViewState createState() => _AreaListViewState();
}

class _AreaListViewState extends State<AreaListView>
    with TickerProviderStateMixin {
  AnimationController? animationController;

  @override
  void initState() {
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    super.initState();
  }

  @override
  void dispose() {
    animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1,
      child: Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 8),
        child: GridView.builder(
          //禁止滑动，不响应滑动事件
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only( top: 8, bottom: 8),
          scrollDirection: Axis.vertical,
          itemCount: widget.videoRespVoList.length,
          itemBuilder: (BuildContext context, int index) {
            final int count = widget.videoRespVoList.length;
            final Animation<double> animation =
            Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: animationController!,
                curve: Interval((1 / count) * index, 1.0,
                    curve: Curves.fastOutSlowIn),
              ),
            );
            animationController?.forward();
            return AreaView(
              videoRespVo: widget.videoRespVoList[index],
              animation: animation,
              animationController: animationController!,
            );
          },
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            // mainAxisSpacing: 8.0,
            // crossAxisSpacing: 8.0,
            childAspectRatio: 5/8, // Adjusted aspect ratio
          ),
        ),
      ),
    );
  }
}

class AreaView extends StatelessWidget {
  const AreaView({
    Key? key,
    this.videoRespVo,
    this.animationController,
    this.animation,
  }) : super(key: key);

  final VideoRespVo? videoRespVo;
  final AnimationController? animationController;
  final Animation<double>? animation;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController!,
      builder: (BuildContext context, Widget? child) {
        return FadeTransition(
          opacity: animation!,
          child: Transform(
            transform: Matrix4.translationValues(
                0.0, 50 * (1.0 - animation!.value), 0.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8.0),
                    bottomLeft: Radius.circular(8.0),
                    bottomRight: Radius.circular(8.0),
                    topRight: Radius.circular(8.0)),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  focusColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                  splashColor: FitnessAppTheme.nearlyDarkBlue.withOpacity(0.2),
                  onTap: () {
                    Navigator.of(context).pushNamed(UnitRouter.video_detail,
                        arguments: videoRespVo);
                    // Navigator.of(context).pushNamed(
                    //     UnitRouter.article_detail,
                    //     arguments: videoRespVo?.playLink);
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: CachedNetworkImage(
                          imageUrl: videoRespVo?.coverImage??'',
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Center(
                            child: CircularProgressIndicator(),
                          ),
                          errorWidget: (context, url, error) =>
                              Icon(Icons.error),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Text(
                          videoRespVo!.name,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
