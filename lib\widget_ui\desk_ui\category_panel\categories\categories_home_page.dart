import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:app/app/router/unit_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class CategoriesHomePage extends StatelessWidget {
  const CategoriesHomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF121212), // 设置纯色背景 #121212
      ),
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 75),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.of(context).categoriesTitle,
                    style: const TextStyle(color: Colors.white, fontSize: 20),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  SearchBar(), // 假设这是一个独立的搜索栏组件
                  const SizedBox(height: 11),
                  Expanded(child: TabContent()), // 假设这是一个独立的标签页内容组件
                ],
              ),
            ),
          ),
          // BottomDownloadButton(), // 如果需要，可以在这里添加底部按钮
        ],
      ),
    );
  }
}

class SearchBar extends StatelessWidget {
  final TextEditingController _textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0x1FFFFFFF), // #FFFFFF with 6% opacity
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      child: Row(
        children: [
          Image.asset(
            'assets/images/icon/<EMAIL>', // 替换为实际的图标路径
            width: 22,
            height: 22,
          ),
          SizedBox(width: 15),
          Expanded(
            child: TextField(
              controller: _textEditingController,
              style: TextStyle(color: Colors.white, fontSize: 16),
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context).categoriesSearchDesc,
                hintStyle: TextStyle(color: Color(0xFF7B7B7B)),
                border: InputBorder.none,
              ),
              textInputAction: TextInputAction.search, // 设置软键盘的按钮为“搜索”
              onSubmitted: (value) {
                handleSearch(context, value); // 当按下“搜索”时触发的回调
              },
            ),
          )
        ],
      ),
    );
  }

  void handleSearch(BuildContext context, String value) {
    if (value.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Please enter a search term"),
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      Navigator.pushNamed(
        context,
        UnitRouter.video_search,
        arguments: value,
      );
    }
  }
}

class TabContent extends StatefulWidget {
  @override
  _TabContentState createState() => _TabContentState();
}

class _TabContentState extends State<TabContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 定义数据结构
  final List<Map<String, dynamic>> _tabsData = [
    {
      'label': 'Movies',
      'items': [
        "Action",
        "Adventure",
        "Comedy",
        "Crime",
        "Documentary",
        "Drama",
        "Family",
        "Fantasy",
        "History",
        "Horror",
        "Music",
        "Mystery",
        "Reality",
        "Romance",
        "Science Fiction",
        "Thriller",
        "War",
        "Western",
        "Animation",
        "18+"
      ],
    },
    {
      'label': 'TV Shows',
      'items': ["Drama", "Fantasy", "Horror"],
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabsData.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(
              color: Color(0xFFFE4600),
              width: 2.0,
            ),
            insets: EdgeInsets.symmetric(horizontal: 8.0),
          ),
          labelColor: Color(0xFFFE4600),
          unselectedLabelColor: Color(0xFF8C8A8B),
          tabs: _tabsData.map((tab) => Tab(text: tab['label'])).toList(),
        ),
        Expanded(
          child: Container(
            child: TabBarView(
              controller: _tabController,
              children: _tabsData.map((tab) {
                return CategoryContent(
                    category: tab['label'], items: tab['items']);
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}

class CategoryContent extends StatelessWidget {
  final String category;
  final List<String> items;

  CategoryContent({required this.category, required this.items});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 20.0, bottom: 12.0), // 左对齐的文本
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              category,
              style: TextStyle(color: Color(0xFF8C8A8B), fontSize: 14),
            ),
          ),
        ),
        Expanded(
          child: GridView.builder(
            padding: EdgeInsets.zero, // 移除默认的内边距
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 6,
              mainAxisSpacing: 6,
              childAspectRatio: 1.5, // 设置宽高比来控制元素高度
            ),
            itemCount: items.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  logger.d('跳转搜索页：${items[index]}');
                  Navigator.pushNamed(
                    context,
                    UnitRouter.video_search,
                    arguments: items[index],
                  );
                },
                child: Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: Color(0x1FFFFFFF), // #FFFFFF with 6% opacity
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    items[index],
                    style: TextStyle(color: Color(0xFFC1C1C1), fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class BottomDownloadButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      color: Colors.white,
      child: Align(
        alignment: Alignment.centerRight,
        child: Container(
          margin: EdgeInsets.only(right: 11),
          child: ElevatedButton(
            onPressed: () {
              // TODO: Implement download functionality
            },
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateProperty.all<Color>(Color(0xFF3478F5)),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10))),
              padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
                  EdgeInsets.symmetric(vertical: 8, horizontal: 20)),
            ),
            child: Text('Download',
                style: TextStyle(color: Colors.white, fontSize: 11)),
          ),
        ),
      ),
    );
  }
}
