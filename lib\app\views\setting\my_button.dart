import 'package:flutter/material.dart';

class MyButton extends StatelessWidget {
  final String title;
  final onClick onpress;
  final double? width;
  final double? height;
  final double? radius;

  const MyButton(
      {Key? key,
      this.height = 36.0,
      this.width = 80.0,
      this.radius = 4.0,
      required this.title,
      required this.onpress})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(radius!),
        child: Si<PERSON>B<PERSON>(
          width: width!,
          height: height!,
          child: ElevatedButton(
            child: Text(
              title,
              style: TextStyle(
                color: Colors.white,
              ),
            ),
            onPressed: onpress,
            style: ButtonStyle(
              backgroundColor:
                  MaterialStateColor.resolveWith((states) => Color(0xFF000000)),
            ),
          ),
        ));
  }
}

typedef onClick = void Function();
