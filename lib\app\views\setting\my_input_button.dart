import 'package:flutter/material.dart';

class MyInputButton extends StatefulWidget {
  final double? width;
  final double? heigth;
  final double? lableWidth;
  final String lableText;
  final String text;
  final onTextChange textChange;

  const MyInputButton(
      {Key? key,
      this.width = 400,
      this.heigth,
      this.lableWidth = 150,
      required this.lableText,
      required this.text,
      required this.textChange})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyInputButtonState();
}

typedef onTextChange = void Function(String);

class _MyInputButtonState extends State<MyInputButton> {
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.text;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4.0),
      child: Container(
        color: Color(0xFFA6A6A6),
        width: widget.width,
        height: 36,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4.0),
              child: Container(
                width: widget.lableWidth,
                height: 36,
                alignment: Alignment.center,
                color: Colors.black,
                child: Text(
                  widget.lableText,
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Expanded(
                child: Padding(
              padding: EdgeInsets.all(10),
              child: TextField(
                  autofocus: true,
                  cursorColor: Colors.white,
                  controller: _controller,
                  decoration: InputDecoration(
                      enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent)),
                      focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent))),
                  onChanged: (v) {
                    widget.textChange(v);
                  }),
            ))
          ],
        ),
      ),
    );
  }
}
