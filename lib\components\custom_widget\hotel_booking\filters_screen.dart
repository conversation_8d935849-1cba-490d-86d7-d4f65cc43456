import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/VideoType.dart';
import 'package:pieces_ai/components/custom_widget/hotel_booking/fliters_load_more.dart';

import 'hotel_app_theme.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class FiltersScreen extends StatefulWidget {
  final List<VideoType> videoTypes;

  const FiltersScreen({super.key, required this.videoTypes});

  @override
  _FiltersScreenState createState() => _FiltersScreenState();
}

class _FiltersScreenState extends State<FiltersScreen> {
  String _selectedNewestHighPraise = 'Newest';
  String _selectedRegion = 'United States';
  VideoType _selectedType = VideoType(id: 6, name: 'Action');
  String _selectedYear = '2025';
  late List<VideoType> videoTypesSelected;

  @override
  void initState() {
    //videoTypesSelected是过滤掉videoTypes中的id为0，1，2，3，4的元素
    videoTypesSelected = widget.videoTypes
        .where((element) => element.id > 4)
        .toList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: HotelAppTheme.buildDarkTheme().scaffoldBackgroundColor,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            'Filters',
            style: TextStyle(color: Colors.white),
          ),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Column(
          children: <Widget>[
            getFilterOptions(),
            Expanded(child: FiltersLoadMore(key: filtersLoadMoreKey)),
          ],
        ),
      ),
    );
  }

Widget getFilterOptions() {
  return Container(
    height: 220, // Adjust the height as needed
    child: ListView(
      children: <Widget>[
        buildFilterRow('Newest/High praise', ['Newest', 'High praise'],
            _selectedNewestHighPraise, (value) {
          int sort = value == 'Newest' ? 0 : 1;
          filtersLoadMoreKey.currentState?.refresh(sort: sort);
          setState(() {
            _selectedNewestHighPraise = value;
          });
        }),
        buildFilterRow(
            'Region',
            [
              'United States',
              'France',
              'Japan',
              'India',
              'Italy',
              'South Korea',
              'Germany',
              'Spain',
              'Canada',
              'Australia',
              'Russia',
              'Sweden',
              'Mexico',
              'Brazil',
              'Turkey',
              'Denmark',
              'Argentina',
              'Poland',
              'Thailand',
              'Netherlands',
              'Belgium',
              'Israel',
              'Austria',
              'Finland',
              'Norway',
              'Switzerland',
              'Ireland',
              'Czech Republic',
              'Singapore',
              'Portugal',
              'Romania',
              'Hungary',
              'Colombia',
              'Indonesia',
              'New Zealand',
              'Philippines',
              'Greece',
              'Ukraine',
              'Pakistan',
              'Croatia',
              'Slovenia',
              'Bulgaria',
              'Luxembourg',
              'Lithuania',
              'Qatar',
              'Estonia',
              'Venezuela',
              'Uruguay',
              'North Macedonia',
              'Georgia',
              'Mongolia',
              'Malta',
              'Ecuador',
              'Lebanon',
              'BiH'
            ],
            _selectedRegion, (value) {
          filtersLoadMoreKey.currentState?.refresh(region: value);
          setState(() {
            _selectedRegion = value;
          });
        }),
        buildFilterRowType('Type', videoTypesSelected, _selectedType, (value) {
          filtersLoadMoreKey.currentState?.refresh(type_id: value.id);
          setState(() {
            _selectedType = value;
          });
        }),
        buildFilterRow(
            'Year',
            List.generate(16, (index) => (2025 - index).toString()),
            _selectedYear, (value) {
          //刷新所有的视频加载数据
          logger.d("刷新所有的视频加载数据");
          filtersLoadMoreKey.currentState?.refresh(year: int.parse(value));
          setState(() {
            _selectedYear = value;
          });
        }),
      ],
    ),
  );
}

  Widget buildFilterRow(String title, List<String> options,
      String selectedValue, ValueChanged<String> onChanged) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: options.map((option) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: FilterChip(
                    label: Text(
                      option,
                      style: TextStyle(
                        color: selectedValue == option
                            ? HotelAppTheme.buildDarkTheme().primaryColor
                            : Colors.white,
                      ),
                    ),
                    selected: selectedValue == option,
                    onSelected: (selected) {
                      if (selected) {
                        onChanged(option);
                      }
                    },
                    backgroundColor: Colors.black,
                    selectedColor: Colors.transparent,
                    showCheckmark: false,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFilterRowType(String title, List<VideoType> options,
      VideoType selectedValue, ValueChanged<VideoType> onChanged) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: options.map((option) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: FilterChip(
                    label: Text(
                      option.name,
                      style: TextStyle(
                        color: selectedValue.id == option.id
                            ? HotelAppTheme.buildDarkTheme().primaryColor
                            : Colors.white,
                      ),
                    ),
                    selected: selectedValue.id == option.id,
                    onSelected: (selected) {
                      if (selected) {
                        onChanged(option);
                      }
                    },
                    backgroundColor: Colors.black,
                    selectedColor: Colors.transparent,
                    showCheckmark: false,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
