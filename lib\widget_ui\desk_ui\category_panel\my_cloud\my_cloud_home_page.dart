import 'dart:async';

import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/api_https/impl/https_diy_roles_repository.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';
import 'package:pieces_ai/app/views/collection/collection_page.dart';
import 'package:pieces_ai/app/views/watch_history/watch_history_page.dart';
import 'package:share_plus/share_plus.dart';
import 'package:singular_flutter_sdk/singular.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../app/data/vip_features.dart';
import '../../../../authentication/blocs/authentic/bloc.dart';
import '../../../../authentication/blocs/authentic/state.dart' as pieces_state;
import '../../../../authentication/models/user.dart' as pieces_user;
import '../../../../authentication/views/mobile/login/login_page.dart';
import '../../../../utils/analytics_helper.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

///我的页面
class MyCloudHomePage extends StatelessWidget {
  const MyCloudHomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xB3C50101), Color(0xFF000000)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.3],
        ),
      ),
      child: Container(
        color: Color(0xDC000000),
        child: ConstrainedBox(
          // 确保内容高度不会无限扩展
          constraints:
              BoxConstraints(minHeight: MediaQuery.of(context).size.height),
          child: IntrinsicHeight(
            // 让子组件根据内容自动调整高度
            child: Scaffold(
              backgroundColor: Colors.transparent, // 背景色透明以显示背景渐变
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                // 去除阴影
                actions: [
                  IconButton(
                    icon: Image.asset('assets/images/icon/<EMAIL>',
                        width: 24, height: 24),
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        UnitRouter.setting,
                      );
                    },
                  ),
                ],
                centerTitle: true,
                // 设置标题居中
                title: Text(
                  AppLocalizations.of(context).me,
                  style: TextStyle(
                    color: Colors.white, // 设置文字颜色为白色
                    fontSize: 18, // 可选：设置字体大小
                  ),
                ),
              ),
              body: CloudMyRoles(),
            ),
          ),
        ),
      ),
    );
  }
}

GlobalKey<_CloudMyRolesState> myRolesKey = GlobalKey();

class CloudMyRoles extends StatefulWidget {
  CloudMyRoles({Key? key}) : super(key: key);

  @override
  State<CloudMyRoles> createState() => _CloudMyRolesState();
}

class _CloudMyRolesState extends State<CloudMyRoles>
    with TickerProviderStateMixin {
  final HttpsDiyRolesRepository httpsDiyRolesRepository =
      HttpsDiyRolesRepository();

  // late Future<List<CustomRolePicture>> _futureAllDiyRoles;
  late TabController _tabController;
  int currentTotalRoleCount = 0;
  int currentSelectCategory = 0;

  @override
  void initState() {
    // _futureAllDiyRoles = httpsDiyRolesRepository.getAllDiyRoles();
    // 页面首次构建时调用
    _fetchPlayRecords();
    super.initState();
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('my_cloud');
  }

  @override
  Widget build(BuildContext context) {
    logger.d("构建我的页面");

    return Container(
        margin: EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min, // 确保 Column 只占用必要的高度
            children: [
              _buildUserInfoWidget(),
              _buildVipCardWidget(context, user),
              // _buildCheckToReceiveVipWidget(context),
              _buildFunctionListWidget(context, user),
              // Expanded(child: _buildMyRoles())
            ],
          ),
        ));
  }

  ///刷新个人信息，角色信息
  _refresh() {
    logger.d("刷新个人信息，角色信息");
    _fetchPlayRecords();
    setState(() {
      // _futureAllDiyRoles = httpsDiyRolesRepository.getAllDiyRoles();
      user = GlobalInfo.instance.user;
    });
  }

  pieces_user.User user = GlobalInfo.instance.user;

  Widget _buildUserInfoWidget() {
    logger.d('构建用户信息组件，当前用户VIP结束时间: ${user.vipEnd}');
    user = GlobalInfo.instance.user;
    String vipEndStr = user.vipEnd == null
        ? ""
        : DateFormat('y-MM-dd')
            .format(DateTime.fromMillisecondsSinceEpoch(user.vipEnd!));
    logger.d('格式化后的VIP结束时间: $vipEndStr');
    return BlocConsumer<AuthBloc, pieces_state.AuthState>(
      listener: (context, state) {
        logger.d("Auth状态变化: ${state.runtimeType}");
        if (state is pieces_state.AuthInitial) {
          _refresh();
        } else if (state is pieces_state.AuthSuccess) {
          _refresh();
        }
      },
      builder: (context, state) {
        logger.d("构建用户信息UI，当前用户VIP结束时间: ${user.vipEnd}");
        return Padding(
          padding: EdgeInsets.only(bottom: 27, top: 30),
          child: GestureDetector(
            onTap: () {
              logger.d("点击了用户信息:${user.authToken}");
              if (user.authToken?.isEmpty ?? true) {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return const LoginScreen();
                }));
                return;
              } else {
                logger.d("点击了用户信息111:${user}");
              }
            },
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: CachedNetworkImageProvider(user.headIcon),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 17),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
                      if (user.userId != -1)
                        Text(
                          "id：${user.userId}",
                          style: TextStyle(color: Color(0xFF808080)),
                        ),
                      if (user.userId != -1 && vipEndStr.isNotEmpty && (user.vipEnd != null && user.vipEnd! > 0))
                        Padding(
                          padding: EdgeInsets.only(top: 1),
                          child: Text(
                            AppLocalizations.of(context).vipExpired + ": " + vipEndStr,
                            style: TextStyle(color: Color(0xFF808080)),
                          ),
                        ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  // 创建VIP卡片组件的方法
  Widget _buildVipCardWidget(BuildContext context, pieces_user.User user) {
    // VIP特性列表
    List<VipFeature> vipFeatures =
        getVipFeatures(context); // 调用外部方法获取 VIP 特性列表;

    return GestureDetector(
        onTap: () {
          // Navigator.push(context, SlidePageRoute(child: VipBuyPage(
          //   doPaySuccess: (success) {
          //     logger.d("首页接收到是否支付成功消息：" + success.toString());
          //     if (success) {
          //       setState(() {});
          //     }
          //   },
          // )));
          // 跟踪简单事件
          Singular.event("myPageClickToVipCenter");
          // 点击跳转到会员中心页面
          if (user.userId != -1) {
            Navigator.of(context).pushNamed(UnitRouter.vip_center);
          } else {
            Navigator.of(context).pushNamed(UnitRouter.login);
          }
        },
        child: Card(
          color: Color.fromARGB(255, 48, 33, 54),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 6.0, 8.0, 10.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).vipCardTitle,
                          style: TextStyle(
                              color: Color(0xFFF5DFB1),
                              fontSize: 17,
                              fontWeight: FontWeight.bold),
                        ),
                        Text(
                          AppLocalizations.of(context).vipCardDescription,
                          style:
                              TextStyle(color: Color(0xFF9A90A0), fontSize: 11),
                        ),
                      ],
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 4, horizontal: 10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFFFBDFA6), Color(0xFFDBB360)],
                        ),
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Text(
                        AppLocalizations.of(context).vipCardButton,
                        style: TextStyle(
                            color: Color.fromARGB(255, 48, 33, 54),
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8), // 添加间距
                Row(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceBetween, // 子组件之间保持一定的间距
                  children: List.generate(vipFeatures.length, (index) {
                    return Expanded(
                      child: Container(
                        constraints: BoxConstraints(
                            maxWidth: 90, minHeight: 70), // 设置最大宽度
                        child: Column(
                          mainAxisAlignment:
                              MainAxisAlignment.start, // 确保内容顶部对齐
                          children: [
                            Image.asset(
                              vipFeatures[index].imagePath,
                              height: 40,
                              width: 40, // 调整图片大小以适应布局
                            ),
                            Text(
                              vipFeatures[index].text,
                              style: TextStyle(
                                  fontSize: 8, color: Color(0xFF9A90A0)),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildCheckToReceiveVipWidget(BuildContext context) {
    logger.d('构建签到领取会员功能区');
    return Card(
      color: Color(0xFF1E1E1E),
      child: Column(
        children: [
          _buildFeatureItem(context, "assets/images/icon/<EMAIL>",
              AppLocalizations.of(context).checkToReceiveVip, onTap: () {
            // 处理点击事件
          }),
        ],
      ),
    );
  }

  Widget _buildFunctionListWidget(BuildContext context, pieces_user.User user) {
    logger.d('构建功能列表区');
    return Card(
      color: Color(0xFF1E1E1E),
      child: Column(
        children: [
          Column(
            children: [
              _buildFeatureItem(
                  context,
                  "assets/images/icon/<EMAIL>",
                  AppLocalizations.of(context).playRecord, onTap: () {
                _trackButtonClick('watch_history_button');
                if (user.userId != -1) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => WatchHistoryPage()),
                  );
                } else {
                  Navigator.of(context).pushNamed(UnitRouter.login);
                }
              }),
              if (playRecords.isNotEmpty) _buildPlayRecordHorizontalList()
            ],
          ),
          // _buildDivider(),
          // _buildFeatureItem(
          //     context,
          //     "assets/images/icon/<EMAIL>",
          //     AppLocalizations.of(context).myDownload, onTap: () {
          //   // 处理点击事件
          // }),
          _buildDivider(),
          _buildFeatureItem(
              context,
              "assets/images/icon/<EMAIL>",
              AppLocalizations.of(context).collection, onTap: () {
            _trackButtonClick('collection_button');
            if (user.userId != -1) {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => CollectionPage()),
              );
            } else {
              Navigator.of(context).pushNamed(UnitRouter.login);
            }
          }),
          _buildDivider(),
          _buildFeatureItem(context, "assets/images/icon/<EMAIL>",
              AppLocalizations.of(context).share, onTap: () async {
            _trackButtonClick('share_app_button');
            final result = await Share.share('https://movienest.com');
            if (result.status == ShareResultStatus.success) {
              logger.i('Share success');
              // 添加分享埋点
              AnalyticsHelper().trackShare('app', contentId: 'movienest');
            }
          })
        ],
      ),
    );
  }

  //分割线
  Widget _buildDivider() {
    return Padding(
      padding: EdgeInsets.only(left: 58, right: 25),
      child: Divider(
        height: 1,
        color: Color(0xFF303030),
      ),
    );
  }

  Widget _buildFeatureItem(
      BuildContext context, String leadingImagePath, String title,
      {required VoidCallback onTap}) {
    return ListTile(
      leading: Image.asset(
        leadingImagePath,
        width: 24,
        height: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16.0,
        ),
      ),
      trailing: Icon(
        FontAwesomeIcons.angleRight,
        color: Colors.white,
        size: 24.0,
      ),
      onTap: onTap,
    );
  }

  List<PlayRecord> playRecords = [];

  Future<void> _fetchPlayRecords() async {
    try {
      final user = GlobalInfo.instance.user;
      if (user.userId == -1) {
        return;
      }
      final records = await getRecentPlayRecords(user); // 获取观看历史记录
      logger.d('加载观看历史记录成功: $records');
      setState(() {
        playRecords = records; // 更新状态
      });
    } catch (e) {
      logger.d('加载观看历史记录失败: $e');
    }
  }

  Future<List<PlayRecord>> getRecentPlayRecords(pieces_user.User user) async {
    try {
      // 查询最近的5个观看历史记录，并关联查询影片信息
      final response = await Supabase.instance.client
          .from('t_watch_history')
          .select(
              'movie_id, progress, update_time, video(*)') // 使用 video(*) 来获取 video 表的所有字段
          .eq('access_token', user.authToken.toString())
          .order('update_time', ascending: false)
          .limit(5);

      logger.d('加载观看历史记录:$response');

      // 将查询结果转换为 PlayRecord 列表
      playRecords = [];
      for (final record in response) {
        final progress = record['progress'];
        final videoData = record['video']; // 获取 video 的所有字段

        // 检查 videoData 是否存在且为 Map 类型
        if (videoData is Map<String, dynamic>) {
          // 将 videoData 转换为 VideoRespVo 对象
          final videoRespVo = VideoRespVo.fromJson(videoData);

          // 计算观看进度百分比
          final progressText = getProgressText(progress);

          // 创建 PlayRecord 对象
          playRecords.add(
            PlayRecord(
              progressText: progressText,
              videoRespVo: videoRespVo, // 将完整的 VideoRespVo 对象传递给 PlayRecord
            ),
          );
        } else {
          logger.w('视频数据格式不正确: $videoData');
        }
      }

      return playRecords;
    } catch (e) {
      logger.d('查询观看历史记录时发生错误: $e');
      rethrow;
    }
  }

// 获取影片总时长文字
  String getProgressText(int progress) {
    return '${AppLocalizations.of(context).progress}:$progress%';
  }

  Widget _buildPlayRecordHorizontalList() {
    logger.d('构建历史记录列表区');
    return Container(
      height: 210,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: playRecords.length, // 使用playRecords列表的长度作为itemCount
        itemBuilder: (context, index) {
          final record = playRecords[index];
          return GestureDetector(
              onTap: () {
                // 整个卡片的点击事件
                logger.d('点击了整个卡片 - ${record.videoRespVo.name}');
                // 在这里可以导航到详情页或其他操作
                Navigator.of(context).pushNamed(UnitRouter.video_detail,
                    arguments: record.videoRespVo);
              },
              child: Container(
                margin: EdgeInsets.only(left: 8.0, right: 8.0, bottom: 8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween, // 调整主轴对齐方式
                  children: [
                    Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: Image.network(
                            record.videoRespVo.coverImage ?? '', // 提供默认图片URL
                            height: 150, // 固定高度
                            fit: BoxFit.cover,
                            loadingBuilder: (BuildContext context, Widget child,
                                ImageChunkEvent? loadingProgress) {
                              if (loadingProgress == null) {
                                return child; // 图片加载完成后返回图片本身
                              }
                              return Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              );
                            },
                            errorBuilder: (BuildContext context,
                                Object exception, StackTrace? stackTrace) {
                              return Image.asset(
                                'assets/images/widgets/draft_empty.png',
                                height: 150, // 固定高度
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                        Positioned(
                          bottom: 8.0,
                          left: 0,
                          right: 0,
                          child: Text(
                            record.progressText,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      ],
                    ),
                    SizedBox(height: 2), // 添加间距
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 150), // 设置最大宽度与图片一致
                      child: Text(
                        record.videoRespVo.name,
                        maxLines: 1, // 设置最大行数防止超出容器
                        overflow: TextOverflow.ellipsis, // 当文本超出时显示省略号
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    SizedBox(height: 2), // 添加间距
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 150), // 设置最大宽度与图片一致
                      child: Text(
                        record.videoRespVo.description ?? '', // 提供默认数据,
                        maxLines: 1, // 设置最大行数防止超出容器
                        overflow: TextOverflow.ellipsis, // 当文本超出时显示省略号
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Color(0xFF7B7B7B)),
                      ),
                    )
                  ],
                ),
              ));
        },
      ),
    );
  }

  ShapeBorder get rRectBorder => const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(10)));

  // 添加按钮点击埋点
  void _trackButtonClick(String buttonName) {
    AnalyticsHelper().trackButtonClick(buttonName, position: 'my_cloud');
  }
}

class PlayRecord {
  final String progressText;
  VideoRespVo videoRespVo;

  PlayRecord({
    required this.progressText,
    required this.videoRespVo,
  });
}
