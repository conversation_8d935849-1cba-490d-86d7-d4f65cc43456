import 'package:json_annotation/json_annotation.dart';

part 'user_coupon.g.dart';

///UserCoupon
@JsonSerializable()
class UserCoupon {
  ///券码, 暂未使用，保留字段
  @JsonKey(name: "coupon_code")
  final String couponCode;

  ///优惠券有效期, 毫秒级时间戳
  @JsonKey(name: "endtime")
  final int endtime;

  ///优惠券id, 优惠券唯一标识，使用时需要传递到后台
  @JsonKey(name: "id")
  final int id;

  ///优惠金额, type为1和2时代表金额（以分为单位），为3时代表比例（0~100的百分数）
  @JsonKey(name: "money")
  final int money;

  ///满减金额, 仅在type=2时使用，以分为单位
  @JsonKey(name: "money_level")
  final int moneyLevel;

  ///适用套餐, 暂未使用，保留字段
  @<PERSON>sonKey(name: "suit_pack")
  final int suitPack;

  ///优惠券名称
  @Json<PERSON>ey(name: "title")
  final String title;

  ///优惠券类型, 1立减券 2满减券 3比例券
  @JsonKey(name: "type")
  final int type;

  ///使用范围, 1会员2皮蛋，暂未使用，保留字段
  @JsonKey(name: "use_range")
  final int useRange;

  UserCoupon({
    required this.couponCode,
    required this.endtime,
    required this.id,
    required this.money,
    required this.moneyLevel,
    required this.suitPack,
    required this.title,
    required this.type,
    required this.useRange,
  });

  UserCoupon copyWith({
    String? couponCode,
    int? endtime,
    int? id,
    int? money,
    int? moneyLevel,
    int? suitPack,
    String? title,
    int? type,
    int? useRange,
  }) =>
      UserCoupon(
        couponCode: couponCode ?? this.couponCode,
        endtime: endtime ?? this.endtime,
        id: id ?? this.id,
        money: money ?? this.money,
        moneyLevel: moneyLevel ?? this.moneyLevel,
        suitPack: suitPack ?? this.suitPack,
        title: title ?? this.title,
        type: type ?? this.type,
        useRange: useRange ?? this.useRange,
      );

  factory UserCoupon.fromJson(Map<String, dynamic> json) =>
      _$UserCouponFromJson(json);

  Map<String, dynamic> toJson() => _$UserCouponToJson(this);
}
