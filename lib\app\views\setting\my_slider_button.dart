import 'package:flutter/material.dart';

class MySliderButton extends StatefulWidget {
  final int? width;
  final int? heigth;
  final double maxValue;
  final String lableText;
  final double current;
  final onTextChange textChange;

  const MySliderButton(
      {Key? key,
      this.width,
      this.heigth,
      this.maxValue = 100,
      required this.lableText,
      required this.current,
      required this.textChange})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyInputButtonState();
}

typedef onTextChange = void Function(double);

class _MyInputButtonState extends State<MySliderButton> {
  double _selectedValue = 20;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.current;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 25, right: 25),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.lableText,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                    ),
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(2.0),
                  child: Container(
                    width: 20,
                    height: 15,
                    color: Color(0xFFA6A6A6),
                    alignment: Alignment.center,
                    child: Text(
                      _selectedValue.toInt().toString(),
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(
            height: 26,
            child: SliderTheme(
              data: SliderThemeData(
                trackHeight: 4,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8),
              ),
              child: Slider(
                value: _selectedValue,
                max: widget.maxValue,
                label: "11",
                activeColor: Color(0xFF12CDD9),
                onChanged: (double value) {
                  setState(() {
                    print(_selectedValue);
                    _selectedValue = value;
                  });
                  widget.textChange(value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
