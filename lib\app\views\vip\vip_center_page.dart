import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:pieces_ai/app/model/vip/vip_ways.dart';
import 'package:pieces_ai/components/top_bar/app_inner_buy/BuyEngine.dart';
import '../../data/vip_features.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../authentication/models/user.dart' as pieces_user;
import 'package:pieces_ai/app/api_https/impl/https_vip_repository.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../../../../authentication/blocs/authentic/bloc.dart';
import '../../../../authentication/blocs/authentic/event.dart';
import 'package:pieces_ai/app/model/vip/order_success_resp.dart';
import 'package:pieces_ai/app/model/vip/vip_order_resp.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));
final BuyEngin _buyEngin = BuyEngin();
late List<VipWays> vipWays = []; // 存储从数据库获取的会员方式数据
int selectedVipWays = 0;
final HttpsVipRepository httpsVipRepository = HttpsVipRepository();
pieces_user.User user = GlobalInfo.instance.user;

class VipCenterPage extends StatefulWidget {
  @override
  _VipCenterPageState createState() => _VipCenterPageState();
}

class _VipCenterPageState extends State<VipCenterPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  bool isLoading = true; // 添加一个布尔变量，用于跟踪加载状态

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    _fetchVipWays(); // 初始化时加载会员方式数据
    _buyEngin.initializeInAppPurchase(); // 初始化内购组件
  }

  @override
  void dispose() {
    // 清理资源
    _tabController.dispose();
    _buyEngin.onClose();
    super.dispose();
  }

  Future<void> _fetchVipWays() async {
    try {
      final vipWaysResponse =
          await httpsVipRepository.getVipWaysInfo(); // 获取会员方式数据
      logger.d('加载会员方式成功: $vipWaysResponse');

      setState(() {
        vipWays = vipWaysResponse; // 更新状态
        isLoading = false; // 数据加载完成后设置为 false
      });
    } catch (e) {
      logger.d('加载会员方式失败: $e');
      setState(() {
        isLoading = false; // 即使加载失败，也需要关闭加载状态
      });
    }
  }

  Future<List<VipWays>> getVipWays() async {
    try {
      // 根据设备类型动态构建查询条件
      final response = await Supabase.instance.client
          .from('t_vip_way')
          .select('*') // 查询所有字段
          .eq('status', 1)
          .eq('way', Platform.isIOS ? 1 : 2) // 根据设备类型设置过滤条件
          .order('sort', ascending: true); // 按照 sort 字段排序

      logger.d('加载会员方式:$response');

      final vipWays = <VipWays>[];
      for (final record in response) {
        vipWays.add(VipWays.fromJson(record));
      }

      return vipWays;
    } catch (e) {
      logger.d('查询会员方式时发生错误: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 数据加载完成后，显示主页面
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xFF61523B),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(0xFFF2DDB2)),
          onPressed: () => Navigator.pop(context),
        ),
        title: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: "Individual"),
            // Tab(text: "Family"),
          ],
          labelColor: Color(0xFFF9E9BA),
          unselectedLabelColor: Color(0xFFF9E9BA),
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(color: Color(0xFFF9E9BA), width: 2),
            insets: EdgeInsets.symmetric(horizontal: 16),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF61523B), Color(0xFF191A1F)],
            stops: [0.0, 0.3],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            IndividualTab(
              vipWays: vipWays,
              selectedVipWays: selectedVipWays,
              onVipWaysSelected: (membership) {
                setState(() {
                  selectedVipWays = membership;
                });
              },
              onPurchasePressed: () => _handlePurchase(context),
            ),
            // Center(child: Text("Family tab content will go here")),
          ],
        ),
      ),
    );
  }

  void _handlePurchase(BuildContext context) async {
    logger.d('【支付流程】开始处理购买请求');
    
    try {
      // 使用新的generateVipOrder方法，返回VipOrderResp对象
      VipOrderResp? orderResp = await httpsVipRepository
          .generateVipOrder(vipWays[selectedVipWays].cpack); // 获取会员方式数据

      if (orderResp == null) {
        logger.e('【支付流程】获取购买订单失败');
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('获取订单信息失败，请重试'),
        ));
        return;
      }

      logger.d('【支付流程】获取购买订单成功:');
      logger.d('【支付流程】- 订单信息(order_info): ${orderResp.orderInfo}');
      logger.d('【支付流程】- 订单包(order_pack): ${orderResp.orderPack}');
      logger.d('【支付流程】所选VIP套餐信息:');
      logger.d('【支付流程】- 套餐ID: ${vipWays[selectedVipWays].id}');
      logger.d('【支付流程】- 套餐名称: ${vipWays[selectedVipWays].title}');
      logger.d('【支付流程】- 套餐描述: ${vipWays[selectedVipWays].desc}');
      logger.d('【支付流程】- 套餐商品ID: ${vipWays[selectedVipWays].iosPay}');
      logger.d('【支付流程】- 套餐价格: ${vipWays[selectedVipWays].price}');
      logger.d('【支付流程】- 套餐原价: ${vipWays[selectedVipWays].originPrice}');
      logger.d('【支付流程】- 套餐包参数: ${vipWays[selectedVipWays].cpack}');

      // 使用新模型传递给buyProduct
      _buyEngin.buyProduct(vipWays[selectedVipWays].iosPay, orderResp,
          (int status, String verData, {bool isAutoRenewing = false, String? gpaOrderId}) async {
        logger.d('【支付流程】收到购买回调，状态: $status, 验证数据长度: ${verData.length}, 是否自动续费: $isAutoRenewing');
        if (gpaOrderId != null && gpaOrderId.isNotEmpty) {
          logger.d('【支付流程】谷歌订单ID: $gpaOrderId');
        }
        
        // 处理回调
        if (status == 0) {
          logger.d('【支付流程】购买成功，准备处理后续逻辑');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('购买成功！'),
          ));
          await _handlePurchaseSuccess(context, orderResp.orderInfo, verData, 
              isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
        } else if (status == -1) {
          logger.e('【支付流程】购买失败');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('购买失败，请重试'),
          ));
        } else if (status == 1) {
          logger.d('【支付流程】支付进行中');
        }
      });
    } catch (e, stackTrace) {
      logger.e('【支付流程】处理购买请求时发生错误: $e');
      logger.e('【支付流程】错误堆栈: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('处理购买请求时发生错误，请稍后重试'),
      ));
    }
  }

  // 处理购买成功后的逻辑
  Future<void> _handlePurchaseSuccess(BuildContext context, String orderResponse, String verData, {bool isAutoRenewing = false, String? gpaOrderId}) async {
    logger.d('【支付流程】开始处理购买成功后的逻辑');
    logger.d('【支付流程】订单号: $orderResponse');
    logger.d('【支付流程】验证数据: ${verData.length > 100 ? verData.substring(0, 100) + "..." : verData}');
    logger.d('【支付流程】商品ID: ${vipWays[selectedVipWays].iosPay}');
    logger.d('【支付流程】是否自动续费: $isAutoRenewing');
    if (gpaOrderId != null && gpaOrderId.isNotEmpty) {
      logger.d('【支付流程】谷歌订单ID: $gpaOrderId');
    }
    
    try {
      logger.d('【支付流程】准备调用服务器验证购买');
      final notifyResponse = await httpsVipRepository.googlePayValidate(
          orderResponse, verData, vipWays[selectedVipWays].iosPay, 
          isAutoRenewing: isAutoRenewing,
          gpaOrderId: gpaOrderId);

      logger.d('【支付流程】购买通知结果：$notifyResponse');

      if (notifyResponse == null) {
        logger.e('【支付流程】购买通知失败，返回数据为空');
        return;
      }

      // 使用 DateFormat 解析日期字符串
      logger.d('【支付流程】解析到期时间字符串: ${notifyResponse.endTime}');
      DateTime parsedDate = DateFormat('y-MM-dd').parse(notifyResponse.endTime);

      logger.d('【支付流程】解析后的到期时间：$parsedDate');

      // 转换为毫秒时间戳
      int vipEndTimestamp = parsedDate.millisecondsSinceEpoch;

      logger.d('【支付流程】VIP到期时间戳：$vipEndTimestamp');

      pieces_user.User user = GlobalInfo.instance.user;
      logger.d('【支付流程】更新前用户VIP信息: ${user.vipEnd}');
      user.vipEnd = vipEndTimestamp;
      GlobalInfo.instance.user = user;
      
      logger.d('【支付流程】用户VIP信息已更新为: ${user.vipEnd}');
      
      BlocProvider.of<AuthBloc>(context).add(Logout(user: user));
      //发出更新用户信息事件
      logger.d('【支付流程】发送用户信息更新事件');
      BlocProvider.of<AuthBloc>(context).add(
        UpdateAuthInfo(user: GlobalInfo.instance.user),
      );

      logger.d('【支付流程】购买成功处理完成');
      // 返回上一页
      Navigator.pop(context);
    } catch (e, stackTrace) {
      logger.e('【支付流程】处理购买成功时发生错误: $e');
      logger.e('【支付流程】错误堆栈: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('处理购买结果时发生错误，请稍后重试'),
      ));
    }
  }
}

class IndividualTab extends StatelessWidget {
  final List<VipWays> vipWays;
  final int selectedVipWays;
  final Function(int) onVipWaysSelected;
  final VoidCallback onPurchasePressed;

  const IndividualTab({
    Key? key,
    required this.vipWays,
    required this.selectedVipWays,
    required this.onVipWaysSelected,
    required this.onPurchasePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // VIP特性列表
    List<VipFeature> vipFeatures =
        getVipFeatures(context); // 调用外部方法获取 VIP 特性列表;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16),
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundImage: NetworkImage(user.headIcon), // 替换为实际头像URL
                backgroundColor: Colors.grey,
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      user.name,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      "Buy membership for more privileges",
                      style: TextStyle(
                        color: Color(0xFFBDBDBD),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            "Members Privilege",
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: vipFeatures.map((feature) {
              return FeatureItem(
                imagePath: feature.imagePathYelloew,
                text: feature.text,
              );
            }).toList(),
          ),
          SizedBox(height: 16),
          Text(
            "Get VIP Membership",
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          SizedBox(
            height: 150,
            child: vipWays.isEmpty
                ? Center(
                    child: CircularProgressIndicator()) // 如果 vipWays 为空，显示加载指示器
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: vipWays.length,
                    itemBuilder: (context, index) {
                      VipWays vipWayModel = vipWays[index];
                      bool isSelected = index == selectedVipWays;
                      return MembershipCard(
                        data: vipWayModel,
                        isSelected: isSelected,
                        onTap: () => onVipWaysSelected(index),
                      );
                    },
                  ),
          ),
          SizedBox(height: 8),
          Text(
            "* Auto-renewing, cancel anytime",
            style: TextStyle(
              color: Color(0xFFBDBDBD),
              fontSize: 13,
            ),
          ),
          SizedBox(height: 16),
          PurchaseButton(
            price: vipWays.isNotEmpty
                ? vipWays[selectedVipWays].getPriceInDollars()
                : '',
            onPressed: onPurchasePressed,
          ),
        ],
      ),
    );
  }
}

class FeatureItem extends StatelessWidget {
  final String imagePath;
  final String text;

  const FeatureItem({Key? key, required this.imagePath, required this.text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width / 2 - 32, // 自动适应两列布局
      child: Row(
        children: [
          Image.asset(
            imagePath,
            width: 30,
          ),
          SizedBox(width: 8),
          Expanded(
            // 使用 Expanded 包裹 Text，确保文字超出时显示省略号
            child: Text(
              text,
              style: TextStyle(
                color: Color(0xFFE9E1B6),
                fontSize: 13,
              ),
              overflow: TextOverflow.ellipsis, // 超出显示省略号
              maxLines: 2, // 限制为单行
            ),
          ),
        ],
      ),
    );
  }
}

class MembershipCard extends StatelessWidget {
  final VipWays data;
  final bool isSelected;
  final VoidCallback onTap;

  const MembershipCard({
    Key? key,
    required this.data,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        margin: EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: isSelected
              ? LinearGradient(
                  colors: [Color(0xFFFFDDA3), Color(0xFFFECA76)],
                )
              : null,
          border: isSelected ? null : Border.all(color: Colors.white38),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center, // 修改为 center
            children: [
              Text(
                data.title,
                textAlign: TextAlign.center, // 确保文本也居中
                style: TextStyle(
                  color: isSelected ? Color(0xFF642D14) : Color(0xFFBDBDBD),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "\$",
                      style: TextStyle(
                        fontSize: 12,
                        color:
                            isSelected ? Color(0xFF642D14) : Color(0xFFFEE0A9),
                      ),
                    ),
                    TextSpan(
                      text: "${data.getPriceInDollars()}",
                      style: TextStyle(
                          fontSize: 30,
                          color: isSelected
                              ? Color(0xFF642D14)
                              : Color(0xFFFEE0A9),
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                textAlign: TextAlign.center, // 确保文本也居中
              ),
              Text(
                "\$${data.getOriginPriceInDollars()}",
                textAlign: TextAlign.center, // 确保文本也居中
                style: TextStyle(
                  decoration: TextDecoration.lineThrough,
                  color: isSelected ? Color(0xFF642D14) : Color(0xFFBDBDBD),
                ),
              ),
              Text(
                "(Save \$${data.getSavePriceInDollars()})",
                textAlign: TextAlign.center, // 确保文本也居中
                style: TextStyle(
                  color: isSelected ? Color(0xFF642D14) : Color(0xFFBDBDBD),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PurchaseButton extends StatelessWidget {
  final String price;
  final VoidCallback onPressed;

  const PurchaseButton({
    Key? key, 
    required this.price,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(9),
        ),
        side: BorderSide.none,
        shadowColor: Colors.transparent,
        padding: EdgeInsets.symmetric(vertical: 12),
        backgroundColor: Colors.transparent,
      ),
      child: Ink(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFFEDEA5), Color(0xFFFFC871)],
          ),
          borderRadius: BorderRadius.circular(9),
        ),
        child: Container(
          alignment: Alignment.center,
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 12),
          child: Text(
            "Purchase Membership for \$${price}",
            style: TextStyle(
              color: Color(0xFF642D14),
              fontSize: 17,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
