import 'package:flutter/material.dart';
import '../../../../utils/analytics_helper.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';

class SearchPage extends StatefulWidget {
  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  @override
  void initState() {
    super.initState();
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('search');
  }

  // 添加搜索埋点
  void _trackSearch(String keyword) {
    AnalyticsHelper().trackSearch(keyword, 'video');
  }

  Widget _buildVideoCard(VideoRespVo video) {
    return InkWell(
      onTap: () {
        AnalyticsHelper().trackViewItem('video', video.id.toString());
        Navigator.of(context).pushNamed(UnitRouter.video_detail, arguments: video);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                video.coverImage ?? '',
                width: 150,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, exception, stackTrace) {
                  return Image.asset(
                    'assets/images/widgets/draft_empty.png',
                    width: 150,
                    height: 100,
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    video.name ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    video.type ?? '',
                    style: TextStyle(color: Color(0xFFBDBDBD)),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          onSubmitted: (value) {
            _trackSearch(value);
            // ... existing search code ...
          },
        ),
      ),
      body: ListView.builder(
        itemCount: searchResults.length,
        itemBuilder: (context, index) {
          final video = searchResults[index];
          return _buildVideoCard(video);
        },
      ),
    );
  }
} 