{"create": "Home", "goods": "Categories", "me": "Me", "@gender": {"male": "Hi man", "female": "Hello girl", "other": "Hello"}, "agree": "agree", "disagree": "disagree", "mainOneClick": "One-click to create", "drafts": "Drafts", "noDrafts": "No drafts, click to create", "oneClickMode": "One-click mode", "title": "Title", "submit": "Submit", "haveDone": "<PERSON>lick to view", "original": "Original", "unDone": "Not done", "generating": "Generating...", "failed": "Failed", "rename": "<PERSON><PERSON>", "delete": "Delete", "export": "Export", "reMake": "Re-make", "earnPoints": "<PERSON><PERSON><PERSON>", "reDownload": "Re-download", "taskClaim": "Task claim", "resDownload": "Res download", "notLogged": "Not logged in", "vipExpired": "Membership expired", "myRole": "My role", "allCategory": "All categories", "videoExport": "Video export", "syntheticSubtitles": "Synthetic subtitles", "noSubtitles": "The video synthesized after cancellation has no subtitles", "exPortVideo": "Export video", "exportingInfo": "Exporting... Please wait", "synthesisProgress": "Video synthesis in progress...", "storyboard": "Storyboard", "exportSuccessfulAndSave": "Export successful, saved to album", "basicSettings": "Basic settings", "next": "Next", "aiWrite": "AI write", "uploadAudio": "Upload Audio", "enterText": "Enter Text", "aiEnterExample": "Enter example: Children's Day", "selectStyle": "Select style", "numStoryboards": "Num storyboards", "aiWriteTips": "Note: Ai automatically generates copy and prompts, deducting 2 points for each storyboard", "nowDeductionPoints": "Current deduction points:", "ttsRoles": "TTS roles", "moreSettings": "More settings", "screenRatio": "Screen ratio", "pleaseEnterText": "Please enter a article", "nOperationIsRequired": "No operation is required for re-production", "speed": "Speed", "fixedCharacter": "Fixed character", "globalSettings": "Global settings", "repOriText": "Replace text", "subImgTask": "Submit image task", "subVideoTask": "Submit video task", "someUnSaved": "Some changes un saved", "clickToSaveAndExit": "<PERSON>lick OK to save the draft and exit", "ok": "OK", "cancel": "Cancel", "confirm": "Confirm", "english": "English", "spanish": "Spanish", "japanese": "Japanese", "appSettings": "App settings", "settings": "Settings", "tutorial": "Tutorial", "about": "About", "privacyPolicy": "Privacy policy", "userAgreement": "User agreement", "pleaseRead": "Please read carefully before using and confirm that you agree to the above terms.", "welcomeToMovieNest": "Welcome to MovieNest, in order to better protect your privacy and personal information security, we have formulated", "versionInfo": "Version Info", "checkForUpdates": "Check for updates", "clickToDownloadNewVersion": "Click to download the new version", "updateContent": "Update content", "newVersionDownloading": "New version downloading", "logOut": "Log out", "logOutInfo": "Are you sure you want to log out?", "exit": "Exit", "logoutSuccessful": "Logout successful", "logoutFailed": "Logout failed", "logoutFailedInfo": "<PERSON><PERSON><PERSON> failed, please try again later", "sighInSuccessful": "Sign in successful", "cacheClean": "Cache cleaning", "clickClean": "Click", "thirdDataList": "Third-party data list", "personalInfoList": "Personal information list", "accountCancellation": "Account cancellation", "accountCancellationInfo": "Are you sure you want to cancel your account?", "accountCancellationInfo1": "your account will be permanently deleted", "aiGenerating": "AI generating...", "setToStoryBoard": "Set to storyboard", "imitation": "Imitation", "light": "Light", "pov": "POV", "promptDic": "Prompt dict", "num": "<PERSON><PERSON>", "style": "Style", "advanceSetting": "Advanced Setting", "image": "Image", "video": "Video", "tts": "Dubbing", "motion": "Motion", "preview": "Preview", "webDetails": "Web details", "invalidEmail": "Invalid email address", "userHasDisabled": "User has been disabled", "userDoesNotExit": "User does not exist", "passError": "Password error", "loginFailed": "<PERSON><PERSON> failed", "emailAlreadyInUse": "Email already in use", "unknownOccurred": "An unknown error occurred", "operationNotAllowed": "Operation not allowed", "weakPassword": "Password is too weak", "registrationFailed": "Registration failed", "submitSuccess": "Submit successfully", "generated": "(Generated)", "notGenerated": "(Not generated)", "lockSeed": "Lock seed", "randomSeed": "Random seed", "setSeed": "Set seed", "deleteDraft": "Delete draft", "areYouSure": "Are you sure?", "generateImage": "Generate image", "unrecognizedRole": "Unrecognized role", "unrecognizedScene": "Unrecognized scene", "selectStartImageToVideo": "Select the first image", "selectEndImageToVideo": "Select the last image", "pleaseEnterTheMotionGuidePrompt": "Please enter the motion guide prompt words for generating the video, if not filled in, it will be automatically generated according to the picture", "otherSetting": "Other settings", "highQualityModeTakesTwiceTime": "(High-quality mode takes twice time, please be patient)", "mode": "Mode", "fastMode": "Fast mode", "highQualityMode": "High-quality mode", "resolution": "Resolution", "sd": "SD", "hd": "HD", "play": "Play", "startDubbing": "Start dubbing", "save": "Save", "dubbingFailed": "Dubbing failed", "noDubbingYet": "No dubbing yet", "textCannotBeEmpty": "Text cannot be empty", "generalWaitInfo": "During peak hours, it takes about 1 minute to generate a picture quickly, and about 5~10 minutes to generate a picture normally. The speed is slightly faster during off-peak hours. Please be patient.\\n\\nDo not exit during the generation of the picture. The recent average waiting time is 3 minutes and 16 seconds.", "downloadSuccessAndSave": "Download successful, saved to album", "submitTask": "Submit task", "imageQuality": "Image quality", "excellentCase": "Excellent case", "allMaterialsGeneratedByMovieNest": "All materials are generated by MovieNest", "theDefaultIsHDInfo": "The default is HD ordinary high-definition. FHD deducts 4 times the points, and QHD deducts 6 times the points. The higher the quality, the richer the picture details", "aiPrompts": "Ai prompts", "aiPromptsInfos": "After checking, Ai reasoning prompts will be added on the basis of the original prompts", "reDubbing": "Re-dubbing", "reDubbingInfos": "After checking, TTS dubbing will be re-generated", "effective": "Effective range", "allStoryboards": "All", "videoStoryboard": "Video storyboard", "dynamicVideo": "Dynamic video", "dynamicVideoInfo": "After checking, each storyboard will generate a dynamic video", "taskLimit": "The concurrent task limit has been reached. Maximum 3", "membershipRenewal": "Membership renewal", "iHaveReadAndAgree": "I have read and agree", "piecesAnimaMembershipAgreement": "《Membership Purchase Agreement》", "payNow": "Pay now", "pleaseReadAndAgreeMembership": "Please read and agree to the membership purchase agreement first", "pleaseSelectTypeOfMembership": "Please select the type of membership you want to purchase.", "gettingPaymentInfo": "Getting payment information...", "paySuccess": "Payment successful", "payFailed": "Payment failed", "membershipExpired": "Membership expired", "pleaseEnterTheme": "Please enter a theme", "pleaseWaitForStyleLoaded": "Please wait for the style to load", "myPoints": "My points", "claim": "<PERSON><PERSON><PERSON>", "claimed": "Claimed", "checkToReceiveVip": "Check in To Receive Membership", "progress": "Progress", "playRecord": "Play Record", "updateReminders": "Movie/TV Update Reminders", "myDownload": "My Download", "done": "Done", "edit": "Edit", "collection": "Collection", "share": "Share", "vipCardTitle": "Limited Time Discount", "vipCardDescription": "Get a member and enjoy exclusive privileges", "vipCardFunction1": "Remove all  Ads", "vipCardFunction2": "GUnlock Movies", "vipCardFunction3": "Unlimited Downloads", "vipCardFunction4": "HD Resources", "vipCardFunction5": "Unlock Cast", "vipCardButton": "Get", "categoriesTitle": "CATEGORIES", "categoriesSearchDesc": "Searcch Movies, Genres etc", "deleteSuccess": "Delete Success", "deleteFailed": "Days Failed", "deleting": "Deleting", "deleteSelected": "DeleteSelected"}