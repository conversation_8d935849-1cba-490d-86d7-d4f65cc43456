import 'package:flutter/material.dart';
import 'package:widget_repository/widget_repository.dart';

import '../res/toly_icon.dart';


class Cons {

  static const menuInfo = <String>["关于", "帮助", "问题反馈"]; //菜单栏

  static const List<Color> tabColors = [
    Color(0xff44D1FD),
    Color(0xffFD4F43),
    Color(0xffB375FF),
    Color(0xFF4CAF50),
    Color(0xFFFF9800),
    Color(0xFF00F1F1),
    Color(0xFFDBD83F),
  ];

  static const tabs = <String>[
    'Stles',
    'Stful',
    'Scrow',
    'Mcrow',
    'Sliver',
    'Proxy',
    'Other'
  ]; //标题列表

  static const iconMap = {
    //底栏图标
    "组件集录": TolyIcon.icon_layout, "收藏集录": TolyIcon.icon_star,
    "绘制集录": Icons.palette, "布局集录": Icons.widgets,
    "要点集录": TolyIcon.icon_bug,
  };

  static const List<String> kFontFamilySupport = [
    'local',
    'ComicNeue',
    'IndieFlower',
    'BalooBhai2',
    'Inconsolata',
    'Neucha'
  ];

  static const Map<WidgetFamily,String> kWidgetFamilyLabelMap = {
    WidgetFamily.statelessWidget: "Stateless",
    WidgetFamily.statefulWidget: "Stateful",
    WidgetFamily.singleChildRenderObjectWidget: "SingleChild",
    WidgetFamily.multiChildRenderObjectWidget: "MultiChild",
    WidgetFamily.sliver: "Sliver",
    WidgetFamily.proxyWidget: "Proxy",
    WidgetFamily.other: "Other",
  };



  static final kThemeColorSupport = <MaterialColor, String>{
    Colors.red: "毁灭之红",
    Colors.orange: "愤怒之橙",
    Colors.yellow: "警告之黄",
    Colors.green: "伪装之绿",
    Colors.blue: "冷漠之蓝",
    Colors.indigo: "无限之靛",
    Colors.purple: "神秘之紫",

    const MaterialColor(0xff2D2D2D, <int, Color>{
      50: Color(0xFF8A8A8A),
      100: Color(0xFF747474),
      200: Color(0xFF616161),
      300: Color(0xFF484848),
      400: Color(0xFF3D3D3D),
      500: Color(0xff2D2D2D),
      600: Color(0xFF252525),
      700: Color(0xFF141414),
      800: Color(0xFF050505),
      900: Color(0xff000000),
    }): "归宿之黑"
  };

}

