{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/android_sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/android_sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/android_sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/android_sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-340b142442b4cfe7fb45.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-11333796ffe0ba841805.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-42009f54e937d5266549.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-11333796ffe0ba841805.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-42009f54e937d5266549.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-340b142442b4cfe7fb45.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}