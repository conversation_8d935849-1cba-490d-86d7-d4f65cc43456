import 'dart:math';

const String judgeContentPrompt = ".判断上述小说文案叙事方式是一人称还是二人称。"
    "如果是一人称则返回数字1，如果是二人称则返回数字2."
    "不返回数字以外的其他任何内容";

const String writeTitlePromptSecond01 =
    ".根据上述小说内容设计一个开头文案，尽量多使用一些口语化或者互联网玩梗的词语和用语，"
    "尽量用上下面这些词语：却、竟、不仅、而且、就连、甚至、然而、不仅不，反而，只因。"
    "整个开头文案在200字左右。我给你一个示例可模仿示例来设计这个开头文案，"
    "示例为：你相貌面善，却没人敢靠近你分毫，一个眼神便能把小区里最凶恶的狗，吓的当场大小便失禁，蚊子臭虫都不敢靠近你身边3米之内，而你虽";

const String writeTitlePromptSecond02 =
    ".根据上述小说内容设计一个开头文案，尽量多使用一些口语化或者互联网玩梗的词语和用语，"
    "尽量用上下面这些词语：却、竟、不仅、而且、就连、甚至、然而、不仅不，反而，只因。"
    "整个开头文案在200字左右。我给你一个示例可模仿示例来设计这个开头文案，"
    "示例为：你高考280，却被国科大特邀入学，可四年大学你却交了四年白卷，成为了全校师生眼中的顶级学渣，而他们不知道的是，你用4年时间读完了学校图书馆里的所有书，并且每本都能做到倒背如流，只因这是系统留给你的任务，四年的时间...";

const String writeTitlePromptFirst01 =
    ".根据上述小说内容设计一个开头文案，尽量多使用一些口语化或者互联网玩梗的词语和用语，"
    "尽量用上下面这些词语：却、竟、不仅、而且、就连、甚至、然而、不仅不，反而，只因。"
    "整个开头文案按下面逻辑来设计："
    "第一步:设定一个人定位或者一件事，例如：你是一个XXX"
    "第二步:和第一步中的设定反过来的事情。例如：却被XXX"
    "第三步:再详细叙述具体反转做了什么事。例如：就连XXX"
    "第四步:再从第三步的描述再次递进强调。例如：甚至连XXX"
    "第五步:使用'然后'铺垫反转。"
    "第六步:再次使用反转。例如：我却XXX"
    "第七步:讲原因。例如：只因XXX"
    "整个开头文案在200字左右。我给你一个示例可模仿示例来设计这个开头文案，"
    "示例为：我一个重度精神病，却被全国人奉为神明，就连我说我是派大星他们都深信不疑，还要陪我一起去海里抓水母，只因我...";

const String writeTitlePromptFirst02 =
    ".根据上述小说内容设计一个开头文案，尽量多使用一些口语化或者互联网玩梗的词语和用语，"
    "尽量用上下面这些词语：却、竟、不仅、而且、就连、甚至、然而、不仅不，反而，只因。"
    "整个开头文案在200字左右。我给你一个示例可模仿示例来设计这个开头文案，"
    "示例为：我身为奥特曼，却从不打怪兽，反而一心想要毁灭全人类，只因这个世界迪迦奥特曼因暴力被举报下架，全网家长顿时一片欢呼，然而却...";

const String writeTitlePromptFirst03 =
    ".根据上述小说内容设计一个开头文案，尽量多使用一些口语化或者互联网玩梗的词语和用语，"
    "尽量用上下面这些词语：却、竟、不仅、而且、就连、甚至、然而、不仅不，反而，只因。"
    "整个开头文案在200字左右。我给你一个示例可模仿示例来设计这个开头文案，"
    "示例为：平时连蚂蚁都不敢踩的我，却在一夜之间宰了十万头牛，就在所有人以为我会拿肉卖钱跑路时，我却选择直接把肉烂在自家仓库，村里都骂我是个疯子，说父母刚死我就败光家产，就连初恋女友也以为我是为了凑彩礼钱，随即让我死了这条心，就算宰光家里的牛也凑不够五千万...";

class AiArticleTemplate {
  ///获取第一人称随机模板
  static String getRandomTemplateFirst() {
    List<String> templateList = [
      writeTitlePromptFirst01,
      writeTitlePromptFirst02,
      writeTitlePromptFirst03
    ];
    Random random = Random();
    int index = random.nextInt(templateList.length);
    return templateList[index];
  }

  static String getRandomTemplateYou() {
    List<String> templateList = [
      writeTitlePromptSecond01,
      writeTitlePromptSecond02,
    ];
    Random random = Random();
    int index = random.nextInt(templateList.length);
    return templateList[index];
  }
}
