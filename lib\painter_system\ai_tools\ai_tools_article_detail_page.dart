import 'package:app/app/router/slide_page_route.dart';
import 'package:flutter/material.dart';
import 'package:motion_toast/motion_toast.dart';
import 'package:pieces_ai/app/navigation/mobile/theme/theme.dart';
import 'package:pieces_ai/painter_system/gallery_card_item.dart';

import '../../app/model/user_info_global.dart';
import '../../authentication/models/user.dart';
import '../../components/top_bar/vip_buy_page.dart';
import '../../widget_ui/desk_ui/category_panel/new_draft_dialog.dart';

/// create by blueming.wu
/// 改文详情页
class ReviseArticleDetailPage extends StatefulWidget {
  final GalleryInfo galleryInfo;

  const ReviseArticleDetailPage({Key? key, required this.galleryInfo})
      : super(key: key);

  @override
  _ReviseArticleDetailPageState createState() =>
      _ReviseArticleDetailPageState();
}

class _ReviseArticleDetailPageState extends State<ReviseArticleDetailPage> {
  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _textEditingReviseController =
      TextEditingController();
  final ValueNotifier<int> _currentIndex = ValueNotifier<int>(0);
  int pegg = 0;
  int type = 1; //默认1为3000字，2为8000字

  @override
  void initState() {
    User user = GlobalInfo.instance.user;
    type = (user.vipLevel == 4 || user.vipLevel == 5) ? 2 : 1;
    super.initState();
  }

  @override
  void dispose() {
    _currentIndex.dispose();
    _textEditingController.dispose();
    _focusNode.dispose();
    _textEditingReviseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColor.piecesBackTwo,
      ),
      body: Column(
        children: [
          Flexible(
            child: _buildTextField(),
            flex: 1,
          ),
          Flexible(
            child: _buildTextFieldRevise(),
            flex: 1,
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 25),
                  child: ElevatedButton(
                    child: const Text(
                      '生成片头',
                      style: TextStyle(fontSize: 14),
                    ),
                    onPressed: _onPressedTitle,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 25),
                  child: ElevatedButton(
                    child: const Text(
                      '全文洗稿',
                      style: TextStyle(fontSize: 14),
                    ),
                    onPressed: _onPressed,
                    // style: ElevatedButton.styleFrom(
                    //   backgroundColor: Color(0xFF12CDD9),
                    //   shape: RoundedRectangleBorder(
                    //     borderRadius: BorderRadius.circular(10), // 设置圆角半径为5像素
                    //   ),
                    // ),
                  ),
                ),
                ElevatedButton(
                  child: const Text('去制作', style: TextStyle(fontSize: 14)),
                  onPressed: _onMakeAnime,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onMakeAnime() {
    if (widget.galleryInfo.vip != 0) {
      User user = GlobalInfo().user;
      if (user.vipLevel == null || user.vipLevel! < 4) {
        Navigator.push(context, SlidePageRoute(child: VipBuyPage(
          doPaySuccess: (success) {
            if (success) {
              setState(() {});
            }
          },
        )));
        return;
      }
    }
    String reviseContent = _textEditingReviseController.text;
    if (reviseContent.isEmpty) {
      MotionToast.warning(description: Text("改写内容不能为空！")).show(context);
      return;
    }
    //先判断是否登录
    User user = GlobalInfo.instance.user;
    if (user.pegg > 0 && user.authToken != null && user.authToken!.isNotEmpty) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return NewDraftDialog(
            type: type,
            originalContent: reviseContent,
          );
        },
      );
    } else {
      MotionToast.warning(description: Text("请先在主页登录！")).show(context);
    }
  }

  void _onPressed() async {
    if (widget.galleryInfo.vip != 0) {
      User user = GlobalInfo().user;
      if (user.vipLevel == null || user.vipLevel! < 4) {
        Navigator.push(context, SlidePageRoute(child: VipBuyPage(
          doPaySuccess: (success) {
            if (success) {
              setState(() {});
            }
          },
        )));
        return;
      }
    }
    //把输入的文本按1000字切割，然后遍历访问，最后合并显示
    String content = _textEditingController.text;
    if (content.isEmpty) {
      MotionToast.warning(description: Text("原文内容不能为空！")).show(context);
      return;
    }
    //先判断是否登录
    User user = GlobalInfo.instance.user;
    if (user.pegg > 0 && user.authToken != null && user.authToken!.isNotEmpty) {
    } else {
      MotionToast.warning(description: Text("请先在主页登录！")).show(context);
      return;
    }

    int peggConsume = type == 1 ? pegg * 4 : pegg;
    if (user.pegg < peggConsume) {
      MotionToast.warning(description: Text("皮蛋不足！")).show(context);
      return;
    }
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                    Color(0xFF12CDD9)), // 设置CircularProgressIndicator颜色
              ),
              SizedBox(height: 20),
              Text(
                '文章正在改写中...', // 添加文字
                style: TextStyle(
                  color: Color(0xFF12CDD9), // 设置文字颜色
                  fontSize: 16,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        );
      },
    );

    List<String> splitContent = splitString(content, 1000);
    List<Future<String>> futures = [];
    splitContent.forEach((element) {
      // futures.add(httpAiStoryRepository.aiReviseArticle(
      //     sentence: element, pegg: type == 1 ? 16 : 4));
    });

    try {
      // Wait for all async operations to complete
      List<String> results = await Future.wait(futures);
      String newContent = results.join('');

      // Update text field with processed content
      setState(() {
        _textEditingReviseController.text = newContent;
      });
    } catch (e) {
      print("改写文案出错  Error occurred: $e");
    } finally {
      // Dismiss loading dialog
      Navigator.of(context).pop();
    }
  }

  ///为文章增加片头
  void _onPressedTitle() async {
    if (widget.galleryInfo.vip != 0) {
      User user = GlobalInfo().user;
      if (user.vipLevel == null || user.vipLevel! < 4) {
        Navigator.push(context, SlidePageRoute(child: VipBuyPage(
          doPaySuccess: (success) {
            if (success) {
              setState(() {});
            }
          },
        )));
        return;
      }
    }
    //把输入的文本按1000字切割，然后遍历访问，最后合并显示
    String content = _textEditingController.text;
    if (content.isEmpty) {
      MotionToast.warning(description: Text("原文内容不能为空！")).show(context);
      return;
    }
    //先判断是否登录
    User user = GlobalInfo.instance.user;
    if (user.pegg > 0 && user.authToken != null && user.authToken!.isNotEmpty) {
    } else {
      MotionToast.warning(description: Text("请先在主页登录！")).show(context);
      return;
    }

    int peggConsume = type == 1 ? pegg * 4 : pegg;
    if (user.pegg < peggConsume) {
      MotionToast.warning(description: Text("皮蛋不足！")).show(context);
      return;
    }
    //取消_focusNode焦点
    _focusNode.unfocus();
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                    Color(0xFF12CDD9)), // 设置CircularProgressIndicator颜色
              ),
              SizedBox(height: 20),
              Text(
                'Ai正在撰写中...', // 添加文字
                style: TextStyle(
                  color: Color(0xFF12CDD9), // 设置文字颜色
                  fontSize: 16,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        );
      },
    );

    int contentTypeInt = 1;
    int countMe = RegExp("我").allMatches(content).length;
    int countYou = RegExp("你").allMatches(content).length;
    if (countYou > countMe) contentTypeInt = 2;
    debugPrint(
        "文案是一人称还是二人称countMe: $countMe   你：$countYou  几人称：$contentTypeInt");
    // try{
    //   //先判断是几人称
    //   String contentType = await httpAiStoryRepository.judgeContentType(
    //       sentence: content, pegg: type == 1 ? 16 : 4);
    //   debugPrint("文案是一人称还是二人称: $contentType");
    //   contentTypeInt = int.parse(contentType);
    // }catch (e) {
    //   debugPrint("文案是一人称还是二人称出错: $e");
    //   contentTypeInt = 1;
    //   ///后续Ai判断出错可以判断文案中出现我和你的次数哪个多
    // }

    // try {
    //   String title = await httpAiStoryRepository.aiWriteTitle(
    //       sentence: content, pegg: type == 1 ? 16 : 4, type: contentTypeInt);
    //   setState(() {
    //     _textEditingReviseController.text = title + "---正文开始\n\n" + content;
    //   });
    // } catch (e) {
    //   debugPrint("改写文案出错  Error occurred: $e");
    // } finally {
    //   // Dismiss loading dialog
    //   Navigator.of(context).pop();
    // }
  }

  List<String> splitString(String input, int chunkSize) {
    List<String> chunks = [];
    for (int i = 0; i < input.length; i += chunkSize) {
      if (i + chunkSize >= input.length) {
        chunks.add(input.substring(i));
      } else {
        chunks.add(input.substring(i, i + chunkSize));
      }
    }
    return chunks;
  }

  Widget _buildTextField() {
    User userLocal = GlobalInfo.instance.user;
    int maxLength =
        (userLocal.vipLevel == 4 || userLocal.vipLevel == 5) ? 8000 : 3000;
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Color(0x33FFFFF), width: 1),
        color: Color(0x19FFFFFF), // 设置背景颜色
      ),
      child: Padding(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
                padding: EdgeInsets.only(bottom: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 10),
                      child: Text(
                        "消耗皮蛋数：${type == 1 ? pegg * 4 : pegg}",
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                    Tooltip(
                      message: '改写1000字消耗16个皮蛋(vip4个)\n不满1000字按1000字计算',
                      child: Icon(
                        Icons.help,
                        size: 16,
                      ),
                    )
                  ],
                )),
            Expanded(
              child: TextField(
                controller: _textEditingController,
                focusNode: _focusNode,
                // 绑定 TextEditingController
                style: const TextStyle(color: Colors.white, fontSize: 12),
                maxLines: 50,
                maxLength: maxLength,
                cursorColor: Colors.green,
                cursorRadius: const Radius.circular(3),
                cursorWidth: 5,
                showCursor: true,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.all(10),
                  hintText: "请输入原文内容...",
                  border: OutlineInputBorder(),
                ),
                onChanged: (v) {
                  //计算皮蛋数
                  var length = v.characters.length;
                  setState(() {
                    pegg = ((length ~/ 1000) + 1) * 4;
                  });
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TextButton(
                    onPressed: () {
                      setState(() {
                        _textEditingController.clear();
                      });
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.clear,
                          color: Colors.white,
                          size: 14,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(
                          "清空",
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    )),
                const Spacer(),
                Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0), // 设置圆角半径
                        side: BorderSide(
                            color: type == 1 ? Color(0xFF12CDD9) : Colors.grey,
                            width: 1),
                      ),
                      padding: EdgeInsets.zero,
                      //设置按钮内边距
                      minimumSize: Size(70, 30),
                      // tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      backgroundColor: Colors.black,
                    ),
                    onPressed: () {
                      if (type == 2) {
                        MotionToast.info(description: Text("您已是年卡！"))
                            .show(context);
                        return;
                      }
                      setState(() {
                        type = 1;
                      });
                    },
                    child: Text(
                      "3000字内",
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0), // 设置圆角半径
                        side: BorderSide(
                            color: type == 2 ? Color(0xFF12CDD9) : Colors.grey,
                            width: 1),
                      ),
                      padding: EdgeInsets.zero,
                      //设置按钮内边距
                      minimumSize: Size(70, 30),
                      backgroundColor: Colors.black,
                    ),
                    onPressed: () {
                      //看是否是年卡，不是则提示
                      if (type == 1) {
                        MotionToast.info(description: Text("年卡权限支持8000字"))
                            .show(context);
                        return;
                      }
                      setState(() {
                        type = 2;
                      });
                    },
                    child: Text(
                      "8000字内",
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
        padding: EdgeInsets.all(15),
      ),
    );
  }

  Widget _buildTextFieldRevise() {
    return Padding(
      padding: EdgeInsets.only(top: 10),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(color: Color(0x33FFFFF), width: 1),
          color: Color(0x19FFFFFF), // 设置背景颜色
        ),
        child: Padding(
          child: TextField(
            controller: _textEditingReviseController,
            // 绑定 TextEditingController
            style: const TextStyle(color: Colors.white, fontSize: 12),
            maxLines: 50,
            maxLength: 10000,
            cursorColor: Colors.green,
            cursorRadius: const Radius.circular(3),
            cursorWidth: 5,
            showCursor: true,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.all(10),
              hintText: "改写后的内容...",
              border: OutlineInputBorder(),
            ),
            onChanged: (v) {},
          ),
          padding: EdgeInsets.all(15),
        ),
      ),
    );
  }
}
