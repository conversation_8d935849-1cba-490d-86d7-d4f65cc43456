import 'package:app/app/router/slide_page_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/components/top_bar/vip_buy_page.dart';

import '../../app/navigation/desk_ui/desk_mission_panel.dart';
import '../../authentication/models/user.dart';

var logger = Logger(printer: PrettyPrinter());

GlobalKey<_DeskTabTopBarState> deskTopBarKey = GlobalKey();

class DeskTabTopBar extends StatefulWidget {
  //是否跳转到登录页面的callback
  final Function(bool) doLogin;

  const DeskTabTopBar({
    Key? key,
    required this.doLogin,
  }) : super(key: key);

  @override
  State<DeskTabTopBar> createState() => _DeskTabTopBarState();
}

class _DeskTabTopBarState extends State<DeskTabTopBar>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant DeskTabTopBar oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    User user = GlobalInfo.instance.user;
    return _buildUserInfoWidget(user);
  }

  Widget _buildUserInfoWidget(User user) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        // 在这里执行点击事件的操作
        //看是否已经登录
        if (user.pegg > 0 &&
            user.authToken != null &&
            user.authToken!.isNotEmpty) {
        } else {
          widget.doLogin(true);
        }
      },
      child: Row(
        children: [
          Container(
            width: 60,
            height: 28,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.yellow, Colors.red],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(14.0), // 设置圆角
            ),
            child: ElevatedButton(
              onPressed: () async {
                print(user.pegg.toString());
                //看是否已经登录
                if (user.authToken != null && user.authToken!.isNotEmpty) {
                } else {
                  widget.doLogin(true);
                  return;
                }
                _showMissionDialog();
              },
              child: Text(
                textAlign: TextAlign.center,
                AppLocalizations.of(context).earnPoints,
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                padding: EdgeInsets.zero,
              ),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Ink(
            width: 60,
            height: double.infinity,
            child: InkWell(
              onTap: () async {
                print(user.pegg.toString());
                //看是否已经登录
                if (user.authToken != null && user.authToken!.isNotEmpty) {
                } else {
                  widget.doLogin(true);
                  return;
                }
                Navigator.push(context, SlidePageRoute(child: VipBuyPage(
                  doPaySuccess: (success) {
                    logger.d("首页接收到是否支付成功消息：" + success.toString());
                    if (success) {
                      setState(() {});
                    }
                  },
                )));
              },
              //使用assets/images/logo/vip.png图标显示
              // child: Text(
              //   textAlign: TextAlign.center,
              //   user.vipLevel == 1 ? "会员充值" : "续费",
              //   style: TextStyle(color: Colors.white, fontSize: 12),
              // ),
              child: Image.asset(
                'assets/images/logo/vip.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }

  //领取皮蛋任务
  _showMissionDialog() {
    var user = GlobalInfo.instance.user;
    // if (user.vipLevel == 4 || user.vipLevel == 5) {
    // } else {
    //   Navigator.push(context, SlidePageRoute(child: VipBuyPage(
    //     doPaySuccess: (success) {
    //       logger.d("首页接收到是否支付成功消息：" + success.toString());
    //       if (success) {
    //         setState(() {});
    //       }
    //     },
    //   )));
    //   return;
    // }
    //使用页面方式打开
    Navigator.push(
        context, MaterialPageRoute(builder: (context) => DeskMissionPanel()));
  }

  List<PopupMenuItem<String>> buildItems(User user) {
    List<PopupMenuItem<String>> items = [];
    String vipEndStr = user.vipEnd == null
        ? ""
        : DateFormat('y-MM-dd')
            .format(DateTime.fromMillisecondsSinceEpoch(user.vipEnd!))
            .toString();
    List<String> values = [
      "UID：" + user.userId.toString(),
      "会员到期：" + vipEndStr,
      "皮蛋数：" + user.pegg.toString()
    ];
    PopupMenuItem<String> userName = PopupMenuItem<String>(
      value: "用户名",
      height: 30,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(user.name,
              style: const TextStyle(color: Colors.white, fontSize: 12)),
          SizedBox(
            width: 8,
          ),
          SizedBox(
              height: 20,
              width: 30,
              child: user.vipLevel == 4 || user.vipLevel == 5
                  ? Image.asset('assets/images/logo/user_vip.png')
                  : Image.asset('assets/images/logo/user_vip.png',
                      color: Colors.grey)),
        ],
      ),
    );
    items.add(userName);

    values.forEach((e) {
      items.add(PopupMenuItem<String>(
          value: e,
          height: 30,
          child: Wrap(
            spacing: 5,
            children: <Widget>[
              // Icon(map[e], color: Colors.blue),
              Text(e,
                  style: const TextStyle(color: Colors.white, fontSize: 12)),
            ],
          )));
    });

    PopupMenuItem<String> logOutItem = const PopupMenuItem<String>(
      value: "退出登录",
      height: 35,
      child: Center(
        child: Text(
          "退出登录",
          style: TextStyle(color: Colors.white, fontSize: 14),
          textAlign: TextAlign.center,
        ),
      ),
    );
    items.add(logOutItem);
    return items;
  }
}
