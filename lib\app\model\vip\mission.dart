// To parse this JSON data, do

import 'package:json_annotation/json_annotation.dart';

part 'mission.g.dart';

@JsonSerializable()
class MissionPegg {
  ///今日已获得皮蛋数，含会员任务
  @Json<PERSON>ey(name: "award")
  int award;

  ///今日可获得最多皮蛋数，不含会员任务
  @Json<PERSON>ey(name: "max_award")
  int maxAward;
  @JsonKey(name: "mission")
  List<Mission> mission;

  ///用户当前皮蛋数
  @Json<PERSON>ey(name: "pegg")
  int pegg;

  MissionPegg({
    required this.award,
    required this.maxAward,
    required this.mission,
    required this.pegg,
  });

  factory MissionPegg.fromJson(Map<String, dynamic> json) =>
      _$MissionPeggFromJson(json);

  Map<String, dynamic> toJson() => _$MissionPeggToJson(this);
}

@JsonSerializable()
class Mission {
  ///任务奖励皮蛋数
  @J<PERSON><PERSON><PERSON>(name: "award")
  int award;

  ///任务已完成次数
  @JsonKey(name: "complete")
  int complete;

  ///任务最多可完成次数
  @Json<PERSON>ey(name: "condition")
  int condition;

  ///领取奖励链接
  @JsonKey(name: "executeUrl")
  String executeUrl;

  ///图标
  @JsonKey(name: "icon")
  String icon;

  ///限制次数
  @JsonKey(name: "limitedType")
  String limitedType;
  @JsonKey(name: "linkUrl")
  String linkUrl;

  ///任务id
  @JsonKey(name: "mid")
  int mid;

  ///任务名称
  @JsonKey(name: "name")
  String name;

  ///任务类型
  @JsonKey(name: "type")
  int type;

  ///任务类型，0普通用户，1会员任务
  @JsonKey(name: "vipType")
  int vipType;

  Mission({
    required this.award,
    required this.complete,
    required this.condition,
    required this.executeUrl,
    required this.icon,
    required this.limitedType,
    required this.linkUrl,
    required this.mid,
    required this.name,
    required this.type,
    required this.vipType,
  });

  factory Mission.fromJson(Map<String, dynamic> json) =>
      _$MissionFromJson(json);

  Map<String, dynamic> toJson() => _$MissionToJson(this);
}
