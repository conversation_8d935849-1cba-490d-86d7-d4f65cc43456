import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import '../../model/videos/video_resp.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class YtsApiService {
  static const String baseUrl = 'https://yts.mx/api/v2';
  int _totalMovies = 0;

  int get totalMovies => _totalMovies;

  Future<List<VideoRespVo>> searchMovies(String query, {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/list_movies.json?query_term=$query&page=$page&limit=$limit'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'ok' && data['data'] != null) {
          _totalMovies = data['data']['movie_count'] ?? 0;
          final movies = data['data']['movies'] as List?;
          if (movies != null) {
            logger.d('获取到 ${movies.length} 部电影，总数: $_totalMovies');
            return movies.map((movie) => _convertToVideoRespVo(movie)).toList();
          }
        }
      }
      return [];
    } catch (e) {
      logger.e('YTS API 搜索错误: $e');
      return [];
    }
  }

  VideoRespVo _convertToVideoRespVo(Map<String, dynamic> movie) {
    final imdbCode = movie['imdb_code'] as String?;
    final playLink = imdbCode != null ? 'https://vidsrc.xyz/embed/movie/$imdbCode' : '';
    
    return VideoRespVo(
      id: movie['id'] ?? 0,
      name: movie['title'] ?? '',
      description: movie['summary'] ?? '',
      coverImage: movie['medium_cover_image'] ?? '',
      duration: movie['runtime'] ?? 0,
      isFree: true,
      playLink: playLink,
      type: movie['genres']?.join(', ') ?? '',
      year: movie['year'] ?? 0,
      score: (movie['rating'] ?? 0.0).toDouble(),
      region: '',
      releaseTime: DateTime.tryParse(movie['date_uploaded'] ?? ''),
    );
  }
} 