{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\git_resource\\movienest-app\\android\\app\\.cxx\\Debug\\2x3625f5\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\git_resource\\movienest-app\\android\\app\\.cxx\\Debug\\2x3625f5\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}