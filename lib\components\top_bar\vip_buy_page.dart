import 'dart:io';

import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:motion_toast/motion_toast.dart';
import 'package:pieces_ai/app/api_https/impl/https_vip_repository.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/app/model/vip/user_coupon.dart';
import 'package:pieces_ai/app/model/vip/vip_ways.dart';

import '../../authentication/models/user.dart';
import '../../utils/http_utils/task_result.dart';
import 'app_inner_buy/BuyEngine.dart';

var logger = Logger(printer: <PERSON><PERSON>rin<PERSON>());

class VipBuyPage extends StatefulWidget {
  final Function(bool) doPaySuccess;

  const VipBuyPage({
    Key? key,
    required this.doPaySuccess,
  }) : super(key: key);

  @override
  State<VipBuyPage> createState() => _VipBuyPageState();
}

class _VipBuyPageState extends State<VipBuyPage> {
  String? selectedDirectory = '';
  final HttpsVipRepository httpsVipRepository = HttpsVipRepository();
  late Future<List<VipWays>> _vipInfoFuture;
  late Future<List<UserCoupon>> _userCouponFuture;
  late TextEditingController _couponCodeEditController;
  bool isChecked = true;
  int price = 299;
  VipWays? selectVipWays;
  UserCoupon? selectCoupon;
  BuyEngin _buyEngin = BuyEngin();

  @override
  void initState() {
    _vipInfoFuture = httpsVipRepository.getVipWaysInfo();
    _userCouponFuture = httpsVipRepository.getUserCoupons();
    _couponCodeEditController = TextEditingController();
    _buyEngin.initializeInAppPurchase();
    super.initState();
  }

  @override
  void dispose() {
    _couponCodeEditController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: Color(0xff2C3036),
        title: Text(AppLocalizations.of(context).membershipRenewal),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
              padding: EdgeInsets.only(left: 10, right: 10),
              child: Column(
                children: [
                  _buildUserInfo(),
                  _buildVips(),
                  SizedBox(
                    height: 10,
                  ),
                  // if (Platform.isAndroid) _buildCouponCode(),
                  if (Platform.isAndroid)
                    SizedBox(
                      height: 10,
                    ),
                  if (Platform.isAndroid) _buildCoupons(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Checkbox(
                        value: isChecked,
                        activeColor: Colors.white,
                        checkColor: Colors.grey,
                        onChanged: (bool? value) {
                          setState(() {
                            isChecked = value!;
                          });
                        },
                      ),
                      Text(
                        AppLocalizations.of(context).iHaveReadAndAgree,
                        style: TextStyle(fontSize: 12),
                      ),
                      RichText(
                        text: TextSpan(
                          text: AppLocalizations.of(context)
                              .piecesAnimaMembershipAgreement,
                          style: TextStyle(color: Colors.blue, fontSize: 12),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Navigator.of(context).pushNamed(
                                  UnitRouter.article_detail,
                                  arguments: "assets/data/user_vip.html");
                            },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.0), // 设置圆角半径
                        ),
                        backgroundColor: Color(0xFF12CDD9),
                      ),
                      onPressed: () async {
                        //生成二维码。替换参数生成购买链接
                        if (!isChecked) {
                          MotionToast.info(
                                  description: Text(AppLocalizations.of(context)
                                      .pleaseReadAndAgreeMembership))
                              .show(context);
                          return;
                        }
                        if (selectVipWays == null) {
                          MotionToast.info(
                                  description: Text(AppLocalizations.of(context)
                                      .pleaseSelectTypeOfMembership))
                              .show(context);
                          return;
                        }

                        //IOS跳IOS支付
                        if (Platform.isIOS) {
                          //获取apple的支付产品的ID
                          EasyLoading.instance..userInteractions = false;
                          EasyLoading.showInfo(
                              AppLocalizations.of(context).gettingPaymentInfo);
                          String iosProductId = selectVipWays!.iosPay;
                          String vpack =
                              await httpsVipRepository.getApplePayOrder(
                                  iosProductId, selectVipWays!.id);
                          logger.d(
                              "获取到的apple支付vpack为：$vpack,产品ID为：$iosProductId");
                          await _buyEngin.buyProduct(iosProductId, vpack,
                              (int status) async {
                            //0表示支付成功，1表示开始支付，-1表示支付失败
                            if (status == 0) {
                              //支付成功
                              widget.doPaySuccess.call(true);
                              EasyLoading.showSuccess(
                                  AppLocalizations.of(context).paySuccess,
                                  duration: Duration(seconds: 1));
                            } else if (status == 1) {
                              EasyLoading.dismiss();
                            } else {
                              EasyLoading.showError(
                                  AppLocalizations.of(context).payFailed,
                                  duration: Duration(seconds: 1));
                            }
                          });
                          return;
                        } else if (Platform.isAndroid) {}

                        price = selectVipWays!.price;
                        User user = GlobalInfo().user;
                        if (user.authToken?.isEmpty ?? true) {
                          MotionToast.error(description: Text("用户信息出错！请重新登录。"))
                              .show(context);
                          return;
                        }
                        if (selectCoupon != null) {
                          if (selectCoupon!.type != 3) {
                            if (selectCoupon!.money >= selectVipWays!.price) {
                              MotionToast.error(
                                      description: Text("该优惠券不可用于当前套餐！"),
                                      toastDuration: Duration(seconds: 1))
                                  .show(context);
                              return;
                            } else {
                              price -= selectCoupon!.money;
                            }
                          }
                        }
                      },
                      child: Text(
                        textAlign: TextAlign.center,
                        AppLocalizations.of(context).payNow +
                            " \$${(price / 100).toStringAsFixed(2)}",
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ),
                    width: MediaQuery.of(context).size.width - 80,
                    height: 40,
                  ),
                  SizedBox(
                    height: 20,
                  ),
                ],
              )),
        ),
      ),
    );
  }

  //激活码兑换
  _buildCouponCode() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: TextField(
            controller: _couponCodeEditController,
            decoration: InputDecoration(
              hintText: '请输入激活码',
              hintStyle: TextStyle(color: Color(0xFF808080), fontSize: 12),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5.0),
                borderSide: BorderSide(color: Colors.blue),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(color: Colors.blue),
              ),
            ),
          ),
          flex: 3,
        ),
        Flexible(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0), // 设置圆角半径
              ),
              backgroundColor: Color(0xFF12CDD9),
            ),
            onPressed: () async {
              String code = _couponCodeEditController.text;
              if (code.isEmpty) {
                MotionToast.warning(description: Text("请输入券码！")).show(context);
                return;
              }
              TaskResult<UserCoupon> result =
                  await httpsVipRepository.codeToCoupons(code);
              if (result.success) {
                MotionToast.success(description: Text("兑换成功")).show(context);
                _couponCodeEditController.clear();
                //重新获取优惠券列表刷新页面
                setState(() {
                  _userCouponFuture = httpsVipRepository.getUserCoupons();
                });
              } else {
                MotionToast.error(description: Text(result.msg)).show(context);
              }
            },
            child: const Text(
              textAlign: TextAlign.center,
              "兑换",
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
          flex: 1,
        )
      ],
    );
  }

  Widget _buildUserInfo() {
    User user = GlobalInfo().user;
    String vipEndStr = user.vipEnd == null
        ? ""
        : DateFormat('y-MM-dd')
            .format(DateTime.fromMillisecondsSinceEpoch(user.vipEnd!))
            .toString();
    return Padding(
      padding: EdgeInsets.only(left: 30, bottom: 27, top: 30),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30, // 头像的半径
            backgroundImage: CachedNetworkImageProvider(user.headIcon), // 显示图
          ),
          Padding(
            padding: EdgeInsets.only(left: 17),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: TextStyle(fontSize: 18),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 9),
                  child: Text(
                    AppLocalizations.of(context).membershipExpired +
                        ":" +
                        vipEndStr,
                    style: TextStyle(color: Color(0xFF808080)),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  //取得服务器下发的vip购买信息
  Widget _buildVips() {
    return FutureBuilder<List<VipWays>>(
        future: _vipInfoFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Text('Error: ${snapshot.error}');
          } else {
            List<VipWays> vipWaysList = snapshot.data!;
            // if(vipWaysList.length>0)
            //   selectVipWays = vipWaysList[0];
            // return Row(
            //   mainAxisAlignment: MainAxisAlignment.start,
            //   children:
            //       vipWaysList.map((vipWays) => _buildVipItem(vipWays)).toList(),
            //使用横向的ListView
            return Container(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: vipWaysList.length,
                //子item的间隔

                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(right: 10),
                    child: _buildVipItem(vipWaysList[index]),
                  );
                },
              ),
            );
          }
        });
  }

  //取得服务器下发的优惠券信息
  Widget _buildCoupons() {
    return FutureBuilder<List<UserCoupon>>(
        future: _userCouponFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Text('Error: ${snapshot.error}');
          } else {
            List<UserCoupon> userCouponList = snapshot.data!;
            // if(vipWaysList.length>0)
            //   selectVipWays = vipWaysList[0];
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: userCouponList
                  .map((userCoupon) => _buildCouponItem(userCoupon))
                  .toList(),
            );
          }
        });
  }

  Widget _buildCouponItem(UserCoupon userCoupon) {
    String vipEndStr = DateFormat('y-MM-dd')
        .format(DateTime.fromMillisecondsSinceEpoch(userCoupon.endtime))
        .toString();
    return GestureDetector(
      onTap: () {
        if (userCoupon.id == selectCoupon?.id) {
          //取消选中
          setState(() {
            selectCoupon = null;
            price = selectVipWays!.price;
          });
        } else {
          if (userCoupon.type != 3) {
            if (userCoupon.money >= selectVipWays!.price) {
              MotionToast.error(
                description: Text("该优惠券不可用于当前套餐！"),
                toastDuration: Duration(seconds: 1),
              ).show(context);
              return;
            }
          }
          setState(() {
            price = selectVipWays!.price - userCoupon.money;
            selectCoupon = userCoupon;
          });
        }
      },
      child: Container(
        decoration: BoxDecoration(
            color: Color(0xFF313131),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: selectCoupon?.id == userCoupon.id
                  ? Color(0xFF12CDD9)
                  : Color(0xFF48474C), // 设置线框的颜色
              width: 2, // 设置线框的宽度
            )),
        child: Column(
          children: [
            Stack(
              children: [
                Container(
                  padding: EdgeInsets.only(top: 15),
                  width: 88,
                  height: 90,
                  decoration: BoxDecoration(
                    //添加渐变色
                    gradient: LinearGradient(
                      begin: Alignment.topCenter, // 从顶部开始
                      end: Alignment.bottomCenter, // 到底部结束
                      colors: [Color(0xFFFDFCFB), Color(0xFFE2D1C3)],
                      stops: [0.0, 1.0],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Text("\$",
                              style: TextStyle(
                                color: Color(0xFFE1BA7B),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Color(0xFFC4964B), // 阴影颜色
                                    offset: Offset(-1, 1), // 阴影偏移量，正值表示向右下偏移
                                  ),
                                ],
                              )),
                          Text((userCoupon.money / 100).toString(),
                              style: TextStyle(
                                color: Color(0xFFE1BA7B),
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Color(0xFFC4964B), // 阴影颜色
                                    offset: Offset(1, 1), // 阴影偏移量，正值表示向右下偏移
                                  ),
                                ],
                              )),
                        ],
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5),
                        child: Text(
                          userCoupon.title,
                          style:
                              TextStyle(color: Color(0xFFDAB16F), fontSize: 12),
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 13),
                        child: Divider(color: Color(0xFFDAB16F), height: 1),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text("有效期：" + vipEndStr,
                          style:
                              TextStyle(color: Color(0xFFDAB16F), fontSize: 8)),
                      // Text(vipWays.title),
                    ],
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  width: 50,
                  height: 12,
                  decoration: BoxDecoration(
                    //添加渐变色
                    gradient: LinearGradient(
                      colors: [Color(0xFFE83333), Color(0xFFFC7E3F)],
                      stops: [0.0, 1.0],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    'MovieNest会员',
                    style: TextStyle(fontSize: 7),
                  ),
                ),
              ],
            ),
            Container(
              alignment: Alignment.center,
              width: 88,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Text(
                '去使用',
                style: TextStyle(
                    fontSize: 13, color: Color(0xFFDAB16F), letterSpacing: 3),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildVipItem(VipWays vipWays) {
    //首次初始化默认选中i的一个
    if (selectVipWays == null) {
      selectVipWays = vipWays;
      price = vipWays.price;
    }
    return GestureDetector(
      onTap: () {
        if (selectCoupon != null && selectCoupon!.type != 3) {
          if (selectCoupon!.money >= vipWays.price) {
            MotionToast.error(
                    description: Text("该优惠券不可用于当前套餐！"),
                    toastDuration: Duration(seconds: 1))
                .show(context);
            //取消选中优惠券
            setState(() {
              selectCoupon = null;
              selectVipWays = vipWays;
              price = vipWays.price;
            });
            return;
          }
        }
        setState(() {
          if (selectCoupon != null)
            price = vipWays.price - selectCoupon!.money;
          else
            price = vipWays.price;
          selectVipWays = vipWays;
        });
      },
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(
          alignment: AlignmentDirectional.topEnd,
          children: [
            Container(
              height: 180,
              padding: EdgeInsets.only(top: 30),
              decoration: BoxDecoration(
                  color: Color(0xFF313131),
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  border: Border.all(
                    color: selectVipWays?.id == vipWays.id
                        ? Color(0xFF12CDD9)
                        : Color(0xFF48474C), // 设置线框的颜色
                    width: 2, // 设置线框的宽度
                  )),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 5),
                    child: Row(
                      children: [
                        // const Image(
                        //   width: 30,
                        //   height: 30,
                        //   image: AssetImage(
                        //       'assets/images/logo/vip_little_logo.png'),
                        // ), // 左边的图标
                        Text(
                          vipWays.title,
                          //自动换行
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: TextStyle(
                              color: Color(0xFFF5D8AC),
                              fontSize: 15,
                              fontWeight: FontWeight.bold),
                        ), // 右边的文字
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 5, top: 10, right: 5),
                    child: Row(
                      children: <Widget>[
                        Text("\$",
                            style: TextStyle(
                                color: Color(0xFFF5D8AC), fontSize: 20)),
                        Text((vipWays.price / 100).toString(),
                            style: TextStyle(
                                color: Color(0xFFF5D8AC), fontSize: 30)),
                      ],
                    ),
                  ),
                  Text(
                    (vipWays.originPrice / 100).toString(),
                    style: TextStyle(
                        decoration: TextDecoration.lineThrough,
                        color: Color(0xFFD1CCC5)),
                  ),
                  // Text(vipWays.title),
                ],
              ),
            ),
            Container(
              alignment: Alignment.center,
              width: 90,
              height: 28,
              decoration: BoxDecoration(
                //添加渐变色
                gradient: LinearGradient(
                  colors: [Color(0xFFFA709A), Color(0xFFFEE140)],
                  stops: [0.0, 1.0],
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: Text(
                vipWays.discount.desc,
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
