// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get create => '作成';

  @override
  String get goods => '商品';

  @override
  String get me => 'マイページ';

  @override
  String get agree => '同意する';

  @override
  String get disagree => '同意しない';

  @override
  String get mainOneClick => 'ワンクリックで作成';

  @override
  String get drafts => '下書き';

  @override
  String get noDrafts => '下書きがありません。クリックして作成してください。';

  @override
  String get oneClickMode => 'ワンクリックモード';

  @override
  String get title => 'タイトル';

  @override
  String get submit => '送信';

  @override
  String get haveDone => '完了\nクリックして確認';

  @override
  String get original => 'オリジナル';

  @override
  String get unDone => '未完了';

  @override
  String get generating => '生成中...';

  @override
  String get failed => '失敗しました';

  @override
  String get rename => '名前を変更';

  @override
  String get delete => '削除';

  @override
  String get export => 'エクスポート';

  @override
  String get reMake => '再作成';

  @override
  String get earnPoints => 'Earn';

  @override
  String get reDownload => '再ダウンロード';

  @override
  String get taskClaim => 'タスク請求';

  @override
  String get resDownload => 'リソースダウンロード';

  @override
  String get notLogged => 'ログインしていません';

  @override
  String get vipExpired => '会員期限切れ';

  @override
  String get myRole => '私の役割';

  @override
  String get allCategory => 'すべてのカテゴリ';

  @override
  String get videoExport => 'ビデオエクスポート';

  @override
  String get syntheticSubtitles => '字幕合成';

  @override
  String get noSubtitles => 'キャンセル後に生成された動画には字幕がありません';

  @override
  String get exPortVideo => '動画をエクスポート';

  @override
  String get exportingInfo => 'エクスポート中...お待ちください。';

  @override
  String get synthesisProgress => '動画合成中...';

  @override
  String get storyboard => 'ストーリーボード';

  @override
  String get exportSuccessfulAndSave => 'エクスポート成功、アルバムに保存されました';

  @override
  String get basicSettings => '基本設定';

  @override
  String get next => '次へ';

  @override
  String get aiWrite => 'AIで作成';

  @override
  String get uploadAudio => '音声をアップロード';

  @override
  String get enterText => 'テキストを入力';

  @override
  String get aiEnterExample => '例を入力: 子供の日';

  @override
  String get selectStyle => 'スタイルを選択';

  @override
  String get numStoryboards => 'ストーリーボードの数';

  @override
  String get aiWriteTips => '注意：AIは自動でコピーとプロンプトを生成します。各ストーリーボードで2ポイントが差し引かれます。';

  @override
  String get nowDeductionPoints => '現在の差し引きポイント：';

  @override
  String get ttsRoles => 'TTSの役割';

  @override
  String get moreSettings => '詳細設定';

  @override
  String get screenRatio => '画面比率';

  @override
  String get pleaseEnterText => 'テキスト内容を入力してください';

  @override
  String get nOperationIsRequired => '再生成には操作が必要ありません';

  @override
  String get speed => '速度';

  @override
  String get fixedCharacter => '固定キャラクター';

  @override
  String get globalSettings => '全体設定';

  @override
  String get repOriText => 'テキストを置き換える';

  @override
  String get subImgTask => '画像タスクを提出';

  @override
  String get subVideoTask => 'ビデオタスクを提出';

  @override
  String get someUnSaved => '一部の変更が保存されていません';

  @override
  String get clickToSaveAndExit => '保存して終了するには「OK」をクリックしてください。';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';

  @override
  String get english => '英語';

  @override
  String get spanish => 'スペイン語';

  @override
  String get japanese => '日本語';

  @override
  String get appSettings => 'アプリ設定';

  @override
  String get settings => 'Settings';

  @override
  String get tutorial => 'チュートリアル';

  @override
  String get about => '概要';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get userAgreement => 'ユーザー契約';

  @override
  String get pleaseRead => 'Please read carefully before using and confirm that you agree to the above terms.';

  @override
  String get welcomeToMovieNest => 'Welcome to MovieNest, in order to better protect your privacy and personal information security, we have formulated';

  @override
  String get versionInfo => 'バージョン情報';

  @override
  String get checkForUpdates => '更新を確認';

  @override
  String get clickToDownloadNewVersion => '新しいバージョンをダウンロードするにはクリックしてください。';

  @override
  String get updateContent => '更新内容';

  @override
  String get newVersionDownloading => '新バージョンをダウンロード中';

  @override
  String get logOut => 'ログアウト';

  @override
  String get logOutInfo => 'ログアウトしますか？';

  @override
  String get exit => '終了';

  @override
  String get logoutSuccessful => 'ログアウト成功';

  @override
  String get logoutFailed => 'ログアウト失敗';

  @override
  String get logoutFailedInfo => 'ログアウトに失敗しました。後でもう一度お試しください。';

  @override
  String get sighInSuccessful => 'Sign in successful';

  @override
  String get cacheClean => 'キャッシュのクリア';

  @override
  String get clickClean => 'クリック';

  @override
  String get thirdDataList => 'サードパーティーデータ一覧';

  @override
  String get personalInfoList => '個人情報リスト';

  @override
  String get accountCancellation => 'アカウント削除';

  @override
  String get accountCancellationInfo => 'アカウントを削除しますか？';

  @override
  String get accountCancellationInfo1 => 'アカウントは永久に削除されます。';

  @override
  String get aiGenerating => 'AI生成中...';

  @override
  String get setToStoryBoard => 'ストーリーボードに設定';

  @override
  String get imitation => 'Imitation';

  @override
  String get light => 'Light';

  @override
  String get pov => 'POV';

  @override
  String get promptDic => 'Prompt dict';

  @override
  String get num => 'Num';

  @override
  String get style => 'Style';

  @override
  String get advanceSetting => 'Advanced Setting';

  @override
  String get image => 'Image';

  @override
  String get video => 'Video';

  @override
  String get tts => 'Dubbing';

  @override
  String get motion => 'Motion';

  @override
  String get preview => 'Preview';

  @override
  String get webDetails => 'ウェブの詳細';

  @override
  String get invalidEmail => 'Invalid email address';

  @override
  String get userHasDisabled => 'User has been disabled';

  @override
  String get userDoesNotExit => 'User does not exist';

  @override
  String get passError => 'Password error';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get emailAlreadyInUse => 'Email already in use';

  @override
  String get unknownOccurred => 'An unknown error occurred';

  @override
  String get operationNotAllowed => 'Operation not allowed';

  @override
  String get weakPassword => 'Password is too weak';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get submitSuccess => 'Submit successfully';

  @override
  String get generated => '(Generated)';

  @override
  String get notGenerated => '(Not generated)';

  @override
  String get lockSeed => 'Lock seed';

  @override
  String get randomSeed => 'Random seed';

  @override
  String get setSeed => 'Set seed';

  @override
  String get deleteDraft => 'Delete draft';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get generateImage => 'Generate image';

  @override
  String get unrecognizedRole => 'Unrecognized role';

  @override
  String get unrecognizedScene => 'Unrecognized scene';

  @override
  String get selectStartImageToVideo => 'Select the first image';

  @override
  String get selectEndImageToVideo => 'Select the last image';

  @override
  String get pleaseEnterTheMotionGuidePrompt => 'Please enter the motion guide prompt words for generating the video, if not filled in, it will be automatically generated according to the picture';

  @override
  String get otherSetting => 'Other settings';

  @override
  String get highQualityModeTakesTwiceTime => '(High-quality mode takes twice time, please be patient)';

  @override
  String get mode => 'Mode';

  @override
  String get fastMode => 'Fast mode';

  @override
  String get highQualityMode => 'High-quality mode';

  @override
  String get resolution => 'Resolution';

  @override
  String get sd => 'SD';

  @override
  String get hd => 'HD';

  @override
  String get play => 'Play';

  @override
  String get startDubbing => 'Start dubbing';

  @override
  String get save => 'Save';

  @override
  String get dubbingFailed => 'Dubbing failed';

  @override
  String get noDubbingYet => 'No dubbing yet';

  @override
  String get textCannotBeEmpty => 'Text cannot be empty';

  @override
  String get generalWaitInfo => 'During peak hours, it takes about 1 minute to generate a picture quickly, and about 5~10 minutes to generate a picture normally. The speed is slightly faster during off-peak hours. Please be patient.\\n\\nDo not exit during the generation of the picture. The recent average waiting time is 3 minutes and 16 seconds.';

  @override
  String get downloadSuccessAndSave => 'Download successful, saved to album';

  @override
  String get submitTask => 'Submit task';

  @override
  String get imageQuality => 'Image quality';

  @override
  String get excellentCase => 'Excellent case';

  @override
  String get allMaterialsGeneratedByMovieNest => 'All materials are generated by MovieNest';

  @override
  String get theDefaultIsHDInfo => 'The default is HD ordinary high-definition. FHD deducts 4 times the points, and QHD deducts 6 times the points. The higher the quality, the richer the picture details';

  @override
  String get aiPrompts => 'Ai prompts';

  @override
  String get aiPromptsInfos => 'After checking, Ai reasoning prompts will be added on the basis of the original prompts';

  @override
  String get reDubbing => 'Re-dubbing';

  @override
  String get reDubbingInfos => 'After checking, TTS dubbing will be re-generated';

  @override
  String get effective => 'Effective range';

  @override
  String get allStoryboards => 'All';

  @override
  String get videoStoryboard => 'Video storyboard';

  @override
  String get dynamicVideo => 'Dynamic video';

  @override
  String get dynamicVideoInfo => 'After checking, each storyboard will generate a dynamic video';

  @override
  String get taskLimit => 'The concurrent task limit has been reached. Maximum 3';

  @override
  String get membershipRenewal => 'Membership renewal';

  @override
  String get iHaveReadAndAgree => 'I have read and agree';

  @override
  String get piecesAnimaMembershipAgreement => '《Membership Purchase Agreement》';

  @override
  String get payNow => 'Pay now';

  @override
  String get pleaseReadAndAgreeMembership => 'Please read and agree to the membership purchase agreement first';

  @override
  String get pleaseSelectTypeOfMembership => 'Please select the type of membership you want to purchase.';

  @override
  String get gettingPaymentInfo => 'Getting payment information...';

  @override
  String get paySuccess => 'Payment successful';

  @override
  String get payFailed => 'Payment failed';

  @override
  String get membershipExpired => 'Membership expired';

  @override
  String get pleaseEnterTheme => 'Please enter a theme';

  @override
  String get pleaseWaitForStyleLoaded => 'Please wait for the style to load';

  @override
  String get myPoints => 'My points';

  @override
  String get claim => 'Claim';

  @override
  String get claimed => 'Claimed';

  @override
  String get checkToReceiveVip => 'Check in To Receive Membership';

  @override
  String get progress => 'Progress';

  @override
  String get playRecord => 'Play Record';

  @override
  String get updateReminders => 'Movie/TV Update Reminders';

  @override
  String get myDownload => 'My Download';

  @override
  String get done => 'Done';

  @override
  String get edit => 'Edit';

  @override
  String get collection => 'Collection';

  @override
  String get share => 'Share';

  @override
  String get vipCardTitle => 'Limited Time Discount';

  @override
  String get vipCardDescription => 'Get a member and enjoy exclusive privileges';

  @override
  String get vipCardFunction1 => 'Remove all  Ads';

  @override
  String get vipCardFunction2 => 'GUnlock Movies';

  @override
  String get vipCardFunction3 => 'Unlimited Downloads';

  @override
  String get vipCardFunction4 => 'HD Resources';

  @override
  String get vipCardFunction5 => 'Unlock Cast';

  @override
  String get vipCardButton => 'Get';

  @override
  String get categoriesTitle => 'CATEGORIES';

  @override
  String get categoriesSearchDesc => 'Searcch Movies, Genres etc';

  @override
  String get deleteSuccess => 'Delete Success';

  @override
  String get deleteFailed => 'Days Failed';

  @override
  String get deleting => 'Deleting';

  @override
  String get deleteSelected => 'DeleteSelected';
}
