import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/vip/order_success_resp.dart';
import 'package:pieces_ai/app/model/vip/vip_order_resp.dart';
import 'package:pieces_ai/utils/analytics_helper.dart';

import '../../../app/api_https/impl/https_vip_repository.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class BuyEngin {
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  late InAppPurchase _inAppPurchase;
  late List<ProductDetails> _products; //内购的商品对象集合
  String orderInfo = ""; // 订单信息
  String orderPack = ""; // 订单包，用于谷歌支付的obfuscatedAccountId

  //外部传入的callback，返回int状态值
  Function(int, String, {bool isAutoRenewing, String? gpaOrderId})? callback;

  //初始化购买组件
  void initializeInAppPurchase() {
    logger.d("【支付流程】初始化购买组件");
    // 初始化in_app_purchase插件
    _inAppPurchase = InAppPurchase.instance;
    //监听购买的事件
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((purchaseDetailsList) {
      logger.d("【支付流程】收到购买更新事件，事件数量: ${purchaseDetailsList.length}");
      logger.d("【支付流程】购买事件完整内容: ${purchaseDetailsList.toString()}");
      for (var purchase in purchaseDetailsList) {
        logger.d("【支付流程】单个购买详情: ${purchase.toString()}");
        if (purchase is GooglePlayPurchaseDetails && purchase.billingClientPurchase != null) {
          logger.d("【支付流程】谷歌购买原始数据: ${purchase.billingClientPurchase.toString()}");
          logger.d("【支付流程】谷歌购买原始JSON: ${purchase.billingClientPurchase!.originalJson}");
        }
      }
      _listenToPurchaseUpdated(purchaseDetailsList);
    }, onDone: () {
      _subscription.cancel();
    }, onError: (error) {
      logger.e("【支付流程】购买监听出错：$error");
    });
  }

  void resumePurchase() {
    _inAppPurchase.restorePurchases();
  }

  /// 加载全部的商品
  Future<void> buyProduct(
      String productId, dynamic orderData, Function(int, String, {bool isAutoRenewing, String? gpaOrderId})? callback) async {
    logger.d("【支付流程】开始购买商品，商品ID: $productId, orderData: $orderData");
    
    // 处理订单数据
    if (orderData is VipOrderResp) {
      this.orderInfo = orderData.orderInfo;
      this.orderPack = orderData.orderPack;
      logger.d("【支付流程】使用VipOrderResp数据, orderInfo: ${orderData.orderInfo}, orderPack: ${orderData.orderPack}");
    } else if (orderData is String) {
      this.orderInfo = orderData;
      // 使用安全的用户ID格式（避免非法字符）
      this.orderPack = 'user_${orderData.hashCode.abs()}';
      logger.d("【支付流程】使用String订单信息: $orderData, 生成orderPack: ${this.orderPack}");
    } else {
      logger.e("【支付流程】无效的订单数据类型");
      this.callback?.call(-1, "", isAutoRenewing: false);
      return;
    }
    
    this.callback = callback; // 保存回调函数
    
    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      logger.e("【支付流程】支付功能不可用");
      this.callback?.call(-1, "", isAutoRenewing: false);
      return;
    }

    logger.d("【支付流程】支付功能可用，准备查询商品信息");
    final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails({productId});
    
    // 记录详细的查询结果
    logger.d("【支付流程】商品查询结果 - 状态码: ${response.error?.code}, 错误信息: ${response.error?.message}");
    logger.d("【支付流程】未找到的商品ID: ${response.notFoundIDs}");
    logger.d("【支付流程】找到的商品数量: ${response.productDetails.length}");
    
    if (response.notFoundIDs.isNotEmpty) {
      logger.e("【支付流程】未找到商品信息，未找到的ID: ${response.notFoundIDs}");
      this.callback?.call(-1, "", isAutoRenewing: false);
      return;
    }

    _products = response.productDetails;
    if (_products.isEmpty) {
      logger.e("【支付流程】未找到商品信息");
      this.callback?.call(-1, "", isAutoRenewing: false);
      return;
    }

    // 记录详细的商品信息
    final ProductDetails productDetails = _products.first;
    logger.d("【支付流程】商品详情:");
    logger.d("【支付流程】- ID: ${productDetails.id}");
    logger.d("【支付流程】- 标题: ${productDetails.title}");
    logger.d("【支付流程】- 描述: ${productDetails.description}");
    logger.d("【支付流程】- 价格: ${productDetails.price} (${productDetails.currencyCode})");
    logger.d("【支付流程】- 原始价格: ${productDetails.rawPrice}");
    
    if (Platform.isAndroid) {
      final GooglePlayProductDetails googleDetails = productDetails as GooglePlayProductDetails;
      logger.d("【支付流程】- Google商品类型: ${googleDetails.productDetails.productType}");
    }
    
    logger.d("【支付流程】找到商品信息，准备开始购买");
    startPurchase(productDetails);
  }

  // 调用此函数以启动购买过程
  void startPurchase(ProductDetails productDetails) async {
    logger.d("购买的商品id为" + productDetails.id);
    if (_products.isNotEmpty) {
      // ToastUtil.showToast("准备开始启动购买流程");
      try {
        logger.d(
            "一切正常，开始购买,信息如下：title: ${productDetails.title}  desc:${productDetails.description} "
            "price:${productDetails.price}  currencyCode:${productDetails.currencyCode}  currencySymbol:${productDetails.currencySymbol}");
        logger.d("【支付流程】订单信息(order_info): $orderInfo");
        logger.d("【支付流程】订单包(order_pack): $orderPack");
        
        // 检查是否为Android平台
        if (Platform.isAndroid) {
          // 在Android平台下设置账户ID
          final purchaseParam = PurchaseParam(
            productDetails: productDetails,
            applicationUserName: orderPack, // 使用orderPack作为用户ID，这是谷歌支付要求的格式
          );
          
          logger.d("【支付流程】设置了applicationUserName: $orderPack");
          
          // 启动购买流程
          _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
        } else {
          // 对于iOS使用常规购买参数
          _inAppPurchase.buyConsumable(
            purchaseParam: PurchaseParam(
              productDetails: productDetails,
              applicationUserName: orderInfo, // iOS使用orderInfo
            )
          );
        }
      } catch (e) {
        logger.e("购买异常：$e");
      }
    } else {
      logger.d("当前没有商品无法调用购买逻辑");
    }
  }

  /// 内购的购买更新监听
  void _listenToPurchaseUpdated(
      List<PurchaseDetails> purchaseDetailsList) async {
    logger.d("【支付流程】开始处理购买更新事件，购买详情数量: ${purchaseDetailsList.length}");
    
    for (PurchaseDetails purchase in purchaseDetailsList) {
      logger.d("【支付流程】处理购买详情: ${purchase.productID}, 状态: ${purchase.status}");
      logger.d("【支付流程】完整购买详情对象: ${purchase.toString()}");
      
      if (purchase is GooglePlayPurchaseDetails) {
        logger.d("【支付流程】谷歌购买对象: ${purchase.toString()}");
        if (purchase.billingClientPurchase != null) {
          final billingPurchase = purchase.billingClientPurchase!;
          logger.d("【支付流程】谷歌账单对象: ${billingPurchase.toString()}");
          logger.d("【支付流程】谷歌购买原始JSON: ${billingPurchase.originalJson}");
          logger.d("【支付流程】谷歌购买Token: ${billingPurchase.purchaseToken}");
          logger.d("【支付流程】谷歌购买签名: ${billingPurchase.signature}");
        }
      }
      
      if (purchase.status == PurchaseStatus.pending) {
        logger.d("【支付流程】支付状态为等待中");
        _handlePending();
      } else if (purchase.status == PurchaseStatus.canceled) {
        logger.d("【支付流程】支付已取消");
        _handleCancel(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        logger.e("【支付流程】支付出错: ${purchase.error?.code} - ${purchase.error?.message}");
        logger.e("【支付流程】完整错误详情: ${purchase.error.toString()}");
        // 检查是否是消费错误
        if (purchase.error?.code == 'consume_purchase_failed') {
          logger.d("【支付流程】尝试重新处理购买");
          try {
            if (Platform.isAndroid) {
              var googleDetail = purchase as GooglePlayPurchaseDetails;
              await checkAndroidPayInfo(googleDetail);
            }
          } catch (e) {
            logger.e("【支付流程】重新处理购买失败: $e");
            _handleError(purchase.error);
          }
        } else {
          _handleError(purchase.error);
        }
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        logger.d("【支付流程】支付成功或已恢复，准备处理");
        
        try {
          if (Platform.isAndroid) {
            logger.d("【支付流程】处理Android支付");
            var googleDetail = purchase as GooglePlayPurchaseDetails;
            logger.d("【支付流程】Android支付详情: ${googleDetail.purchaseID}, ${googleDetail.productID}");
            await checkAndroidPayInfo(googleDetail);
          } else if (Platform.isIOS || Platform.isMacOS) {
            logger.d("【支付流程】处理Apple支付");
            var appstoreDetail = purchase as AppStorePurchaseDetails;
            logger.d("【支付流程】Apple支付详情: ${appstoreDetail.purchaseID}, ${appstoreDetail.productID}");
            await checkApplePayInfo(appstoreDetail);
          }
        } catch (e, stackTrace) {
          logger.e("【支付流程】处理支付成功时发生异常: $e");
          logger.e("【支付流程】异常堆栈: $stackTrace");
          this.callback?.call(-1, "", isAutoRenewing: false);
        }
      }
    }
  }

  /// 购买失败
  void _handleError(IAPError? iapError) {
    this.callback?.call(-1, "", isAutoRenewing: false, gpaOrderId: null);
  }

  /// 等待支付
  void _handlePending() {
    logger.d("等待支付");
    this.callback?.call(1, "", isAutoRenewing: false, gpaOrderId: null);
  }

  /// 取消支付
  void _handleCancel(PurchaseDetails purchase) {
    _inAppPurchase.completePurchase(purchase);
  }

  /// Android支付成功的校验
  Future<void> checkAndroidPayInfo(GooglePlayPurchaseDetails googleDetail) async {
    logger.d("【支付流程】开始处理Android支付成功回调");
    logger.d("【支付流程】Android支付详情 - 交易ID: ${googleDetail.purchaseID}, 商品ID: ${googleDetail.productID}");
    
    // 记录详细的购买信息
    logger.d("【支付流程】Android购买详情:");
    logger.d("【支付流程】- 订单ID: ${googleDetail.purchaseID}");
    logger.d("【支付流程】- 购买状态: ${googleDetail.status}");
    logger.d("【支付流程】- 交易时间: ${googleDetail.transactionDate}");
    
    // 默认为非订阅(非自动续费)
    bool isAutoRenewing = false;
    // 谷歌订单ID (GPA格式)
    String? gpaOrderId;
    
    // 记录详细的PurchaseWrapper信息
    if (googleDetail.billingClientPurchase != null) {
      final billingPurchase = googleDetail.billingClientPurchase!;
      // 获取自动续费状态
      isAutoRenewing = billingPurchase.isAutoRenewing;
      // 获取谷歌订单ID
      gpaOrderId = billingPurchase.orderId;
      
      logger.d("【支付流程】- Google订单详情:");
      logger.d("【支付流程】  - 订单ID: ${billingPurchase.orderId}");
      logger.d("【支付流程】  - 购买Token: ${billingPurchase.purchaseToken}");
      logger.d("【支付流程】  - 原始JSON: ${billingPurchase.originalJson}");
      logger.d("【支付流程】  - 签名: ${billingPurchase.signature}");
      logger.d("【支付流程】  - 包名: ${billingPurchase.packageName}");
      logger.d("【支付流程】  - 购买时间: ${DateTime.fromMillisecondsSinceEpoch(billingPurchase.purchaseTime)}");
      logger.d("【支付流程】  - 购买状态: ${billingPurchase.purchaseState}");
      logger.d("【支付流程】  - 是否自动续费: ${billingPurchase.isAutoRenewing}");
      logger.d("【支付流程】  - 是否已确认: ${billingPurchase.isAcknowledged}");
    }
    
    try {
      if (orderInfo.isEmpty) {
        logger.e("【支付流程】orderInfo为空，无法继续处理");
        this.callback?.call(-1, "", isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
        return;
      }

      String verData = googleDetail.verificationData.serverVerificationData;
      logger.d("【支付流程】验证数据长度: ${verData.length}");
      logger.d("【支付流程】验证数据: $verData");
      logger.d("【支付流程】验证数据对象: ${googleDetail.verificationData.toString()}");
      logger.d("【支付流程】验证数据来源: ${googleDetail.verificationData.source}");
      logger.d("【支付流程】验证数据本地验证数据: ${googleDetail.verificationData.localVerificationData}");
      
      // 记录成功支付事件
      try {
        ProductDetails product = _products.firstWhere((p) => p.id == googleDetail.productID);
        double price = double.tryParse(product.rawPrice.toString()) ?? 0.0;
        String currency = product.currencyCode;
        
        logger.d("【支付流程】准备记录Singular埋点，商品信息: ID=${product.id}, 价格=$price, 货币=$currency");
        await AnalyticsHelper().trackAndroidPurchase(
          product.id,
          price,
          currency,
          googleDetail.purchaseID!,
          verData,
          planType: 'vip'
        );
        logger.d("【支付流程】Singular埋点记录成功");

        // 先完成购买
        try {
          logger.d("【支付流程】准备完成购买");
          await _inAppPurchase.completePurchase(googleDetail);
          logger.d("【支付流程】购买已完成");
        } catch (e, stackTrace) {
          logger.e("【支付流程】完成购买时发生异常: $e");
          logger.e("【支付流程】异常堆栈: $stackTrace");
          this.callback?.call(-1, "", isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
          return;
        }

        // 最后调用成功回调
        logger.d("【支付流程】准备调用成功回调");
        logger.d("【支付流程】购买类型: ${isAutoRenewing ? 'subs (订阅)' : 'inapps (一次性购买)'}");
        if (gpaOrderId != null && gpaOrderId.isNotEmpty) {
          logger.d("【支付流程】谷歌订单ID: $gpaOrderId");
        }
        this.callback?.call(0, verData, isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
        logger.d("【支付流程】成功回调已调用");
      } catch (e, stackTrace) {
        logger.e("【支付流程】记录支付事件失败: $e");
        logger.e("【支付流程】异常堆栈: $stackTrace");
        this.callback?.call(-1, "", isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
      }
    } catch (e, stackTrace) {
      logger.e("【支付流程】处理Android支付时发生异常: $e");
      logger.e("【支付流程】异常堆栈: $stackTrace");
      this.callback?.call(-1, "", isAutoRenewing: isAutoRenewing, gpaOrderId: gpaOrderId);
    }
  }

  /// Apple支付成功的校验
  Future<void> checkApplePayInfo(AppStorePurchaseDetails appstoreDetail) async {
    logger.d("【支付流程】开始处理Apple支付成功回调");
    logger.d("【支付流程】Apple支付purchaseID为:${appstoreDetail.purchaseID}");
    logger.d("【支付流程】Apple支付验证收据为:${appstoreDetail.verificationData.serverVerificationData}");
    logger.d("【支付流程】Apple支付完整数据:$appstoreDetail");

    if (orderInfo.isEmpty) {
      logger.e("【支付流程】orderInfo为空，无法继续处理");
      this.callback?.call(-1, "", isAutoRenewing: false, gpaOrderId: null);
      return;
    }
    _inAppPurchase.completePurchase(appstoreDetail);
    //向服务器请求发放商品
    String verData = appstoreDetail.verificationData.serverVerificationData;
    logger.d("【支付流程】准备调用服务器验证，orderInfo: $orderInfo, verData: $verData");
    int result = await HttpsVipRepository()
        .applePayValidate(orderInfo, verData, appstoreDetail.purchaseID!);
    logger.d("【支付流程】服务器验证结果: $result");
    
    if (200 == result) {
      // 记录成功支付事件
      try {
        ProductDetails product = _products.firstWhere((p) => p.id == appstoreDetail.productID);
        double price = double.tryParse(product.rawPrice.toString()) ?? 0.0;
        String currency = product.currencyCode;
        
        logger.d("【支付流程】准备记录Singular埋点，商品ID: ${product.id}, 价格: $price, 货币: $currency");
        // 调用Singular埋点
        await AnalyticsHelper().trackIosPurchase(
          product.id,
          price,
          currency,
          appstoreDetail.purchaseID!,
          verData,
          planType: 'vip'
        );
        logger.d("【支付流程】Singular埋点记录成功");
        
        logger.d("【支付流程】准备调用成功回调");
        // iOS默认为非订阅购买
        this.callback?.call(0, verData, isAutoRenewing: false, gpaOrderId: null);
        logger.d("【支付流程】成功回调已调用");
      } catch (e) {
        logger.e("【支付流程】记录支付事件失败: $e");
        this.callback?.call(-1, "", isAutoRenewing: false, gpaOrderId: null);
      }
    } else {
      logger.e("【支付流程】服务器验证失败，错误码: $result");
      this.callback?.call(-1, "", isAutoRenewing: false, gpaOrderId: null);
    }
  }

  @override
  void onClose() {
    if (Platform.isIOS || Platform.isMacOS) {
      final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
          _inAppPurchase
              .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      iosPlatformAddition.setDelegate(null);
    }
    _subscription.cancel();
  }
}
