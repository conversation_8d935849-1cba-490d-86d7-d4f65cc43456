import 'package:flutter/widgets.dart';
// Power By 张风捷特烈--- Generated file. Do not edit.
// 欢迎支持： https://github.com/toly1994328/FlutterUnit
class TolyIcon {
    TolyIcon._();
    static const IconData icon_artifact = IconData(0xe726, fontFamily: "TolyIcon");
static const IconData dark = IconData(0xe72f, fontFamily: "TolyIcon");
static const IconData wb_sunny = IconData(0xe746, fontFamily: "TolyIcon");
static const IconData icon_fast = IconData(0xe607, fontFamily: "TolyIcon");
static const IconData icon_layout = IconData(0xe85e, fontFamily: "TolyIcon");
static const IconData upload_success = IconData(0xe60b, fontFamily: "TolyIcon");
static const IconData download = IconData(0xea51, fontFamily: "TolyIcon");
static const IconData upload = IconData(0xea52, fontFamily: "TolyIcon");
static const IconData error = IconData(0xe614, fontFamily: "TolyIcon");
static const IconData dingzhi1 = IconData(0xe60e, fontFamily: "TolyIcon");
static const IconData icon_collect = IconData(0xe672, fontFamily: "TolyIcon");
static const IconData yonghu = IconData(0xe619, fontFamily: "TolyIcon");
static const IconData icon_common = IconData(0xe634, fontFamily: "TolyIcon");
static const IconData icon_see = IconData(0xe608, fontFamily: "TolyIcon");
static const IconData icon_issues = IconData(0xe7a7, fontFamily: "TolyIcon");
static const IconData icon_fork = IconData(0xe623, fontFamily: "TolyIcon");
static const IconData icon_github_star = IconData(0xe7df, fontFamily: "TolyIcon");
static const IconData icon_show = IconData(0xe648, fontFamily: "TolyIcon");
static const IconData icon_hide = IconData(0xe649, fontFamily: "TolyIcon");
static const IconData icon_email = IconData(0xe694, fontFamily: "TolyIcon");
static const IconData icon_github = IconData(0xe689, fontFamily: "TolyIcon");
static const IconData icon_juejin = IconData(0xe601, fontFamily: "TolyIcon");
static const IconData icon_share = IconData(0xe613, fontFamily: "TolyIcon");
static const IconData icon_background = IconData(0xe60a, fontFamily: "TolyIcon");
static const IconData icon_code = IconData(0xe70b, fontFamily: "TolyIcon");
static const IconData icon_item = IconData(0xe66f, fontFamily: "TolyIcon");
static const IconData icon_kafei = IconData(0xe6aa, fontFamily: "TolyIcon");
static const IconData icon_tag = IconData(0xe6e7, fontFamily: "TolyIcon");
static const IconData icon_them = IconData(0xe6c2, fontFamily: "TolyIcon");
static const IconData icon_bug = IconData(0xe7af, fontFamily: "TolyIcon");
static const IconData icon_sound = IconData(0xe606, fontFamily: "TolyIcon");
static const IconData icon_search = IconData(0xe604, fontFamily: "TolyIcon");
static const IconData icon_star_ok = IconData(0xe6ae, fontFamily: "TolyIcon");
static const IconData icon_star = IconData(0xe609, fontFamily: "TolyIcon");
static const IconData icon_star_add = IconData(0xe68e, fontFamily: "TolyIcon");

}  
