/// 关于页面
/// blueming.wu
/// 时间：2024-08-10
import 'package:app/app.dart';
import 'package:components/toly_ui/toly_ui.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/authentication/models/user.dart' as pieces_user;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../authentication/blocs/authentic/bloc.dart';
import '../../../authentication/blocs/authentic/event.dart';
import '../../../utils/analytics_helper.dart';

class AboutAppPage extends StatelessWidget {
  const AboutAppPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: AppColor.piecesBackTwo,
      appBar: AppBar(
        // backgroundColor: AppColor.piecesBlackGrey,
        title: Text(AppLocalizations.of(context).about),
      ),
      body: Container(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const SizedBox(height: 10),
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    image: const DecorationImage(
                        image: AssetImage('assets/images/ic_launcher.png'),
                        fit: BoxFit.cover)),
              ),
            ),
            const SizedBox(height: 10),
            Center(
              child: Text(
                StrUnit.appName,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 10),
            Flexible(
                child: Container(
              margin: const EdgeInsets.all(24),
              child: _buildInfo(context),
            )),
            Container(
              width: double.infinity,
              //水平居中
              child: _buildFoot(),
            )
          ],
        ),
      ),
    );
  }

  _buildFoot() {
    return Column(
      children: <Widget>[
        // FeedbackWidget(
        //     onPressed: (){
        //       _launchURL("https://github.com/toly1994328/FlutterUnit");
        //     },
        //     child: const Text('《查看本项目Github仓库》',style: TextStyle(fontSize: 12,color: Color(0xff616C84),),)),
        const Text(
          'Power By MovieNest',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const Text(
          'Copyright © 2020-2024 ',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildInfo(BuildContext context) {
    const Widget divider = Divider(height: 1);
    return Column(
      children: <Widget>[
        divider,
        ListTile(
          title: Text(AppLocalizations.of(context).privacyPolicy,
              style: TextStyle(
                fontSize: 16,
              )),
          trailing: _nextIcon(context),
          onTap: () => Navigator.of(context).pushNamed(
              UnitRouter.article_detail,
              arguments: "assets/data/Privacy.html"),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
        divider,
        ListTile(
          title: Text(AppLocalizations.of(context).userAgreement,
              style: TextStyle(
                fontSize: 16,
              )),
          trailing: _nextIcon(context),
          onTap: () => Navigator.of(context).pushNamed(
              UnitRouter.article_detail,
              arguments: "assets/data/user_agreement.html"),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
        divider,
        ListTile(
          title: Text(AppLocalizations.of(context).thirdDataList,
              style: TextStyle(
                fontSize: 16,
              )),
          trailing: _nextIcon(context),
          onTap: () => Navigator.of(context).pushNamed(
              UnitRouter.article_detail,
              arguments: "assets/data/third_party_information_sharing.html"),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
        divider,
        ListTile(
          title: Text(AppLocalizations.of(context).personalInfoList,
              style: TextStyle(
                fontSize: 16,
              )),
          trailing: _nextIcon(context),
          onTap: () => Navigator.of(context).pushNamed(
              UnitRouter.article_detail,
              arguments: "assets/data/personal_information.html"),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
        divider,
        ListTile(
          title: Text(AppLocalizations.of(context).accountCancellation,
              style: TextStyle(
                fontSize: 16,
              )),
          trailing: _nextIcon(context),
          onTap: () => _deleteAccount(context),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
        divider,
        SizedBox(
          height: 20,
        ),
        ListTile(
          title: Text(AppLocalizations.of(context).logOut,
              style: TextStyle(fontSize: 16, color: Colors.red)),
          trailing: _nextIcon(context),
          onTap: () => _logout(context),
          // Navigator.of(context).pushReplacementNamed(UnitRouter.version_info),
        ),
      ],
    );
  }

  void _deleteAccount(BuildContext context) async {
    //弹窗给用户确认
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(AppLocalizations.of(context).accountCancellation),
            content: Text(AppLocalizations.of(context).accountCancellationInfo),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).cancel,
                    style: TextStyle(color: Colors.grey)),
              ),
              TextButton(
                onPressed: () async {
                  //删除账户
                  Navigator.of(context).pop();
                },
                child: Text(
                  AppLocalizations.of(context).confirm,
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          );
        });
  }

  //退出登录
  void _logout(BuildContext context) async {
    //弹窗给用户确认
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(AppLocalizations.of(context).logOut),
            content: Text(AppLocalizations.of(context).logOutInfo),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).cancel),
              ),
              TextButton(
                onPressed: () async {
                  await Supabase.instance.client.auth.signOut();
                  // await FirebaseAuth.instance.signOut();
                  //清除登录信息
                  GlobalInfo.instance.reset();
                  pieces_user.User user = GlobalInfo.instance.user;
                  
                  // 添加退出登录埋点
                  await AnalyticsHelper().trackLogout(user.userId.toString());
                  
                  //发送退出登录通知
                  final AuthBloc authBloc = BlocProvider.of<AuthBloc>(context);
                  authBloc.add(Logout(user: user));
                  //直接跳转到首页的我的页面，MyCloudHomePage
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      UnitRouter.nav, (route) => false);
                },
                child: Text(AppLocalizations.of(context).confirm),
              ),
            ],
          );
        });
  }

  Widget _nextIcon(BuildContext context) =>
      Icon(Icons.chevron_right, color: Theme.of(context).primaryColor);
}

class InfoPanel extends StatelessWidget {
  final String title;
  final String info;

  const InfoPanel({Key? key, required this.title, required this.info})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Circle(color: Theme.of(context).primaryColor),
            Padding(
              padding: const EdgeInsets.only(left: 15, top: 15, bottom: 15),
              child: Text(
                title,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            )
          ],
        ),
        Panel(
          color: Theme.of(context).primaryColor.withAlpha(33),
          child: Text(
            info,
            style: const TextStyle(
                color: Colors.grey,
                fontSize: 13,
                shadows: [Shadow(color: Colors.white, offset: Offset(1, 1))]),
          ),
        ),
      ],
    );
  }
}
