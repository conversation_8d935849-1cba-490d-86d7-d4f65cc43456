import 'package:json_annotation/json_annotation.dart';

part 'vip_order_resp.g.dart';

/// 会员订单响应模型
@JsonSerializable()
class VipOrderResp {
  /// 订单信息，用于支付验证
  @Json<PERSON><PERSON>(name: "order_info")
  final String orderInfo;
  
  /// 订单包，用于Google支付的obfuscatedAccountId
  @JsonKey(name: "order_pack")
  final String orderPack;

  VipOrderResp({
    required this.orderInfo,
    required this.orderPack,
  });

  VipOrderResp copyWith({
    String? orderInfo,
    String? orderPack,
  }) =>
      VipOrderResp(
        orderInfo: orderInfo ?? this.orderInfo,
        orderPack: orderPack ?? this.orderPack,
      );

  factory VipOrderResp.fromJson(Map<String, dynamic> json) =>
      _$VipOrderRespFromJson(json);

  Map<String, dynamic> toJson() => _$VipOrderRespToJson(this);
} 