import 'package:json_annotation/json_annotation.dart';

part 'vip_ways.g.dart';

@JsonSerializable()
class VipWays {
  @<PERSON>sonKey(name: "cpack")
  final String cpack;
  @<PERSON>son<PERSON>ey(name: "desc")
  final String desc;
  @<PERSON>son<PERSON>ey(name: "discount")
  final Discount discount;
  @<PERSON>sonKey(name: "id")
  final int id;
  @JsonKey(name: "ios_pay")
  final String iosPay;
  @JsonKey(name: "origin_price")
  final int originPrice;
  @<PERSON>sonKey(name: "price")
  final int price;
  @<PERSON>sonKey(name: "title")
  final String title;
  @<PERSON>sonKey(name: "way")
  final int way;

  VipWays({
    required this.cpack,
    required this.desc,
    required this.discount,
    required this.id,
    required this.iosPay,
    required this.originPrice,
    required this.price,
    required this.title,
    required this.way,
  });

  // 方法：将 originPrice 转换为以元为单位的 double 值，再变成字符串
  String getPriceInDollars() {
    final calculatePrice = price / 100.0;
    if (calculatePrice == calculatePrice.truncateToDouble()) {
      // 如果价格的小数部分为 0，则返回整数字符串
      return calculatePrice.truncate().toString();
    } else {
      // 否则返回包含小数的字符串
      return calculatePrice.toStringAsFixed(2); // 保留两位小数
    }
  }

  // 获取原价方法
  String getOriginPriceInDollars() {
    final calculatePrice = originPrice / 100.0;
    if (calculatePrice == calculatePrice.truncateToDouble()) {
      // 如果价格的小数部分为 0，则返回整数字符串
      return calculatePrice.truncate().toString();
    } else {
      // 否则返回包含小数的字符串
      return calculatePrice.toStringAsFixed(2); // 保留两位小数
    }
  }

  // 方法：计算省钱的金额，再变成字符串
  String getSavePriceInDollars() {
    final savePrice = (originPrice - price) / 100.0;
    if (savePrice == savePrice.truncateToDouble()) {
      // 如果价格的小数部分为 0，则返回整数字符串
      return savePrice.truncate().toString();
    } else {
      // 否则返回包含小数的字符串
      return savePrice.toStringAsFixed(2); // 保留两位小数
    }
  }

  VipWays copyWith({
    String? cpack,
    String? consumeUrl,
    String? desc,
    Discount? discount,
    String? hint,
    int? id,
    String? iosPay,
    int? mode,
    int? originPrice,
    int? price,
    String? title,
    int? way,
  }) =>
      VipWays(
        cpack: cpack ?? this.cpack,
        desc: desc ?? this.desc,
        discount: discount ?? this.discount,
        id: id ?? this.id,
        iosPay: iosPay ?? this.iosPay,
        originPrice: originPrice ?? this.originPrice,
        price: price ?? this.price,
        title: title ?? this.title,
        way: way ?? this.way,
      );

  factory VipWays.fromJson(Map<String, dynamic> json) =>
      _$VipWaysFromJson(json);

  Map<String, dynamic> toJson() => _$VipWaysToJson(this);
}

@JsonSerializable()
class Discount {
  @JsonKey(name: "desc")
  final String desc;
  @JsonKey(name: "hint")
  final String hint;

  Discount({
    required this.desc,
    required this.hint,
  });

  Discount copyWith({
    String? desc,
    String? hint,
  }) =>
      Discount(
        desc: desc ?? this.desc,
        hint: hint ?? this.hint,
      );

  factory Discount.fromJson(Map<String, dynamic> json) =>
      _$DiscountFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountToJson(this);
}
