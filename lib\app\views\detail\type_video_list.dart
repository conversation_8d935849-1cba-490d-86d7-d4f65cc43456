import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../components/custom_widget/LoadMoreListview.dart';
import '../../../components/custom_widget/hotel_booking/hotel_app_theme.dart';
import '../../model/VideoType.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class TypeVideosList extends StatefulWidget {
  final VideoType videoType;

  const TypeVideosList({Key? key, required this.videoType}) : super(key: key);

  @override
  State<TypeVideosList> createState() => _TypeVideosListState();
}

class _TypeVideosListState extends State<TypeVideosList> {
  @override
  void initState() {
    super.initState();
  }

  @override
  dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: HotelAppTheme.buildDarkTheme(),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0x4D000000).withOpacity(0.5),
                Color(0xB3C50101).withOpacity(0.5)
              ],
              // begin: Alignment.topCenter,
              // end: Alignment.bottomCenter,
            ),
          ),
          child: Scaffold(
            appBar: PreferredSize(
              preferredSize:
                  Size.fromHeight(MediaQuery.of(context).size.width * 9 / 16),
              child: Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Image.asset(
                      "assets/images/bg/type_list_bg.jpg",
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    child: Padding(
                        padding: EdgeInsets.only(left: 20),
                        child: Text(
                          'TOP TV SHOWS',
                          style: TextStyle(
                              fontSize: 35, fontWeight: FontWeight.w600),
                        )),
                    left: 0,
                    bottom: 0,
                  ),
                  Positioned(
                    left: 20,
                      top: 30,
                      child: IconButton(
                    icon: Icon(Icons.arrow_back_ios),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ))
                ],
              ),
            ),
            body: Center(
              child: PullToRefreshAndLoadMore(),
            ),
          ),
        ));
  }
}
