import 'package:app/app/router/unit_router.dart';
import 'package:flutter/material.dart';
import 'package:pieces_ai/app/model/VideoType.dart';

import '../theme/fitness_app_theme.dart';

class TitleView extends StatelessWidget {
  final List<VideoType> videoTypes;
  final String titleTxt;
  final String subTxt;

  const TitleView({
    super.key,
    this.titleTxt = "",
    this.subTxt = "", required this.videoTypes,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Text(
                titleTxt,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: FitnessAppTheme.fontName,
                  fontWeight: FontWeight.w500,
                  fontSize: 18,
                  letterSpacing: 0.5,
                  color: FitnessAppTheme.white,
                ),
              ),
            ),
            InkWell(
              highlightColor: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(4.0)),
              onTap: () {
                // Navigator.pushNamed(context, UnitRouter.video_type_list,
                //     arguments: VideoType(id: 0, name: "All"));
                Navigator.pushNamed(
                  context,
                  UnitRouter.video_filter,arguments: videoTypes
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Row(
                  children: <Widget>[
                    Text(
                      subTxt,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontFamily: FitnessAppTheme.fontName,
                        fontWeight: FontWeight.normal,
                        fontSize: 16,
                        letterSpacing: 0.5,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(
                      height: 38,
                      width: 26,
                      child: Icon(
                        Icons.navigate_next_sharp,
                        color: Colors.grey,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
