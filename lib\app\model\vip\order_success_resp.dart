import 'package:json_annotation/json_annotation.dart';

part 'order_success_resp.g.dart';

@JsonSerializable()
class OrderSuccessResp {
  @J<PERSON><PERSON>ey(name: "day")
  final int day;
  @<PERSON><PERSON><PERSON><PERSON>(name: "end_time")
  final String endTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: "uid")
  final String uid;

  OrderSuccessResp({
    required this.day,
    required this.endTime,
    required this.uid,
  });

  OrderSuccessResp copyWith({
    int? day,
    String? endTime,
    String? uid,
  }) =>
      OrderSuccessResp(
        day: day ?? this.day,
        endTime: endTime ?? this.endTime,
        uid: uid ?? this.uid,
      );

  factory OrderSuccessResp.fromJson(Map<String, dynamic> json) =>
      _$OrderSuccessRespFromJson(json);

  Map<String, dynamic> toJson() => _$OrderSuccessRespToJson(this);
}
