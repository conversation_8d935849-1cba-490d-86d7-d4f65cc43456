// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyArwH3shy1BI9rhrNVcJWJCuHZlHhZSPak',
    appId: '1:172739951489:web:2db5e58549655dd75b8680',
    messagingSenderId: '172739951489',
    projectId: 'movienest-fdedb',
    authDomain: 'movienest-fdedb.firebaseapp.com',
    databaseURL: 'https://movienest-fdedb-default-rtdb.firebaseio.com',
    storageBucket: 'movienest-fdedb.firebasestorage.app',
    measurementId: 'G-SRWC4PRV4X',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAxvTvXGbb2fksNqqFDg9NQR273U1I02Ws',
    appId: '1:172739951489:android:923a1e1f36bfb3835b8680',
    messagingSenderId: '172739951489',
    projectId: 'movienest-fdedb',
    databaseURL: 'https://movienest-fdedb-default-rtdb.firebaseio.com',
    storageBucket: 'movienest-fdedb.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBewhWvuDf4IVq1qnVKtXqnNEFmdNSVA6o',
    appId: '1:172739951489:ios:ea97309c7365b04f5b8680',
    messagingSenderId: '172739951489',
    projectId: 'movienest-fdedb',
    databaseURL: 'https://movienest-fdedb-default-rtdb.firebaseio.com',
    storageBucket: 'movienest-fdedb.firebasestorage.app',
    androidClientId: '172739951489-1jj3o88vh60l9r8b4dn09racve3ctgs7.apps.googleusercontent.com',
    iosClientId: '172739951489-p5v6rd6p962h5vqc28d6pm0gnum9glbo.apps.googleusercontent.com',
    iosBundleId: 'com.doujing.movienest',
  );
}
