import 'dart:convert';

import 'package:logger/logger.dart';
import 'package:pieces_ai/utils/http_utils/encrypt_utils.dart';
import 'package:pieces_ai/utils/http_utils/http_util.dart';
import '../../model/user/user_resp.dart';

var logger = Logger(printer: PrettyPrinter());

/// 登录接口类
class HttpsUserEncryption {
  static const String loginUrl = '/movie/nest/sec/login';

  /// 登录方法
  Future<UserRespVo?> login(String email, String tid) async {
    try {
      // 构建请求参数
      Map<String, dynamic> param = await HttpUtil.withBaseParam();

      final dataToSend = '{"email": "$email","tid": "$tid"}';

      var encryptedData = EncryptUtils.encryptData(dataToSend);

      param['spec'] = {'login': encryptedData};

      logger.d("加密登录请求参数：${param.toString()}");

      // 发送POST请求
      var result = await HttpUtil.instance.client.post(
        HttpUtil.apiBaseUrl + loginUrl,
        data: param,
      );

      logger.d("加密登录响应值：${result.data}");

      // 检查响应数据
      if (result.data != null && result.data['code'] == 200) {
        logger.d("加密登录成功，返回数据：${result.data['data'].toString()}");

        // 解密响应数据
        final decryptedResponse =
            EncryptUtils.decryptData(result.data['data']['info']);

        logger.d("解密后的响应数据：$decryptedResponse");

        // 解析并返回用户信息r
        return UserRespVo.fromJson(jsonDecode(decryptedResponse));
      } else {
        logger.e("加密登录失败，错误信息：${result.data['message']}");
        return null;
      }
    } catch (e) {
      logger.e("加密登录异常：$e");
      return null;
    }
  }
}
