import 'package:json_annotation/json_annotation.dart';

part 'text_2_video_request.g.dart';

///业务参数
@JsonSerializable()
class Text2VideoParam {
  ///固定格式来组装动作。
  @JsonKey(name: "actions")
  final List<Action> actions;

  ///生成视频长度，单位为秒，默认为2，，最长支持4秒
  @JsonKey(name: "duration")
  final int duration;
  @JsonKey(name: "lora")
  final String? lora;
  @<PERSON>sonKey(name: "negative_prompt")
  final String negativePrompt;

  ///绘画提示词，支持中英文
  @JsonKey(name: "prompt")
  final String prompt;

  ///生成视频质量，默认为0，0表示标清(8fps),1表示高清24FPS
  @JsonKey(name: "quality")
  final int quality;

  ///生成视频宽高比例,默认为0，0表示1:1，1表示4:3，2表示16:9,3表示9:16，4表示4：3
  @<PERSON>sonKey(name: "ratio")
  final int ratio;
  @JsonKey(name: "sampling")
  final String? sampling;

  ///支持文生视频得风格
  @JsonKey(name: "style_name")
  final String styleName;
  @JsonKey(name: "vae")
  final String? vae;

  @JsonKey(name: "mode")
  final String? mode;

  Text2VideoParam(
      {required this.actions,
      required this.duration,
      this.lora,
      required this.negativePrompt,
      required this.prompt,
      required this.quality,
      required this.ratio,
      this.sampling,
      required this.styleName,
      this.vae,
      this.mode});

  Text2VideoParam copyWith(
          {List<Action>? actions,
          int? duration,
          String? lora,
          String? negativePrompt,
          String? prompt,
          int? quality,
          int? ratio,
          String? sampling,
          String? styleName,
          String? vae,
          String? mode}) =>
      Text2VideoParam(
          actions: actions ?? this.actions,
          duration: duration ?? this.duration,
          lora: lora ?? this.lora,
          negativePrompt: negativePrompt ?? this.negativePrompt,
          prompt: prompt ?? this.prompt,
          quality: quality ?? this.quality,
          ratio: ratio ?? this.ratio,
          sampling: sampling ?? this.sampling,
          styleName: styleName ?? this.styleName,
          vae: vae ?? this.vae,
          mode: mode ?? this.mode);

  factory Text2VideoParam.fromJson(Map<String, dynamic> json) =>
      _$Text2VideoParamFromJson(json);

  Map<String, dynamic> toJson() => _$Text2VideoParamToJson(this);
}

@JsonSerializable()
class Action {
  @JsonKey(name: "frame")
  final String frame;

  Action({
    required this.frame,
  });

  Action copyWith({
    String? frame,
  }) =>
      Action(
        frame: frame ?? this.frame,
      );

  factory Action.fromJson(Map<String, dynamic> json) => _$ActionFromJson(json);

  Map<String, dynamic> toJson() => _$ActionToJson(this);
}
