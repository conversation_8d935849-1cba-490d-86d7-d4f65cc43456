import 'package:json_annotation/json_annotation.dart';

part 'user_resp.g.dart';

///VipInfo
@JsonSerializable()
class VipInfo {
  /// 会员结束时间
  @Json<PERSON>ey(name: "endtime_at")
  final String? endtimeAt;

  VipInfo({
    this.endtimeAt,
  });

  VipInfo copyWith({
    String? endtimeAt,
  }) =>
      VipInfo(
        endtimeAt: endtimeAt ?? this.endtimeAt,
      );

  factory VipInfo.fromJson(Map<String, dynamic> json) => _$VipInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VipInfoToJson(this);
}

///UserRespVo
@JsonSerializable()
class UserRespVo {
  /// FireBase第三方信息
  @Json<PERSON>ey(name: "access_token")
  final String? accessToken;

  /// 邮箱号
  @JsonKey(name: "email")
  final String? email;

  /// 用户uid
  @J<PERSON><PERSON><PERSON>(name: "uuid")
  final String? uuid;

  /// 用户名
  @J<PERSON><PERSON><PERSON>(name: "uname")
  final String? uname;

  /// 用户简介
  @J<PERSON><PERSON><PERSON>(name: "user_desc")
  final String? userDesc;

  /// VIP信息
  @JsonKey(name: "vip_info")
  final VipInfo? vipInfo;

  const UserRespVo({
    this.accessToken,
    this.email,
    this.uuid,
    this.uname,
    this.userDesc,
    this.vipInfo,
  });

  UserRespVo copyWith({
    String? accessToken,
    String? email,
    String? uuid,
    String? uname,
    String? userDesc,
    VipInfo? vipInfo,
  }) =>
      UserRespVo(
        accessToken: accessToken ?? this.accessToken,
        email: email ?? this.email,
        uuid: uuid ?? this.uuid,
        uname: uname ?? this.uname,
        userDesc: userDesc ?? this.userDesc,
        vipInfo: vipInfo ?? this.vipInfo,
      );

  factory UserRespVo.fromJson(Map<String, dynamic> json) =>
      _$UserRespVoFromJson(json);

  Map<String, dynamic> toJson() => _$UserRespVoToJson(this);
}
