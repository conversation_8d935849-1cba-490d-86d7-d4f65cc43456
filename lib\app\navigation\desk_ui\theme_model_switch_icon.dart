import 'package:app/app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

///首页主题设置
class ThemeModelSwitchIcon extends StatelessWidget {
  const ThemeModelSwitchIcon({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ThemeMode model =
        context.select<AppBloc, ThemeMode>((bloc) => bloc.state.themeMode);
    bool isDark = Theme.of(context).brightness == Brightness.dark;
    // bool isDark = true;
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          context
              .read<AppBloc>()
              .changeThemeMode(isDark ? ThemeMode.light : ThemeMode.dark);
        },
        child: Padding(
          padding: const EdgeInsets.only(bottom: 16, top: 16),
          child: Icon(
            !isDark ? TolyIcon.dark : TolyIcon.wb_sunny,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
