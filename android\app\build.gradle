plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services" version "4.4.0" apply false
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion 35

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    namespace 'com.czygame.HeChengDaXiGua' // 替换为你的实际包名

    kotlinOptions {
//        jvmTarget = '1.8'
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    signingConfigs {
        debug {
            keyAlias 'keyczy'
            keyPassword '123654'
            storeFile file('./GoogleKeyStore.jks')
            storePassword '123654'
        }
        release {
            keyAlias 'keyczy'
            keyPassword '123654'
            storeFile file('./GoogleKeyStore.jks')
            storePassword '123654'
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.czygame.HeChengDaXiGua"
        // 这里修改成24 打包的apk大了一倍，具体见这个帖子https://blog.csdn.net/TsuiXh/article/details/127917925
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        archivesBaseName = "PiecePro.apk"
        ndk {
//            abiFilters 'arm64-v8a'
//            abiFilters 'armeabi', 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }


    buildTypes {
        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            // signingConfig signingConfigs.debug
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            minifyEnabled true
            //输出的apk增加版本号
            applicationVariants.all { variant ->
                variant.outputs.all {
                    outputFileName = "MovieNest_v${variant.versionName}.apk"
                }
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.facebook.android:facebook-login:latest.release'
    implementation platform('com.google.firebase:firebase-bom:33.10.0')
    
    // Singular SDK依赖
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    implementation 'com.google.android.gms:play-services-appset:16.0.2'
    implementation 'store.galaxy.samsung.installreferrer:samsung_galaxystore_install_referrer:4.0.0'
}

