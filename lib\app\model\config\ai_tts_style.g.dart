// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_tts_style.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiTtsStyle _$AiTtsStyleFromJson(Map<String, dynamic> json) => AiTtsStyle(
      age: (json['age'] as num).toInt(),
      charge: (json['charge'] as num).toInt(),
      icon: json['icon'] as String,
      id: (json['id'] as num).toInt(),
      language: (json['language'] as num).toInt(),
      mode: (json['mode'] as num).toInt(),
      name: json['name'] as String,
      order: (json['order'] as num).toInt(),
      sex: (json['sex'] as num).toInt(),
      style: json['style'] as String?,
      tag: json['tag'] as String,
      type: json['type'] as String,
      url: json['url'] as String,
      vip: (json['vip'] as num).toInt(),
      voiceType: json['voice_type'] as String,
    );

Map<String, dynamic> _$AiTtsStyleToJson(AiTtsStyle instance) =>
    <String, dynamic>{
      'age': instance.age,
      'charge': instance.charge,
      'icon': instance.icon,
      'id': instance.id,
      'language': instance.language,
      'mode': instance.mode,
      'name': instance.name,
      'order': instance.order,
      'sex': instance.sex,
      'style': instance.style,
      'tag': instance.tag,
      'type': instance.type,
      'url': instance.url,
      'vip': instance.vip,
      'voice_type': instance.voiceType,
    };
