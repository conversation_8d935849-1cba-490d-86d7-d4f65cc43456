import 'package:cached_network_image/cached_network_image.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:components/toly_ui/ti/circle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:pieces_ai/app/model/ai_style_model.dart';

/// create by blueming.wu
/// Ai推文风格专用的gridView
//    {
//      "widgetId": 163,
//      "name": 'GridView.count构造',
//      "priority": 1,
//      "subtitle":
//          "【children】 : 子组件列表   【List<Widget>】\n"
//          "【crossAxisCount】 : 主轴一行box数量  【int】\n"
//          "【mainAxisSpacing】 : 主轴每行间距  【double】\n"
//          "【crossAxisSpacing】 : 交叉轴每行间距  【double】\n"
//          "【childAspectRatio】 : box主长/交叉轴长  【double】\n"
//          "【crossAxisCount】 : 主轴一行数量  【int】",
//    }
class StyleGridView extends StatefulWidget {
  const StyleGridView(
      {Key? key,
      required this.aiStyleModels,
      required this.aiStyleModelChanged,
      required this.selectStyleId,
      required this.countPerLine})
      : super(key: key);

  final List<Child> aiStyleModels;
  final int selectStyleId;
  final int countPerLine;
  final Function(Child) aiStyleModelChanged;

  @override
  _StyleGridViewState createState() => _StyleGridViewState();
}

class _StyleGridViewState extends State<StyleGridView>
    with SingleTickerProviderStateMixin {
  int selectedIndex = 0; //默认选中第一个风格
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    //看是否选中
    for (int i = 0; i < widget.aiStyleModels.length; i++) {
      if (widget.selectStyleId == widget.aiStyleModels[i].id) {
        selectedIndex = i;
        break;
      }
    }
    // widget.aiStyleModelChanged(
    //     widget.aiStyleModels[selectedCategoryIndex].children[selectedIndex]);
    _tabController =
        TabController(length: widget.aiStyleModels.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose(); // 释放控制器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        if (widget.countPerLine < 10)
          Row(
            children: [
              Circle(
                color: Color(0xFF12CDD9),
                radius: 5,
              ),
              Padding(
                padding: EdgeInsets.only(left: 15),
                child: Text(
                  AppLocalizations.of(context).selectStyle,
                  style: TextStyle(fontSize: 14),
                ),
              )
            ],
          ),
        const SizedBox(height: 5),
        Expanded(child: _buildGridView(widget.aiStyleModels)),
      ],
    );
  }

  Widget _buildGridView(List<Child> aiStyleModels) => Swiper(
      itemCount: aiStyleModels.length,
      // pagination: SwiperPagination(),
      control: SwiperControl(),
      viewportFraction: 0.5,
      onIndexChanged: (index) {
        widget.aiStyleModelChanged(aiStyleModels[index]);
        setState(() {
          selectedIndex = index;
        });
      },
      itemBuilder: (context, index) {
        return _buildItem(
          aiStyleModels[index].name,
          aiStyleModels[index].icon,
          index,
          aiStyleModels[index],
        );
      });

  Container _buildItem(
      String title, String imageUrl, int index, Child aiStyleModelChild) {
    return Container(
      height: 68,
      width: 68,
      alignment: Alignment.center,
      child: Padding(
        child: GestureDetector(
          onTap: () {
            widget.aiStyleModelChanged(aiStyleModelChild);
            setState(() {
              selectedIndex = index;
            });
          },
          child: Column(
            children: [
              Flexible(
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    border: Border.all(
                      color: selectedIndex == index
                          ? Color(0xFF12CDD9)
                          : Colors.transparent,
                      width: 2, // 设置边框宽度
                    ),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(imageUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                flex: 4,
              ),
              const SizedBox(height: 5),
              Text(
                title,
                maxLines: 1,
                style: const TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
        padding: const EdgeInsets.only(left: 2, top: 2, right: 2),
      ),
    );
  }
}
