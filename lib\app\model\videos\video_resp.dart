import 'package:json_annotation/json_annotation.dart';

part 'video_resp.g.dart';

///ApifoxModel
@JsonSerializable()
class VideoRespVo {
  ///封面图
  @Json<PERSON><PERSON>(name: "cover_image")
  String? coverImage;

  ///简介
  @<PERSON><PERSON><PERSON><PERSON>(name: "description")
  String? description;

  ///视频的持续时长，单位为：
  @Json<PERSON>ey(name: "duration")
  int duration;

  ///ID
  @JsonKey(name: "id")
  int id;

  ///是否免费
  @Json<PERSON><PERSON>(name: "is_free")
  bool isFree;

  ///影片名称
  @JsonKey(name: "name")
  String name;

  ///播放链接
  @JsonKey(name: "play_link")
  String playLink;

  ///国家或地区
  @JsonKey(name: "region")
  String? region;

  ///上映时间
  @Json<PERSON><PERSON>(name: "release_time")
  DateTime? releaseTime;

  ///评分
  @<PERSON>son<PERSON><PERSON>(name: "score")
  double? score;

  ///类型
  @J<PERSON><PERSON><PERSON>(name: "type")
  String type;

  ///年份
  @J<PERSON><PERSON><PERSON>(name: "year")
  int year;

  ///字幕文件地址
  @J<PERSON><PERSON><PERSON>(name: "subtitle_link")
  String? subtitle;

  ///额外的字幕文件,内容是json集合
  @JsonKey(name: "other_subtitle")
  List<String>? extraSubtitle;

  VideoRespVo({
    this.coverImage,
    this.description,
    required this.duration,
    required this.id,
    required this.isFree,
    required this.name,
    required this.playLink,
    this.region,
    this.releaseTime,
    this.score,
    this.subtitle,
    this.extraSubtitle,
    required this.type,
    required this.year,
  });

  VideoRespVo copyWith({
    String? coverImage,
    String? description,
    int? duration,
    int? id,
    bool? isFree,
    String? name,
    String? playLink,
    String? subtitle,
    List<String>? extraSubtitle,
    String? region,
    DateTime? releaseTime,
    double? score,
    String? type,
    int? year,
  }) =>
      VideoRespVo(
        coverImage: coverImage ?? this.coverImage,
        description: description ?? this.description,
        duration: duration ?? this.duration,
        id: id ?? this.id,
        isFree: isFree ?? this.isFree,
        name: name ?? this.name,
        playLink: playLink ?? this.playLink,
        subtitle: subtitle ?? this.subtitle,
        extraSubtitle: extraSubtitle ?? this.extraSubtitle,
        region: region ?? this.region,
        releaseTime: releaseTime ?? this.releaseTime,
        score: score ?? this.score,
        type: type ?? this.type,
        year: year ?? this.year,
      );

  factory VideoRespVo.fromJson(Map<String, dynamic> json) => _$VideoRespVoFromJson(json);

  Map<String, dynamic> toJson() => _$VideoRespVoToJson(this);
}
