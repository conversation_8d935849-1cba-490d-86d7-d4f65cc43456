// ignore_for_file: constant_identifier_names

import 'dart:io';

import 'package:app/app.dart';
import 'package:flutter/material.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';
import 'package:pieces_ai/app/navigation/unit_navigation.dart';
import 'package:pieces_ai/app/views/about/about_app_page.dart';
import 'package:pieces_ai/app/views/about/version_info.dart';
import 'package:pieces_ai/app/views/detail/recom_video_detail.dart';
import 'package:pieces_ai/app/views/detail/video_search.dart';
import 'package:pieces_ai/app/views/setting/setting_page.dart';
import 'package:pieces_ai/components/custom_widget/hotel_booking/filters_screen.dart';
import 'package:pieces_ai/widget_ui/mobile/widget_detail/article_detail_page.dart';

import '../../authentication/views/mobile/login/login_page.dart';
import '../model/VideoType.dart';
import '../views/detail/type_video_list.dart';
import '../../../../app/views/vip/vip_center_page.dart';

class UnitRouters {
  static const String widget_scene_edit = '/widget_scene_edit';
  static const String text_2_video = '/text_2_video';

  static const String detail = 'detail';

  // static const String search = 'search_bloc';

  static const String collect = 'CollectPage';
  static const String setting = 'SettingPage';
  static const String font_setting = 'FountSettingPage';
  static const String theme_color_setting = 'ThemeColorSettingPage';
  static const String code_style_setting = 'CodeStyleSettingPage';
  static const String item_style_setting = 'ItemStyleSettingPage';
  static const String version_info = 'VersionInfo';
  static const String login = 'login';

  static const String ai_style_edit = 'ai_style_edit';
  static const String about_me = 'AboutMePage';
  static const String about_app = 'AboutAppPage';
  static const String register = 'register';

  //跳转到ArticleDetailPage，内置webview
  static const String article_detail = 'article_detail';

  //视频播放详情页
  static const String video_detail = 'video_detail';
  static const String video_type_list = 'video_type_list';

  //视频搜索页
  static const String video_search = 'video_search';

  //视频过滤页
  static const String video_filter = 'video_filter';

  //会员中心页
  static const String vip_center = 'vip_center';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      //
      case UnitRouter.nav:
        if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          return ZeroPageRoute(child: UnitNavigation());
        }
        return ZeroPageRoute(child: UnitNavigation());

      case article_detail:
        return SlidePageRoute(
            child: ArticleDetailPage(url: settings.arguments as String));
      case setting:
        return SlidePageRoute(child: const SettingPage());
      // return Right2LeftRouter(builder:(_)=> const SettingPage());
      // return MaterialPageRoute(builder:(_)=> const SettingPage());

      case version_info:
        return ZeroPageRoute(child: const VersionInfo());

      case login:
        return ZeroPageRoute(child: const LoginScreen());

      case about_app:
        return SlidePageRoute(child: const AboutAppPage());

      case video_detail:
        return SlidePageRoute(
            child: VideoDetail(
          recomVideo: settings.arguments as VideoRespVo,
        ));
      case video_type_list:
        return SlidePageRoute(
            child: TypeVideosList(
          videoType: settings.arguments as VideoType,
        ));
      case video_search:
        return MaterialPageRoute(
          builder: (context) => VideoSearch(),
          settings: settings, // 确保传递 settings
        );
      case video_filter:
        return SlidePageRoute(
            child: FiltersScreen(
                videoTypes: settings.arguments as List<VideoType>));
      case vip_center:
        return MaterialPageRoute(
          builder: (context) => VipCenterPage(),
        );

      default:
        return MaterialPageRoute(
            builder: (_) => Scaffold(
                  body: Center(
                    child: Text('No route defined for ${settings.name}'),
                  ),
                ));
    }
  }
}
