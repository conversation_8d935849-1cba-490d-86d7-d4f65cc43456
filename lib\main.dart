import 'dart:io';

import 'package:app/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/app/navigation/mobile/state/appState.dart' as mobile;
import 'package:pieces_ai/authentication/models/user.dart' as pieces_user;
import 'package:pieces_ai/firebase_options.dart';
import 'package:pieces_ai/utils/analytics_helper.dart';
import 'package:provider/provider.dart';
import 'package:singular_flutter_sdk/singular.dart';
import 'package:singular_flutter_sdk/singular_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:widget_repository/widget_repository.dart';
import 'package:pieces_ai/utils/user_storage.dart';
import 'package:android_id/android_id.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

import '../authentication/blocs/authentic/bloc.dart';
import '../authentication/repository/impl/http_auth_repository.dart';
import 'app/router/unit_router.dart';
import 'app/views/splash/standard_unit_splash.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await InAppPurchase.instance.isAvailable(); // 检查支付是否可用
  await dotenv.load(fileName: ".env");
  // MediaKit.ensureInitialized();
  
  // 初始化UserStorage
  await UserStorage.init();
  
  // 先检查本地缓存的用户数据
  final UserStorage userStorage = UserStorage();
  final pieces_user.User? cachedUser = await userStorage.getUser();
  if (cachedUser != null) {
    print('找到本地缓存的用户信息: ${cachedUser.name}');
    GlobalInfo.instance.user = cachedUser;
  }
  
  await Firebase.initializeApp(
    // name: 'MovieNest',
    options: DefaultFirebaseOptions.currentPlatform,
  ).whenComplete(() {
    print('Firebase initialized');
    
    // 禁用Firebase Dynamic Links相关功能
    // 这将帮助防止在activity的resume过程中发生的崩溃
    FirebaseOptions options = Firebase.app().options;
    print('Firebase app name: ${Firebase.app().name}');
    print('Firebase options: ${options.projectId}');
  });

  // 启用 Firebase Analytics 数据收集
  await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  print('Firebase Analytics 数据收集已启用');

  await Supabase.initialize(
    url: "https://twbktftgvlefrcwhkmtw.supabase.co",
    anonKey:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR3Ymt0ZnRndmxlZnJjd2hrbXR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg1MDU1NzAsImV4cCI6MjA1NDA4MTU3MH0.-nv7aXbf9oXUdejHQjjfPyrjjzE_gmqxjUWyGU7Vrgk",
  ).then((value) => debugPrint('Supabase initialized'));
  // firebase_auth.User? user = firebase_auth.FirebaseAuth.instance.currentUser;

  User? user = await Supabase.instance.client.auth.currentUser;
  print('当前Supabase用户状态: ${user != null ? "已登录" : "未登录"}');

// 使用本地缓存，不需要Supabase用户信息了
//   if (user != null) {
//     print('使用Supabase用户信息');
//     GlobalInfo.instance.user = pieces_user.User(
//         name: user.email!,
//         pegg: 1000,
//         gender: 0,
//         vType: 0,
//         userId: user.id.hashCode,
//         vipEnd: -1,
//         headIcon:
//             'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
//         authToken: user.id.toString());
//   }
  
  // 初始化本地化数据
  // await initializeDateFormatting('jp_JP', null);
  //加载全局配置文件
  await GlobalConfiguration().loadFromAsset("app_settings");

  runApp(
    MultiBlocProvider(providers: [
      // 全局 bloc : 维护应用存储状态、更新、认证
      ChangeNotifierProvider<mobile.AppState>(create: (_) => mobile.AppState()),
      BlocProvider<AuthBloc>(
          create: (_) => AuthBloc(repository: HttpAuthRepository())),
      BlocProvider<AppBloc>(
          create: (_) => AppBloc(AppStateRepository())..initApp()),
      // BlocProvider<UpdateBloc>(create: (_) => UpdateBloc()),
      // BlocProvider<UserBloc>(create: (_) => UserBloc()),

      // BlocProvider<DraftBloc>(
      //     create: (_) => DraftBloc(repository: DraftDbRepository())),
    ], child: FlutterUnit()),
  );

  // 配置Singular SDK
  SingularConfig config = SingularConfig('xiway_dae2e281', '724b2d633addd42da4684ff782567844');
  
  print('===== Singular SDK初始化开始 =====');
  
  // 打印包名信息以验证
  String packageName = 'com.czygame.HeChengDaXiGua'; // 从日志中观察到的包名
  print('当前应用包名: $packageName');
  print('确保此包名与Singular平台配置一致');
  
  // 设置用户ID
  if (user != null) {
    config.customUserId = user.id; // 设置自定义用户 ID
    print('设置用户ID: ${user.id}');
  } else {
    config.customUserId = "unlogin";
    print('设置默认用户ID: unlogin');
  }
  
  // 设置用户类型
  String userType = 'free';
  if (user != null) {
    // 从用户元数据中获取VIP等级
    final userMeta = user.userMetadata;
    if (userMeta != null && userMeta['vip_level'] != null) {
      final vipLevel = userMeta['vip_level'] as int;
      if (vipLevel > 0) {
        userType = 'vip';
      }
    }
  }
  
  // 添加全局属性
  config.withGlobalProperty("app_side", "b_side", true); // B面标识
  config.withGlobalProperty("app_version", "1.0.0", true); // 应用版本
  
  // 添加用户类型
  config.withGlobalProperty("user_type", userType, true);
  
  // 添加系统语言
  final locale = WidgetsBinding.instance.window.locale;
  config.withGlobalProperty("language", locale.languageCode, true);
  
  // 添加平台信息
  config.withGlobalProperty("platform", Platform.operatingSystem, true);
  
  // 添加地区信息
  config.withGlobalProperty("region", locale.countryCode ?? "unknown", true);
  
  // 启用Facebook广告归因（META Install Referrer）
  config.facebookAppId = "1172104681217718";
  print('设置Facebook App ID: 1172104681217718');
  
  print('添加全局属性:');
  print('- app_side=b_side');
  print('- app_version=1.0.0');
  print('- user_type=$userType');
  print('- language=${locale.languageCode}');
  print('- platform=${Platform.operatingSystem}');
  print('- region=${locale.countryCode ?? "unknown"}');
  
  // 启用剪贴板归因
  config.clipboardAttribution = true;
  print('启用剪贴板归因: true');
  
  // 支持深链接域名
  config.espDomains = ['movienet.app', 'vidsrc.xyz'];
  print('配置深链接域名: movienet.app, vidsrc.xyz');
  
  // 启用SKAN
  if (Platform.isIOS) {
    config.skAdNetworkEnabled = true;
    config.waitForTrackingAuthorizationWithTimeoutInterval = 300;
    print('iOS设备，启用SKAN: true, 设置ATT等待时间: 300秒');
  } else {
    print('非iOS设备，SKAN未启用');
  }

  // 配置设备特性回调
  config.deviceAttributionCallback = (Map<String, dynamic> deviceAttributionInfo) {
    print('收到设备归因信息: $deviceAttributionInfo');
  };
  
  // SDID回调
  config.sdidReceivedCallback = (String sdid) {
    print('收到SDID: $sdid');
  };
  
  // 添加SDID设置回调
  config.didSetSdidCallback = (String sdid) {
    print('SDID设置成功: $sdid');
  };
  
  // 添加SKAN转换值更新回调
  config.conversionValueUpdatedCallback = (int conversionValue) {
    print('SKAN转换值更新: $conversionValue');
  };
  
  // 添加SKAN转换值更新回调（包含粗粒度值和锁定状态）
  config.conversionValuesUpdatedCallback = (int conversionValue, int coarse, bool lock) {
    print('SKAN转换值更新: $conversionValue, 粗粒度值: $coarse, 锁定: ${lock ? "是" : "否"}');
  };
  
  // 配置初始化日志
  print('Singular SDK版本: 1.6.0');
  print('设备平台: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');
  
  // 初始化Singular SDK
  try {
    // 增加一个用于检测SDK初始化状态的事件
    print('开始初始化Singular SDK...');
    Singular.start(config);
    print('Singular SDK初始化成功');
    
    // 立即发送一个初始化成功事件
    Singular.event('b_app_init');
    print('发送初始化成功事件');
    
    // 获取并打印Android ID
    if (Platform.isAndroid) {
      final androidIdPlugin = AndroidId();
      try {
        // 检查网络连接
        final connectivityResult = await Connectivity().checkConnectivity();
        if (connectivityResult == ConnectivityResult.none) {
          print('警告: 设备当前没有网络连接，Singular事件可能无法发送');
        } else {
          print('网络连接正常: ${connectivityResult.toString()}');
        }
        
        final String? androidId = await androidIdPlugin.getId();
        if (androidId != null) {
          print('Android ID: $androidId');
          
          // 添加一个立即触发的测试事件
          print('发送控制台测试事件...');
          Singular.eventWithArgs('b_console_test', {
            'android_id': androidId,
            'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            'test_mode': 'singular_console_test'
          });
          print('控制台测试事件发送成功');
          
          // 添加一个不带前缀的标准事件
          print('发送标准事件名称测试...');
          Singular.eventWithArgs('test_event', {
            'android_id': androidId,
            'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            'package_name': 'com.czygame.HeChengDaXiGua'
          });
          print('标准事件名称测试发送成功');
          
          // 添加Singular推荐的标准事件
          print('发送Singular推荐的标准事件...');
          Singular.event('login');
          print('Singular推荐的标准事件发送成功');
          
          // 尝试获取Singular的设备标识
          try {
            print('获取Singular设备标识...');
            Map<String, dynamic> deviceIds = {
              'androidId': androidId,
              'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
            };
            Singular.eventWithArgs('device_ids_debug', deviceIds);
            print('设备标识事件已发送，请在Singular控制台查看');
          } catch (e) {
            print('获取Singular设备标识出错: $e');
          }
        } else {
          print('无法获取Android ID');
        }
      } catch (e) {
        print('获取Android ID时出错: $e');
      }
    }
  } catch (e) {
    print('Singular SDK初始化失败: $e');
  }
  print('===== Singular SDK初始化完成 =====');
  
  // 延迟检查Singular状态
  Future.delayed(Duration(seconds: 5), () async {
    print('===== 开始测试Singular SDK功能 =====');
    
    try {
      // 1. 测试基本事件发送功能
      print('测试1: 发送基本事件');
      Singular.event('b_test_event');
      print('基本事件发送成功');
      print('请检查Singular测试控制台是否显示此事件');
      
      // 等待1秒后再发送第二个事件
      await Future.delayed(Duration(seconds: 1));
      
      // 2. 测试带参数事件功能
      print('测试2: 发送带参数事件');
      final params = {
        'test_param': 'test_value', 
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        'device_type': Platform.isAndroid ? 'android' : 'ios',
        'os_version': Platform.operatingSystemVersion,
        'build_mode': 'debug'
      };
      Singular.eventWithArgs('b_test_event_with_args', params);
      print('带参数事件发送成功');
      print('请检查Singular测试控制台是否显示此事件');
      
      // 3. 测试设置用户ID
      print('测试3: 设置用户ID');
      Singular.setCustomUserId('test_user_${DateTime.now().millisecondsSinceEpoch}');
      print('用户ID设置成功');
      
      // 4. 使用AnalyticsHelper
      print('测试4: 使用AnalyticsHelper');
      final analyticsHelper = AnalyticsHelper();
      await analyticsHelper.checkInitialization();
      await analyticsHelper.debugPrintAllConfig();
      
      // 5. 发送APP启动事件
      print('测试5: 发送APP启动事件');
      await analyticsHelper.trackCustomEvent('b_app_start', {
        'platform': Platform.operatingSystem,
        'mode': 'debug',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      });
      print('APP启动事件发送成功');
      
    } catch (e) {
      print('Singular SDK功能测试失败: $e');
    }
    
    print('===== Singular SDK功能测试完成 =====');
  });
}

class FlutterUnit extends StatelessWidget {
  FlutterUnit({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: StrUnit.appName,
      debugShowCheckedModeBanner: false,
      onGenerateRoute: UnitRouters.generateRoute,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      //设置默认语言
      // locale: const Locale('jp', 'JP'),
      // themeMode: ThemeMode.dark,
      // themeMode: state.themeMode,
      // theme: HotelAppTheme.buildLightTheme(),
      //全局单例的loading
      theme: ThemeData(
        primaryColor: Color(0xFF939393),
        tabBarTheme:
            const TabBarTheme(dividerColor: Colors.transparent), //去掉tabbar分割线
      ),
      navigatorObservers: [
        FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance),
      ],
      builder: EasyLoading.init(),
      home: const StandardUnitSplash(),
    );
  }
}
