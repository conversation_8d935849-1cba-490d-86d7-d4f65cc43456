<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40"><head><meta http-equiv=Content-Type  content="text/html; charset=utf-8" ><meta name=ProgId  content=Word.Document ><meta name=Generator  content="Microsoft Word 14" ><meta name=Originator  content="Microsoft Word 14" ><link rel=File-List  href="Luckyluna_User_Agreement_English.files/filelist.xml" ><title></title><!--[if gte mso 9]><xml><o:DocumentProperties><o:Author>python-docx</o:Author><o:Description>generated by python-docx</o:Description><o:LastAuthor>周周</o:LastAuthor><o:Revision>1</o:Revision><o:Pages>6</o:Pages></o:DocumentProperties><o:CustomDocumentProperties><o:KSOProductBuildVer dt:dt="string" >2052-6.11.0.8885</o:KSOProductBuildVer><o:ICV dt:dt="string" >7B119FF06D2855B6BBE746670079DBCA_43</o:ICV></o:CustomDocumentProperties></xml><![endif]--><!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]--><!--[if gte mso 9]><xml><w:WordDocument><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery><w:DocumentKind>DocumentNotSpecified</w:DocumentKind><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing><w:View>Web</w:View><w:Compatibility><w:DontGrowAutofit/><w:DoNotExpandShiftReturn/><w:UseFELayout/></w:Compatibility><w:Zoom>0</w:Zoom></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml><w:LatentStyles DefLockedState="false"  DefUnhideWhenUsed="true"  DefSemiHidden="true"  DefQFormat="false"  DefPriority="99"  LatentStyleCount="260" >
<w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 7" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 8" ></w:LsdException>
<w:LsdException Locked="false"  Priority="9"  SemiHidden="false"  QFormat="true"  Name="heading 9" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 7" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 8" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index 9" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 7" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 8" ></w:LsdException>
<w:LsdException Locked="false"  Priority="39"  SemiHidden="false"  Name="toc 9" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Normal Indent" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="footnote text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="annotation text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="header" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="footer" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="index heading" ></w:LsdException>
<w:LsdException Locked="false"  Priority="35"  SemiHidden="false"  QFormat="true"  Name="caption" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="table of figures" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="envelope address" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="envelope return" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="footnote reference" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="annotation reference" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="line number" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="page number" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="endnote reference" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="endnote text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="table of authorities" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="macro" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="toa heading" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Bullet" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Number" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Bullet 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Bullet 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Bullet 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Bullet 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Number 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Number 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Number 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Number 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="10"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Title" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Closing" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Signature" ></w:LsdException>
<w:LsdException Locked="false"  Priority="1"  SemiHidden="false"  Name="Default Paragraph Font" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text Indent" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Continue" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Continue 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Continue 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Continue 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Continue 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Message Header" ></w:LsdException>
<w:LsdException Locked="false"  Priority="11"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Subtitle" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Salutation" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Date" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text First Indent" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text First Indent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Note Heading" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text Indent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Body Text Indent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Block Text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Hyperlink" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="FollowedHyperlink" ></w:LsdException>
<w:LsdException Locked="false"  Priority="22"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Strong" ></w:LsdException>
<w:LsdException Locked="false"  Priority="20"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Emphasis" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Document Map" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Plain Text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="E-mail Signature" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Normal (Web)" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Acronym" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Address" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Cite" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Code" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Definition" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Keyboard" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Preformatted" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Sample" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Typewriter" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="HTML Variable" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Normal Table" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="annotation subject" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No List" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / a / i" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / 1.1 / 1.1.1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Article / Section" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Simple 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Simple 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Simple 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Classic 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Classic 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Classic 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Classic 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Colorful 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Colorful 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Colorful 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Columns 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Columns 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Columns 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Columns 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Columns 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 7" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Grid 8" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 7" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table List 8" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table 3D effects 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table 3D effects 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table 3D effects 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Contemporary" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Elegant" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Professional" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Subtle 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Subtle 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Web 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Web 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Web 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Balloon Text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="59"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Table Theme" ></w:LsdException>
<w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Placeholder Text" ></w:LsdException>
<w:LsdException Locked="false"  Priority="1"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="No Spacing" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="34"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="List Paragraph" ></w:LsdException>
<w:LsdException Locked="false"  Priority="29"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Quote" ></w:LsdException>
<w:LsdException Locked="false"  Priority="30"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Intense Quote" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 1" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 2" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 3" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 4" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 5" ></w:LsdException>
<w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 6" ></w:LsdException>
<w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 6" ></w:LsdException>
</w:LatentStyles></xml><![endif]--><style>
@font-face{
font-family:"Times New Roman";
}

@font-face{
font-family:"宋体";
}

@font-face{
font-family:"Wingdings";
}

@font-face{
font-family:"Symbol";
}

@font-face{
font-family:"Calibri";
}

@font-face{
font-family:"ＭＳ ゴシック";
}

@font-face{
font-family:"Courier";
}

@font-face{
font-family:"Cambria";
}

@font-face{
font-family:"ＭＳ 明朝";
}

@list l0:level1{
mso-level-number-format:decimal;
mso-level-suffix:tab;
mso-level-text:"%1.";
mso-level-tab-stop:54.0000pt;
mso-level-number-position:left;
margin-left:54.0000pt;text-indent:-18.0000pt;font-family:'Times New Roman';}

@list l1:level1{
mso-level-number-format:decimal;
mso-level-suffix:tab;
mso-level-text:"%1.";
mso-level-tab-stop:36.0000pt;
mso-level-number-position:left;
margin-left:36.0000pt;text-indent:-18.0000pt;font-family:'Times New Roman';}

@list l2:level1{
mso-level-number-format:bullet;
mso-level-suffix:tab;
mso-level-text:"";
mso-level-tab-stop:54.0000pt;
mso-level-number-position:left;
margin-left:54.0000pt;text-indent:-18.0000pt;font-family:Symbol;}

@list l3:level1{
mso-level-number-format:bullet;
mso-level-suffix:tab;
mso-level-text:"";
mso-level-tab-stop:36.0000pt;
mso-level-number-position:left;
margin-left:36.0000pt;text-indent:-18.0000pt;font-family:Symbol;}

@list l4:level1{
mso-level-number-format:decimal;
mso-level-suffix:tab;
mso-level-text:"%1.";
mso-level-tab-stop:18.0000pt;
mso-level-number-position:left;
margin-left:18.0000pt;text-indent:-18.0000pt;font-family:'Times New Roman';}

@list l5:level1{
mso-level-number-format:bullet;
mso-level-suffix:tab;
mso-level-text:"";
mso-level-tab-stop:18.0000pt;
mso-level-number-position:left;
margin-left:18.0000pt;text-indent:-18.0000pt;font-family:Symbol;}

p.MsoNormal{
mso-style-name:正文;
mso-style-parent:"";
margin-bottom:10.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

h1{
mso-style-name:"标题 1";
mso-style-next:正文;
margin-top:24.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:1;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(54,96,145);
font-weight:bold;
font-size:14.0000pt;
}

h2{
mso-style-name:"标题 2";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:2;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-size:13.0000pt;
}

h3{
mso-style-name:"标题 3";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:3;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-size:11.0000pt;
}

h4{
mso-style-name:"标题 4";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:4;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-style:italic;
font-size:11.0000pt;
}

h5{
mso-style-name:"标题 5";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:5;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(36,63,97);
font-size:11.0000pt;
}

h6{
mso-style-name:"标题 6";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:6;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(36,63,97);
font-style:italic;
font-size:11.0000pt;
}

p.MsoHeading7{
mso-style-name:"标题 7";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:7;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(63,63,63);
font-style:italic;
font-size:11.0000pt;
}

p.MsoHeading8{
mso-style-name:"标题 8";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:8;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-size:10.0000pt;
}

p.Msoheading9{
mso-style-name:"标题 9";
mso-style-noshow:yes;
mso-style-next:正文;
margin-top:10.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
mso-outline-level:9;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(63,63,63);
font-style:italic;
font-size:10.0000pt;
}

span.10{
font-family:'Times New Roman';
}

span.15{
font-family:'Times New Roman';
color:rgb(192,80,77);
letter-spacing:0.2500pt;
font-weight:bold;
text-decoration:underline;
text-underline:single;
font-variant:small-caps;
}

span.16{
font-family:'Times New Roman';
color:rgb(192,80,77);
text-decoration:underline;
text-underline:single;
font-variant:small-caps;
}

span.17{
font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-style:italic;
}

span.18{
font-family:'Times New Roman';
color:rgb(127,127,127);
font-style:italic;
}

span.19{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(63,63,63);
font-style:italic;
font-size:10.0000pt;
}

span.20{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-size:10.0000pt;
}

span.21{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(36,63,97);
font-style:italic;
}

span.22{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(36,63,97);
}

span.23{
font-family:'Times New Roman';
}

span.24{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
}

span.25{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-size:13.0000pt;
}

span.26{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(54,96,145);
font-weight:bold;
font-size:14.0000pt;
}

span.27{
font-family:'Times New Roman';
font-size:8.0000pt;
}

span.28{
font-family:'Times New Roman';
}

span.29{
font-family:'Times New Roman';
font-style:italic;
}

span.30{
font-family:'Times New Roman';
font-weight:bold;
}

span.31{
font-family:'Times New Roman';
letter-spacing:0.2500pt;
font-weight:bold;
font-variant:small-caps;
}

span.32{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
letter-spacing:0.7500pt;
font-style:italic;
font-size:12.0000pt;
}

span.33{
font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-style:italic;
}

span.34{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(63,63,63);
font-style:italic;
}

span.35{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(23,54,93);
letter-spacing:0.2500pt;
font-size:26.0000pt;
mso-font-kerning:14.0000pt;
}

span.36{
font-family:'Times New Roman';
color:rgb(0,0,0);
font-style:italic;
}

span.37{
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-style:italic;
}

span.38{
font-family:Courier;
font-size:10.0000pt;
}

span.39{
font-family:'Times New Roman';
}

span.40{
font-family:'Times New Roman';
}

p.41{
mso-style-name:"TOC Heading";
mso-style-noshow:yes;
mso-style-parent:"标题 1";
mso-style-next:正文;
margin-top:24.0000pt;
margin-bottom:0.0000pt;
page-break-after:avoid;
mso-pagination:lines-together;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(54,96,145);
font-weight:bold;
font-size:14.0000pt;
}

p.42{
mso-style-name:"Intense Quote";
mso-style-next:正文;
margin-top:10.0000pt;
margin-right:46.8000pt;
margin-bottom:14.0000pt;
margin-left:46.8000pt;
border-bottom:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:0.5000pt solid rgb(79,129,189);
padding:0pt 0pt 4pt 0pt ;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-style:italic;
font-size:11.0000pt;
}

p.43{
mso-style-name:"No Spacing";
margin-bottom:0.0000pt;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.44{
mso-style-name:"List Paragraph";
margin-bottom:10.0000pt;
margin-left:36.0000pt;
mso-add-space:auto;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.45{
mso-style-name:Quote;
mso-style-next:正文;
margin-bottom:10.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-style:italic;
font-size:11.0000pt;
}

p.MsoTitle{
mso-style-name:标题;
mso-style-next:正文;
margin-bottom:15.0000pt;
mso-add-space:auto;
border-bottom:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
padding:0pt 0pt 4pt 0pt ;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(23,54,93);
letter-spacing:0.2500pt;
font-size:26.0000pt;
mso-font-kerning:14.0000pt;
}

p.MsoListContinue3{
mso-style-name:"列表接续 3";
mso-style-noshow:yes;
margin-bottom:6.0000pt;
margin-left:54.0000pt;
mso-add-space:auto;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.p{
mso-style-name:"普通\(网站\)";
mso-style-noshow:yes;
margin-top:5.0000pt;
margin-right:0.0000pt;
margin-bottom:5.0000pt;
margin-left:0.0000pt;
mso-margin-top-alt:auto;
mso-margin-bottom-alt:auto;
text-align:left;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:12.0000pt;
}

p.MsoListContinue2{
mso-style-name:"列表接续 2";
mso-style-noshow:yes;
margin-bottom:6.0000pt;
margin-left:36.0000pt;
mso-add-space:auto;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoBodyText2{
mso-style-name:"正文文本 2";
mso-style-noshow:yes;
margin-bottom:6.0000pt;
line-height:200%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoList{
mso-style-name:列表;
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:18.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoSubtitle{
mso-style-name:副标题;
mso-style-next:正文;
margin-bottom:10.0000pt;
line-height:114%;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
letter-spacing:0.7500pt;
font-style:italic;
font-size:12.0000pt;
}

p.MsoHeader{
mso-style-name:页眉;
mso-style-noshow:yes;
margin-bottom:0.0000pt;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoFooter{
mso-style-name:页脚;
mso-style-noshow:yes;
margin-bottom:0.0000pt;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoListBullet2{
mso-style-name:"列表项目符号 2";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:36.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoListContinue{
mso-style-name:列表接续;
mso-style-noshow:yes;
margin-bottom:6.0000pt;
margin-left:18.0000pt;
mso-add-space:auto;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoList2{
mso-style-name:"列表 2";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:36.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoListNumber3{
mso-style-name:"列表编号 3";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:54.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoBodyText{
mso-style-name:正文文本;
mso-style-noshow:yes;
margin-bottom:6.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoListBullet3{
mso-style-name:"列表项目符号 3";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:54.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoBodyText3{
mso-style-name:"正文文本 3";
mso-style-noshow:yes;
margin-bottom:6.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:8.0000pt;
}

p.MsoListBullet{
mso-style-name:列表项目符号;
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:18.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoCaption{
mso-style-name:题注;
mso-style-noshow:yes;
mso-style-next:正文;
margin-bottom:10.0000pt;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
color:rgb(79,129,189);
font-weight:bold;
font-size:9.0000pt;
}

p.MsoListNumber2{
mso-style-name:"列表编号 2";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:36.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoList3{
mso-style-name:"列表 3";
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:54.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoListNumber{
mso-style-name:列表编号;
mso-style-noshow:yes;
margin-bottom:10.0000pt;
margin-left:18.0000pt;
mso-add-space:auto;
text-indent:-18.0000pt;
line-height:114%;
font-family:Cambria;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:11.0000pt;
}

p.MsoMacro{
mso-style-name:宏文本;
mso-style-noshow:yes;
margin-bottom:10.0000pt;
line-height:114%;
font-family:Courier;
mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';
font-size:10.0000pt;
}

span.msoIns{
mso-style-type:export-only;
mso-style-name:"";
text-decoration:underline;
text-underline:single;
color:blue;
}

span.msoDel{
mso-style-type:export-only;
mso-style-name:"";
text-decoration:line-through;
color:red;
}

table.MsoNormalTable{
mso-style-name:普通表格;
mso-style-parent:"";
mso-style-noshow:yes;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent6{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-left-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-border-right-alt:1.0000pt solid rgb(247,150,70);
mso-border-insideh:1.0000pt solid rgb(247,150,70);
mso-border-insidev:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent6FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:2.2500pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(247,150,70);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent6LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(247,150,70);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent6FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent6LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent6OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
}

table.MsoTableLightGridAccent6OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-insidev:1.0000pt solid rgb(247,150,70);
}

table.MsoTableLightGridAccent6EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 6";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-insidev:1.0000pt solid rgb(247,150,70);
}

table.MsoTableLightGridAccent1{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-left-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-border-right-alt:1.0000pt solid rgb(79,129,189);
mso-border-insideh:1.0000pt solid rgb(79,129,189);
mso-border-insidev:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent1FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:2.2500pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(79,129,189);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent1LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(79,129,189);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent1FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent1LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent1OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
}

table.MsoTableLightGridAccent1OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-insidev:1.0000pt solid rgb(79,129,189);
}

table.MsoTableLightGridAccent1EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 1";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-insidev:1.0000pt solid rgb(79,129,189);
}

table.MsoTableColorfulGridAccent3{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(234,241,221);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent3FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(214,227,188);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent3LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(214,227,188);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent3FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(118,146,60);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent3LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(118,146,60);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent3OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableColorfulGridAccent3OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableMediumGrid3Accent3{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(230,238,213);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent3FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent3LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent3FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent3LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent3OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(205,221,172);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent3OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(205,221,172);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableMediumList2Accent5{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-left-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-border-right-alt:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent5FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent5LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent5FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent5LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent5OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent5OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent5NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent5SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 5";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableMediumShading1Accent1{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(123,160,205);
mso-border-left-alt:1.0000pt solid rgb(123,160,205);
mso-border-bottom-alt:1.0000pt solid rgb(123,160,205);
mso-border-right-alt:1.0000pt solid rgb(123,160,205);
mso-border-insideh:1.0000pt solid rgb(123,160,205);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent1FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-left:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-bottom:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-right:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent1LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(123,160,205);
mso-tstyle-border-left:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-bottom:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-right:1.0000pt solid rgb(123,160,205);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent1FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent1LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent1OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
}

table.MsoTableMediumShading1Accent1OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent1EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 1";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableGrid{
mso-style-name:网格型;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:0.5000pt solid windowtext;
mso-border-left-alt:0.5000pt solid windowtext;
mso-border-bottom-alt:0.5000pt solid windowtext;
mso-border-right-alt:0.5000pt solid windowtext;
mso-border-insideh:0.5000pt solid windowtext;
mso-border-insidev:0.5000pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent4{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-left-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-border-right-alt:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent4FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent4LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent4FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent4LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent4OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent4OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent4NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent4SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 4";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableMediumShading1Accent6{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(249,176,116);
mso-border-left-alt:1.0000pt solid rgb(249,176,116);
mso-border-bottom-alt:1.0000pt solid rgb(249,176,116);
mso-border-right-alt:1.0000pt solid rgb(249,176,116);
mso-border-insideh:1.0000pt solid rgb(249,176,116);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent6FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-left:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-bottom:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-right:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent6LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(249,176,116);
mso-tstyle-border-left:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-bottom:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-right:1.0000pt solid rgb(249,176,116);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent6FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent6LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent6OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
}

table.MsoTableMediumShading1Accent6OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent6EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 6";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent1{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent1FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent1LastRow{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(36,63,97);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent1FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(54,96,145);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent1LastCol{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(54,96,145);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent1OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(54,96,145);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent1OddRow{
mso-style-name:"深色列表 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(54,96,145);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid1Accent1{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(211,223,238);
mso-border-top-alt:1.0000pt solid rgb(123,160,205);
mso-border-left-alt:1.0000pt solid rgb(123,160,205);
mso-border-bottom-alt:1.0000pt solid rgb(123,160,205);
mso-border-right-alt:1.0000pt solid rgb(123,160,205);
mso-border-insideh:1.0000pt solid rgb(123,160,205);
mso-border-insidev:1.0000pt solid rgb(123,160,205);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent1FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent1LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(123,160,205);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent1FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent1LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent1OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableMediumGrid1Accent1OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableMediumShading2Accent2{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent2FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent2LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent2FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent2LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent2OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent2OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent2NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent2NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 2";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent4{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(229,223,236);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent4FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(204,192,217);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent4LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(204,192,217);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent4FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(95,73,122);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent4LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(95,73,122);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent4OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableColorfulGridAccent4OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableLightShading{
mso-style-name:浅色底纹;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingFirstRow{
mso-style-name:浅色底纹;
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingLastRow{
mso-style-name:浅色底纹;
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingFirstCol{
mso-style-name:浅色底纹;
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingLastCol{
mso-style-name:浅色底纹;
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingOddColumn{
mso-style-name:浅色底纹;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingOddRow{
mso-style-name:浅色底纹;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightGrid{
mso-style-name:浅色网格;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-left-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-border-right-alt:1.0000pt solid rgb(0,0,0);
mso-border-insideh:1.0000pt solid rgb(0,0,0);
mso-border-insidev:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridFirstRow{
mso-style-name:浅色网格;
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:2.2500pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(0,0,0);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridLastRow{
mso-style-name:浅色网格;
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(0,0,0);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridFirstCol{
mso-style-name:浅色网格;
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridLastCol{
mso-style-name:浅色网格;
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridOddColumn{
mso-style-name:浅色网格;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
}

table.MsoTableLightGridOddRow{
mso-style-name:浅色网格;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-insidev:1.0000pt solid rgb(0,0,0);
}

table.MsoTableLightGridEvenRow{
mso-style-name:浅色网格;
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-insidev:1.0000pt solid rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent6{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(254,244,236);
mso-border-top-alt:3.0000pt solid rgb(75,172,198);
mso-border-left-alt:0.5000pt solid rgb(247,150,70);
mso-border-bottom-alt:0.5000pt solid rgb(247,150,70);
mso-border-right-alt:0.5000pt solid rgb(247,150,70);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent6FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent6LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(182,86,7);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent6FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(182,86,7);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(182,86,7);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent6LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(182,86,7);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent6OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(251,212,180);
}

table.MsoTableColorfulShadingAccent6OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableColorfulShadingAccent6NECell{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent6NWCell{
mso-style-name:"彩色底纹 - 强调文字颜色 6";
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableLightShadingAccent1{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(54,96,145);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent1FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent1LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent1FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent1LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent1OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent1OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent2{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(148,55,52);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent2FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent2LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent2FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent2LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent2OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent2OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList1Accent2{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent2FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent2LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent2FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent2LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent2OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
}

table.MsoTableMediumList1Accent2OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(239,211,211);
}

table.MsoTableColorfulListAccent2{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(248,237,237);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent2FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(158,58,56);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent2LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(158,58,56);
font-weight:bold;
}

table.MsoTableColorfulListAccent2FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent2LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent2OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent2OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(242,219,219);
}

table.MsoTableLightShadingAccent3{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(118,146,60);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent3FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent3OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent3{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(95,73,122);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent3FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent3OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent3OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent5{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(49,132,155);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent5FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent5LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent5FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent5LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent5OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent5OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList1Accent5{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent5FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent5LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent5FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent5LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent5OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
}

table.MsoTableMediumList1Accent5OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(210,234,240);
}

table.MsoTableLightShadingAccent6{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(227,108,9);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightShadingAccent6FirstRow{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent6LastRow{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent6FirstCol{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent6LastCol{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightShadingAccent6OddColumn{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightShadingAccent6OddRow{
mso-style-name:"浅色底纹 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableLightList{
mso-style-name:浅色列表;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-left-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-border-right-alt:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListFirstRow{
mso-style-name:浅色列表;
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListLastRow{
mso-style-name:浅色列表;
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListFirstCol{
mso-style-name:浅色列表;
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListLastCol{
mso-style-name:浅色列表;
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListOddColumn{
mso-style-name:浅色列表;
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
}

table.MsoTableLightListOddRow{
mso-style-name:浅色列表;
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
}

table.MsoTableLightListAccent1{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-left-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-border-right-alt:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent1FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(79,129,189);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent1LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent1FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent1LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent1OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
}

table.MsoTableLightListAccent1OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
}

table.MsoTableLightListAccent2{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-left-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-border-right-alt:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent2FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(192,80,77);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent2LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent2FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent2LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent2OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
}

table.MsoTableLightListAccent2OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
}

table.MsoTableLightListAccent3{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-left-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-border-right-alt:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent3FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(155,187,89);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent3LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent3FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent3LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent3OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
}

table.MsoTableLightListAccent3OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
}

table.MsoTableMediumGrid3{
mso-style-name:"中等深浅网格 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(191,191,191);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3FirstRow{
mso-style-name:"中等深浅网格 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3LastRow{
mso-style-name:"中等深浅网格 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3FirstCol{
mso-style-name:"中等深浅网格 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3LastCol{
mso-style-name:"中等深浅网格 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3OddColumn{
mso-style-name:"中等深浅网格 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(127,127,127);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3OddRow{
mso-style-name:"中等深浅网格 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(127,127,127);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableLightListAccent4{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-left-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-border-right-alt:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent4FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(128,100,162);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent4LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent4FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent4LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent4OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
}

table.MsoTableLightListAccent4OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
}

table.MsoTableLightListAccent5{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-left-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-border-right-alt:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent5FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(75,172,198);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent5LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent5FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent5LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent5OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
}

table.MsoTableLightListAccent5OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
}

table.MsoTableLightListAccent6{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-left-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-border-right-alt:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightListAccent6FirstRow{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(247,150,70);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableLightListAccent6LastRow{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent6FirstCol{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent6LastCol{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightListAccent6OddColumn{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
}

table.MsoTableLightListAccent6OddRow{
mso-style-name:"浅色列表 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
}

table.MsoTableLightGridAccent2{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-left-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-border-right-alt:1.0000pt solid rgb(192,80,77);
mso-border-insideh:1.0000pt solid rgb(192,80,77);
mso-border-insidev:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent2FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:2.2500pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(192,80,77);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent2LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(192,80,77);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent2FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent2LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent2OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
}

table.MsoTableLightGridAccent2OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-insidev:1.0000pt solid rgb(192,80,77);
}

table.MsoTableLightGridAccent2EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 2";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-insidev:1.0000pt solid rgb(192,80,77);
}

table.MsoTableLightGridAccent3{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-left-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-border-right-alt:1.0000pt solid rgb(155,187,89);
mso-border-insideh:1.0000pt solid rgb(155,187,89);
mso-border-insidev:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent3FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:2.2500pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(155,187,89);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent3LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(155,187,89);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent3FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent3LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent3OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
}

table.MsoTableLightGridAccent3OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-insidev:1.0000pt solid rgb(155,187,89);
}

table.MsoTableLightGridAccent3EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 3";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-insidev:1.0000pt solid rgb(155,187,89);
}

table.MsoTableMediumGrid2Accent6{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(253,229,209);
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-left-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-border-right-alt:1.0000pt solid rgb(247,150,70);
mso-border-insideh:1.0000pt solid rgb(247,150,70);
mso-border-insidev:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent6FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(254,244,236);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent6LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent6FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent6LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(253,233,217);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent6OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableMediumGrid2Accent6OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(251,202,162);
mso-tstyle-border-insideh:0.7500pt solid rgb(247,150,70);
mso-tstyle-border-insidev:0.7500pt solid rgb(247,150,70);
}

table.MsoTableMediumGrid2Accent6NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 6";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid3Accent5{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(210,234,240);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent5FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent5LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent5FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent5LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent5OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(165,213,226);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent5OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(165,213,226);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableLightGridAccent4{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-left-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-border-right-alt:1.0000pt solid rgb(128,100,162);
mso-border-insideh:1.0000pt solid rgb(128,100,162);
mso-border-insidev:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent4FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:2.2500pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(128,100,162);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent4LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(128,100,162);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent4FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent4LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent4OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
}

table.MsoTableLightGridAccent4OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-insidev:1.0000pt solid rgb(128,100,162);
}

table.MsoTableLightGridAccent4EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 4";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-insidev:1.0000pt solid rgb(128,100,162);
}

table.MsoTableLightGridAccent5{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-left-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-border-right-alt:1.0000pt solid rgb(75,172,198);
mso-border-insideh:1.0000pt solid rgb(75,172,198);
mso-border-insidev:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableLightGridAccent5FirstRow{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:2.2500pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(75,172,198);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent5LastRow{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(75,172,198);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent5FirstCol{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent5LastCol{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableLightGridAccent5OddColumn{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
}

table.MsoTableLightGridAccent5OddRow{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-insidev:1.0000pt solid rgb(75,172,198);
}

table.MsoTableLightGridAccent5EvenRow{
mso-style-name:"浅色网格 - 强调文字颜色 5";
mso-table-condition:even-row;
mso-tstyle-border-top:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-left:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-bottom:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-right:1.0000pt solid rgb(75,172,198);
mso-tstyle-border-insidev:1.0000pt solid rgb(75,172,198);
}

table.MsoTableMediumGrid1Accent5{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(210,234,240);
mso-border-top-alt:1.0000pt solid rgb(120,192,212);
mso-border-left-alt:1.0000pt solid rgb(120,192,212);
mso-border-bottom-alt:1.0000pt solid rgb(120,192,212);
mso-border-right-alt:1.0000pt solid rgb(120,192,212);
mso-border-insideh:1.0000pt solid rgb(120,192,212);
mso-border-insidev:1.0000pt solid rgb(120,192,212);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent5FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent5LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(120,192,212);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent5FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent5LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent5OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(165,213,226);
}

table.MsoTableMediumGrid1Accent5OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(165,213,226);
}

table.MsoTableColorfulListAccent4{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(242,239,245);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent4FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(126,156,64);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent4LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(126,156,64);
font-weight:bold;
}

table.MsoTableColorfulListAccent4FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent4LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent4OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent4OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(229,223,236);
}

table.MsoTableMediumShading1{
mso-style-name:"中等深浅底纹 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(63,63,63);
mso-border-left-alt:1.0000pt solid rgb(63,63,63);
mso-border-bottom-alt:1.0000pt solid rgb(63,63,63);
mso-border-right-alt:1.0000pt solid rgb(63,63,63);
mso-border-insideh:1.0000pt solid rgb(63,63,63);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1FirstRow{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-left:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-bottom:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-right:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1LastRow{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(63,63,63);
mso-tstyle-border-left:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-bottom:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-right:1.0000pt solid rgb(63,63,63);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1FirstCol{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1LastCol{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1OddColumn{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
}

table.MsoTableMediumShading1OddRow{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1EvenRow{
mso-style-name:"中等深浅底纹 1";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent2{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(207,123,121);
mso-border-left-alt:1.0000pt solid rgb(207,123,121);
mso-border-bottom-alt:1.0000pt solid rgb(207,123,121);
mso-border-right-alt:1.0000pt solid rgb(207,123,121);
mso-border-insideh:1.0000pt solid rgb(207,123,121);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent2FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-left:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-bottom:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-right:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent2LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(207,123,121);
mso-tstyle-border-left:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-bottom:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-right:1.0000pt solid rgb(207,123,121);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent2FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent2LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent2OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
}

table.MsoTableMediumShading1Accent2OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent2EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 2";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent3{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(180,204,130);
mso-border-left-alt:1.0000pt solid rgb(180,204,130);
mso-border-bottom-alt:1.0000pt solid rgb(180,204,130);
mso-border-right-alt:1.0000pt solid rgb(180,204,130);
mso-border-insideh:1.0000pt solid rgb(180,204,130);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent3FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-left:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-bottom:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-right:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent3LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(180,204,130);
mso-tstyle-border-left:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-bottom:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-right:1.0000pt solid rgb(180,204,130);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent3FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent3LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent3OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
}

table.MsoTableMediumShading1Accent3OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent3EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 3";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent4{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(159,138,185);
mso-border-left-alt:1.0000pt solid rgb(159,138,185);
mso-border-bottom-alt:1.0000pt solid rgb(159,138,185);
mso-border-right-alt:1.0000pt solid rgb(159,138,185);
mso-border-insideh:1.0000pt solid rgb(159,138,185);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent4FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-left:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-bottom:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-right:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent4LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(159,138,185);
mso-tstyle-border-left:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-bottom:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-right:1.0000pt solid rgb(159,138,185);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent4FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent4LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent4OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
}

table.MsoTableMediumShading1Accent4OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,216,232);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent4EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 4";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent5{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(120,192,212);
mso-border-left-alt:1.0000pt solid rgb(120,192,212);
mso-border-bottom-alt:1.0000pt solid rgb(120,192,212);
mso-border-right-alt:1.0000pt solid rgb(120,192,212);
mso-border-insideh:1.0000pt solid rgb(120,192,212);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading1Accent5FirstRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-left:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-bottom:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-right:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading1Accent5LastRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-border-top:0.7500pt double rgb(120,192,212);
mso-tstyle-border-left:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-bottom:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-right:1.0000pt solid rgb(120,192,212);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent5FirstCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent5LastCol{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumShading1Accent5OddColumn{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
}

table.MsoTableMediumShading1Accent5OddRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading1Accent5EvenRow{
mso-style-name:"中等深浅底纹 1 - 强调文字颜色 5";
mso-table-condition:even-row;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList1Accent6{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent6FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent6LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent6FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent6LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:1.0000pt solid rgb(247,150,70);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent6OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
}

table.MsoTableMediumList1Accent6OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,229,209);
}

table.MsoTableMediumShading2{
mso-style-name:"中等深浅底纹 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2FirstRow{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2LastRow{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2FirstCol{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2LastCol{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2OddColumn{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2OddRow{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2NECell{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2NWCell{
mso-style-name:"中等深浅底纹 2";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGrid{
mso-style-name:彩色网格;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(204,204,204);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridFirstRow{
mso-style-name:彩色网格;
mso-table-condition:first-row;
mso-tstyle-shading:rgb(153,153,153);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridLastRow{
mso-style-name:彩色网格;
mso-table-condition:last-row;
mso-tstyle-shading:rgb(153,153,153);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridFirstCol{
mso-style-name:彩色网格;
mso-table-condition:first-column;
mso-tstyle-shading:rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridLastCol{
mso-style-name:彩色网格;
mso-table-condition:last-column;
mso-tstyle-shading:rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridOddColumn{
mso-style-name:彩色网格;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableColorfulGridOddRow{
mso-style-name:彩色网格;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableMediumShading2Accent1{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent1FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent1LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent1FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent1LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent1OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent1OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent1NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent1NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 1";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableMediumGrid1Accent4{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(223,216,232);
mso-border-top-alt:1.0000pt solid rgb(159,138,185);
mso-border-left-alt:1.0000pt solid rgb(159,138,185);
mso-border-bottom-alt:1.0000pt solid rgb(159,138,185);
mso-border-right-alt:1.0000pt solid rgb(159,138,185);
mso-border-insideh:1.0000pt solid rgb(159,138,185);
mso-border-insidev:1.0000pt solid rgb(159,138,185);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent4FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent4LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(159,138,185);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent4FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent4LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent4OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableMediumGrid1Accent4OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableMediumShading2Accent3{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent3FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent3LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent3FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent3LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(155,187,89);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent3OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent3OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent3NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent3NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 3";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableMediumShading2Accent4{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent4FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent4LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent4FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent4LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent4OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent4OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent4NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent4NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 4";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableMediumShading2Accent5{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent5FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent5LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent5FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent5LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(75,172,198);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent5OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent5OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent5NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent5NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 5";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableMediumShading2Accent6{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:2.2500pt solid windowtext;
mso-border-bottom-alt:2.2500pt solid windowtext;
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumShading2Accent6FirstRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent6LastRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:0.7500pt double windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
}

table.MsoTableMediumShading2Accent6FirstCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent6LastCol{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableMediumShading2Accent6OddColumn{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(215,215,215);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent6OddRow{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(215,215,215);
}

table.MsoTableMediumShading2Accent6NECell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:ne-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumShading2Accent6NWCell{
mso-style-name:"中等深浅底纹 2 - 强调文字颜色 6";
mso-table-condition:nw-cell;
mso-tstyle-border-top:2.2500pt solid windowtext;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid windowtext;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableMediumList1{
mso-style-name:"中等深浅列表 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1FirstRow{
mso-style-name:"中等深浅列表 1";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1LastRow{
mso-style-name:"中等深浅列表 1";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1FirstCol{
mso-style-name:"中等深浅列表 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1LastCol{
mso-style-name:"中等深浅列表 1";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:1.0000pt solid rgb(0,0,0);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1OddColumn{
mso-style-name:"中等深浅列表 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
}

table.MsoTableMediumList1OddRow{
mso-style-name:"中等深浅列表 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,191,191);
}

table.MsoTableMediumList1Accent1{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent1FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent1LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent1FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent1LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:1.0000pt solid rgb(79,129,189);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent1OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
}

table.MsoTableMediumList1Accent1OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(211,223,238);
}

table.MsoTableMediumList1Accent3{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent3FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent3LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent3FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent3LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:1.0000pt solid rgb(155,187,89);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent3OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
}

table.MsoTableMediumList1Accent3OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(230,238,213);
}

table.MsoTableMediumList1Accent4{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList1Accent4FirstRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
}

table.MsoTableMediumList1Accent4LastRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
font-family:'Times New Roman';
color:rgb(31,73,125);
font-weight:bold;
}

table.MsoTableMediumList1Accent4FirstCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent4LastCol{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-border-top:1.0000pt solid rgb(128,100,162);
mso-tstyle-border-bottom:1.0000pt solid rgb(128,100,162);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumList1Accent4OddColumn{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,216,232);
}

table.MsoTableMediumList1Accent4OddRow{
mso-style-name:"中等深浅列表 1 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,216,232);
}

table.MsoTableMediumList2{
mso-style-name:"中等深浅列表 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-left-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-border-right-alt:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2FirstRow{
mso-style-name:"中等深浅列表 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(0,0,0);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2LastRow{
mso-style-name:"中等深浅列表 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2FirstCol{
mso-style-name:"中等深浅列表 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2LastCol{
mso-style-name:"中等深浅列表 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(0,0,0);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2OddColumn{
mso-style-name:"中等深浅列表 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2OddRow{
mso-style-name:"中等深浅列表 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2NWCell{
mso-style-name:"中等深浅列表 2";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2SWCell{
mso-style-name:"中等深浅列表 2";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableMediumList2Accent1{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-left-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-border-right-alt:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent1FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(79,129,189);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent1LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent1FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent1LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(79,129,189);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent1OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent1OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent1NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent1SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 1";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableColorfulListAccent3{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(245,248,238);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent3FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(102,78,130);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent3LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(102,78,130);
font-weight:bold;
}

table.MsoTableColorfulListAccent3FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent3LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent3OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent3OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(234,241,221);
}

table.MsoTableMediumList2Accent2{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-left-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-border-right-alt:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent2FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent2LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent2FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent2LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(192,80,77);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent2OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent2OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(239,211,211);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent2NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent2SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 2";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableMediumList2Accent3{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-left-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-border-right-alt:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent3FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent3LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent3FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent3LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(155,187,89);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent3OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent3OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(230,238,213);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent3NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent3SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 3";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableDarkListAccent5{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent5FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent5LastRow{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(32,88,103);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent5FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(49,132,155);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent5LastCol{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(49,132,155);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent5OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(49,132,155);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent5OddRow{
mso-style-name:"深色列表 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(49,132,155);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-border-top-alt:1.0000pt solid rgb(247,150,70);
mso-border-left-alt:1.0000pt solid rgb(247,150,70);
mso-border-bottom-alt:1.0000pt solid rgb(247,150,70);
mso-border-right-alt:1.0000pt solid rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumList2Accent6FirstRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-size:12.0000pt;
}

table.MsoTableMediumList2Accent6LastRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6FirstCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6LastCol{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:1.0000pt solid rgb(247,150,70);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6OddColumn{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-left:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6OddRow{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-top:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumList2Accent6NWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumList2Accent6SWCell{
mso-style-name:"中等深浅列表 2 - 强调文字颜色 6";
mso-table-condition:sw-cell;
mso-tstyle-border-top:none;
}

table.MsoTableMediumGrid1{
mso-style-name:"中等深浅网格 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(191,191,191);
mso-border-top-alt:1.0000pt solid rgb(63,63,63);
mso-border-left-alt:1.0000pt solid rgb(63,63,63);
mso-border-bottom-alt:1.0000pt solid rgb(63,63,63);
mso-border-right-alt:1.0000pt solid rgb(63,63,63);
mso-border-insideh:1.0000pt solid rgb(63,63,63);
mso-border-insidev:1.0000pt solid rgb(63,63,63);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1FirstRow{
mso-style-name:"中等深浅网格 1";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1LastRow{
mso-style-name:"中等深浅网格 1";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(63,63,63);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1FirstCol{
mso-style-name:"中等深浅网格 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1LastCol{
mso-style-name:"中等深浅网格 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1OddColumn{
mso-style-name:"中等深浅网格 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableMediumGrid1OddRow{
mso-style-name:"中等深浅网格 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableMediumGrid1Accent2{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(239,211,211);
mso-border-top-alt:1.0000pt solid rgb(207,123,121);
mso-border-left-alt:1.0000pt solid rgb(207,123,121);
mso-border-bottom-alt:1.0000pt solid rgb(207,123,121);
mso-border-right-alt:1.0000pt solid rgb(207,123,121);
mso-border-insideh:1.0000pt solid rgb(207,123,121);
mso-border-insidev:1.0000pt solid rgb(207,123,121);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent2FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent2LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(207,123,121);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent2FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent2LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent2OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableMediumGrid1Accent2OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableColorfulShadingAccent1{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(237,242,248);
mso-border-top-alt:3.0000pt solid rgb(192,80,77);
mso-border-left-alt:0.5000pt solid rgb(79,129,189);
mso-border-bottom-alt:0.5000pt solid rgb(79,129,189);
mso-border-right-alt:0.5000pt solid rgb(79,129,189);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent1FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent1LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(43,77,116);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent1FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(43,77,116);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(43,77,116);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent1LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(43,77,116);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent1OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(184,204,228);
}

table.MsoTableColorfulShadingAccent1OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableColorfulShadingAccent1NECell{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent1NWCell{
mso-style-name:"彩色底纹 - 强调文字颜色 1";
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableMediumGrid1Accent3{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(230,238,213);
mso-border-top-alt:1.0000pt solid rgb(180,204,130);
mso-border-left-alt:1.0000pt solid rgb(180,204,130);
mso-border-bottom-alt:1.0000pt solid rgb(180,204,130);
mso-border-right-alt:1.0000pt solid rgb(180,204,130);
mso-border-insideh:1.0000pt solid rgb(180,204,130);
mso-border-insidev:1.0000pt solid rgb(180,204,130);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent3FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent3LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(180,204,130);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent3FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent3LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent3OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableMediumGrid1Accent3OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableMediumGrid1Accent6{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(253,229,209);
mso-border-top-alt:1.0000pt solid rgb(249,176,116);
mso-border-left-alt:1.0000pt solid rgb(249,176,116);
mso-border-bottom-alt:1.0000pt solid rgb(249,176,116);
mso-border-right-alt:1.0000pt solid rgb(249,176,116);
mso-border-insideh:1.0000pt solid rgb(249,176,116);
mso-border-insidev:1.0000pt solid rgb(249,176,116);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid1Accent6FirstRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:first-row;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent6LastRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-border-top:2.2500pt solid rgb(249,176,116);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent6FirstCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent6LastCol{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableMediumGrid1Accent6OddColumn{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableMediumGrid1Accent6OddRow{
mso-style-name:"中等深浅网格 1 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableMediumGrid2Accent3{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(230,238,213);
mso-border-top-alt:1.0000pt solid rgb(155,187,89);
mso-border-left-alt:1.0000pt solid rgb(155,187,89);
mso-border-bottom-alt:1.0000pt solid rgb(155,187,89);
mso-border-right-alt:1.0000pt solid rgb(155,187,89);
mso-border-insideh:1.0000pt solid rgb(155,187,89);
mso-border-insidev:1.0000pt solid rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent3FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(245,248,238);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent3LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent3FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent3LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(234,241,221);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent3OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableMediumGrid2Accent3OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(205,221,172);
mso-tstyle-border-insideh:0.7500pt solid rgb(155,187,89);
mso-tstyle-border-insidev:0.7500pt solid rgb(155,187,89);
}

table.MsoTableMediumGrid2Accent3NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 3";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid2{
mso-style-name:"中等深浅网格 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(191,191,191);
mso-border-top-alt:1.0000pt solid rgb(0,0,0);
mso-border-left-alt:1.0000pt solid rgb(0,0,0);
mso-border-bottom-alt:1.0000pt solid rgb(0,0,0);
mso-border-right-alt:1.0000pt solid rgb(0,0,0);
mso-border-insideh:1.0000pt solid rgb(0,0,0);
mso-border-insidev:1.0000pt solid rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2FirstRow{
mso-style-name:"中等深浅网格 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(229,229,229);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2LastRow{
mso-style-name:"中等深浅网格 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2FirstCol{
mso-style-name:"中等深浅网格 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2LastCol{
mso-style-name:"中等深浅网格 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(204,204,204);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2OddColumn{
mso-style-name:"中等深浅网格 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableMediumGrid2OddRow{
mso-style-name:"中等深浅网格 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(127,127,127);
mso-tstyle-border-insideh:0.7500pt solid rgb(0,0,0);
mso-tstyle-border-insidev:0.7500pt solid rgb(0,0,0);
}

table.MsoTableMediumGrid2NWCell{
mso-style-name:"中等深浅网格 2";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent2{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(248,237,237);
mso-border-top-alt:3.0000pt solid rgb(192,80,77);
mso-border-left-alt:0.5000pt solid rgb(192,80,77);
mso-border-bottom-alt:0.5000pt solid rgb(192,80,77);
mso-border-right-alt:0.5000pt solid rgb(192,80,77);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent2FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent2LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(119,44,42);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent2FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(119,44,42);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(119,44,42);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent2LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(119,44,42);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent2OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(229,184,183);
}

table.MsoTableColorfulShadingAccent2OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableColorfulShadingAccent2NECell{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent2NWCell{
mso-style-name:"彩色底纹 - 强调文字颜色 2";
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulListAccent5{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(237,246,249);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent5FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(243,115,10);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent5LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(243,115,10);
font-weight:bold;
}

table.MsoTableColorfulListAccent5FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent5LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent5OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(210,234,240);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent5OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(218,238,243);
}

table.MsoTableMediumGrid2Accent1{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(211,223,238);
mso-border-top-alt:1.0000pt solid rgb(79,129,189);
mso-border-left-alt:1.0000pt solid rgb(79,129,189);
mso-border-bottom-alt:1.0000pt solid rgb(79,129,189);
mso-border-right-alt:1.0000pt solid rgb(79,129,189);
mso-border-insideh:1.0000pt solid rgb(79,129,189);
mso-border-insidev:1.0000pt solid rgb(79,129,189);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent1FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(237,242,248);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent1LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent1FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent1LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(219,229,241);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent1OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableMediumGrid2Accent1OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(167,192,222);
mso-tstyle-border-insideh:0.7500pt solid rgb(79,129,189);
mso-tstyle-border-insidev:0.7500pt solid rgb(79,129,189);
}

table.MsoTableMediumGrid2Accent1NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 1";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid2Accent2{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(239,211,211);
mso-border-top-alt:1.0000pt solid rgb(192,80,77);
mso-border-left-alt:1.0000pt solid rgb(192,80,77);
mso-border-bottom-alt:1.0000pt solid rgb(192,80,77);
mso-border-right-alt:1.0000pt solid rgb(192,80,77);
mso-border-insideh:1.0000pt solid rgb(192,80,77);
mso-border-insidev:1.0000pt solid rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent2FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(248,237,237);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent2LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent2FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent2LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(242,219,219);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent2OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableMediumGrid2Accent2OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,167,166);
mso-tstyle-border-insideh:0.7500pt solid rgb(192,80,77);
mso-tstyle-border-insidev:0.7500pt solid rgb(192,80,77);
}

table.MsoTableMediumGrid2Accent2NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 2";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid2Accent4{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(223,216,232);
mso-border-top-alt:1.0000pt solid rgb(128,100,162);
mso-border-left-alt:1.0000pt solid rgb(128,100,162);
mso-border-bottom-alt:1.0000pt solid rgb(128,100,162);
mso-border-right-alt:1.0000pt solid rgb(128,100,162);
mso-border-insideh:1.0000pt solid rgb(128,100,162);
mso-border-insidev:1.0000pt solid rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent4FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(242,239,245);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent4LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent4FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent4LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(229,223,236);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent4OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableMediumGrid2Accent4OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,177,208);
mso-tstyle-border-insideh:0.7500pt solid rgb(128,100,162);
mso-tstyle-border-insidev:0.7500pt solid rgb(128,100,162);
}

table.MsoTableMediumGrid2Accent4NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 4";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid2Accent5{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(210,234,240);
mso-border-top-alt:1.0000pt solid rgb(75,172,198);
mso-border-left-alt:1.0000pt solid rgb(75,172,198);
mso-border-bottom-alt:1.0000pt solid rgb(75,172,198);
mso-border-right-alt:1.0000pt solid rgb(75,172,198);
mso-border-insideh:1.0000pt solid rgb(75,172,198);
mso-border-insidev:1.0000pt solid rgb(75,172,198);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:Calibri;
mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid2Accent5FirstRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(237,246,249);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent5LastRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent5FirstCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableMediumGrid2Accent5LastCol{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(218,238,243);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:normal;
}

table.MsoTableMediumGrid2Accent5OddColumn{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(165,213,226);
}

table.MsoTableMediumGrid2Accent5OddRow{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(165,213,226);
mso-tstyle-border-insideh:0.7500pt solid rgb(75,172,198);
mso-tstyle-border-insidev:0.7500pt solid rgb(75,172,198);
}

table.MsoTableMediumGrid2Accent5NWCell{
mso-style-name:"中等深浅网格 2 - 强调文字颜色 5";
mso-table-condition:nw-cell;
mso-tstyle-shading:rgb(255,255,255);
}

table.MsoTableMediumGrid3Accent1{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(211,223,238);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent1FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent1LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent1FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent1LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(79,129,189);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent1OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(167,192,222);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent1OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(167,192,222);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableMediumGrid3Accent2{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(239,211,211);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent2FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent2LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent2FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent2LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(192,80,77);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent2OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,167,166);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent2OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,167,166);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableMediumGrid3Accent4{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(223,216,232);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent4FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent4LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent4FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent4LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(128,100,162);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent4OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,177,208);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent4OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,177,208);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableColorfulGridAccent2{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(242,219,219);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent2FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(229,184,183);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent2LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(229,184,183);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent2FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(148,55,52);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent2LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(148,55,52);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent2OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableColorfulGridAccent2OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(223,167,166);
}

table.MsoTableMediumGrid3Accent6{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(253,229,209);
mso-border-top-alt:1.0000pt solid rgb(255,255,255);
mso-border-left-alt:1.0000pt solid rgb(255,255,255);
mso-border-bottom-alt:1.0000pt solid rgb(255,255,255);
mso-border-right-alt:1.0000pt solid rgb(255,255,255);
mso-border-insideh:0.7500pt solid rgb(255,255,255);
mso-border-insidev:0.7500pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableMediumGrid3Accent6FirstRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent6LastRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent6FirstCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent6LastCol{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(247,150,70);
mso-tstyle-border-top:none;
mso-tstyle-border-left:3.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
font-style:normal;
}

table.MsoTableMediumGrid3Accent6OddColumn{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(251,202,162);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableMediumGrid3Accent6OddRow{
mso-style-name:"中等深浅网格 3 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(251,202,162);
mso-tstyle-border-top:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-left:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-bottom:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-right:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insideh:1.0000pt solid rgb(255,255,255);
mso-tstyle-border-insidev:1.0000pt solid rgb(255,255,255);
}

table.MsoTableDarkList{
mso-style-name:深色列表;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(0,0,0);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListFirstRow{
mso-style-name:深色列表;
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListLastRow{
mso-style-name:深色列表;
mso-table-condition:last-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListFirstCol{
mso-style-name:深色列表;
mso-table-condition:first-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListLastCol{
mso-style-name:深色列表;
mso-table-condition:last-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListOddColumn{
mso-style-name:深色列表;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListOddRow{
mso-style-name:深色列表;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent2{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(192,80,77);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent2FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent2LastRow{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(98,36,35);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent2FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(148,55,52);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent2LastCol{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(148,55,52);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent2OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(148,55,52);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent2OddRow{
mso-style-name:"深色列表 - 强调文字颜色 2";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(148,55,52);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent3{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(155,187,89);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent3FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent3LastRow{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(78,97,39);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent3FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(118,146,60);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent3LastCol{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(118,146,60);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent3OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(118,146,60);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent3OddRow{
mso-style-name:"深色列表 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(118,146,60);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent4{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(128,100,162);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent4FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent4LastRow{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(63,48,81);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent4FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(95,73,122);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent4LastCol{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(95,73,122);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent4OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(95,73,122);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent4OddRow{
mso-style-name:"深色列表 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(95,73,122);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent6{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(247,150,70);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(255,255,255);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableDarkListAccent6FirstRow{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableDarkListAccent6LastRow{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(151,71,6);
mso-tstyle-border-top:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent6FirstCol{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(227,108,9);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent6LastCol{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(227,108,9);
mso-tstyle-border-top:none;
mso-tstyle-border-left:2.2500pt solid rgb(255,255,255);
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent6OddColumn{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(227,108,9);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableDarkListAccent6OddRow{
mso-style-name:"深色列表 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(227,108,9);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulShading{
mso-style-name:彩色底纹;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(229,229,229);
mso-border-top-alt:3.0000pt solid rgb(192,80,77);
mso-border-left-alt:0.5000pt solid rgb(0,0,0);
mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);
mso-border-right-alt:0.5000pt solid rgb(0,0,0);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingFirstRow{
mso-style-name:彩色底纹;
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(192,80,77);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingLastRow{
mso-style-name:彩色底纹;
mso-table-condition:last-row;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingFirstCol{
mso-style-name:彩色底纹;
mso-table-condition:first-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(0,0,0);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingLastCol{
mso-style-name:彩色底纹;
mso-table-condition:last-column;
mso-tstyle-shading:rgb(0,0,0);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingOddColumn{
mso-style-name:彩色底纹;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(153,153,153);
}

table.MsoTableColorfulShadingOddRow{
mso-style-name:彩色底纹;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(127,127,127);
}

table.MsoTableColorfulShadingNECell{
mso-style-name:彩色底纹;
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingNWCell{
mso-style-name:彩色底纹;
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent3{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(245,248,238);
mso-border-top-alt:3.0000pt solid rgb(128,100,162);
mso-border-left-alt:0.5000pt solid rgb(155,187,89);
mso-border-bottom-alt:0.5000pt solid rgb(155,187,89);
mso-border-right-alt:0.5000pt solid rgb(155,187,89);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent3FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(128,100,162);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent3LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(94,117,48);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent3FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(94,117,48);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(94,117,48);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent3LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(94,117,48);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent3OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(214,227,188);
}

table.MsoTableColorfulShadingAccent3OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 3";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(205,221,172);
}

table.MsoTableColorfulShadingAccent4{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(242,239,245);
mso-border-top-alt:3.0000pt solid rgb(155,187,89);
mso-border-left-alt:0.5000pt solid rgb(128,100,162);
mso-border-bottom-alt:0.5000pt solid rgb(128,100,162);
mso-border-right-alt:0.5000pt solid rgb(128,100,162);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent4FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(155,187,89);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent4LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(76,58,98);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent4FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(76,58,98);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(76,58,98);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent4LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(76,58,98);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent4OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(204,192,217);
}

table.MsoTableColorfulShadingAccent4OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(191,177,208);
}

table.MsoTableColorfulShadingAccent4NECell{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent4NWCell{
mso-style-name:"彩色底纹 - 强调文字颜色 4";
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent5{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(237,246,249);
mso-border-top-alt:3.0000pt solid rgb(247,150,70);
mso-border-left-alt:0.5000pt solid rgb(75,172,198);
mso-border-bottom-alt:0.5000pt solid rgb(75,172,198);
mso-border-right-alt:0.5000pt solid rgb(75,172,198);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-border-insidev:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulShadingAccent5FirstRow{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:3.0000pt solid rgb(247,150,70);
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulShadingAccent5LastRow{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(39,106,124);
mso-tstyle-border-top:0.7500pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulShadingAccent5FirstCol{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(39,106,124);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:0.5000pt solid rgb(39,106,124);
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent5LastCol{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(39,106,124);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulShadingAccent5OddColumn{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(182,221,232);
}

table.MsoTableColorfulShadingAccent5OddRow{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(165,213,226);
}

table.MsoTableColorfulShadingAccent5NECell{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:ne-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulShadingAccent5NWCell{
mso-style-name:"彩色底纹 - 强调文字颜色 5";
mso-table-condition:nw-cell;
font-family:'Times New Roman';
color:rgb(0,0,0);
}

table.MsoTableColorfulList{
mso-style-name:彩色列表;
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(229,229,229);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListFirstRow{
mso-style-name:彩色列表;
mso-table-condition:first-row;
mso-tstyle-shading:rgb(158,58,56);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListLastRow{
mso-style-name:彩色列表;
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(158,58,56);
font-weight:bold;
}

table.MsoTableColorfulListFirstCol{
mso-style-name:彩色列表;
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListLastCol{
mso-style-name:彩色列表;
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListOddColumn{
mso-style-name:彩色列表;
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(191,191,191);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListOddRow{
mso-style-name:彩色列表;
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(204,204,204);
}

table.MsoTableColorfulListAccent1{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(237,242,248);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent1FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(158,58,56);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent1LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(158,58,56);
font-weight:bold;
}

table.MsoTableColorfulListAccent1FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent1LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent1OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(211,223,238);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent1OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(219,229,241);
}

table.MsoTableColorfulListAccent6{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(254,244,236);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulListAccent6FirstRow{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(52,141,165);
mso-tstyle-border-bottom:1.5000pt solid rgb(255,255,255);
font-family:'Times New Roman';
color:rgb(255,255,255);
font-weight:bold;
}

table.MsoTableColorfulListAccent6LastRow{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(255,255,255);
mso-tstyle-border-top:1.5000pt solid rgb(0,0,0);
font-family:'Times New Roman';
color:rgb(52,141,165);
font-weight:bold;
}

table.MsoTableColorfulListAccent6FirstCol{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:first-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent6LastCol{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:last-column;
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulListAccent6OddColumn{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(253,229,209);
mso-tstyle-border-top:none;
mso-tstyle-border-left:none;
mso-tstyle-border-bottom:none;
mso-tstyle-border-right:none;
mso-tstyle-border-insideh:none;
mso-tstyle-border-insidev:none;
}

table.MsoTableColorfulListAccent6OddRow{
mso-style-name:"彩色列表 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(253,233,217);
}

table.MsoTableColorfulGridAccent1{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(219,229,241);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent1FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(184,204,228);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent1LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(184,204,228);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent1FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(54,96,145);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent1LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(54,96,145);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent1OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableColorfulGridAccent1OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 1";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(167,192,222);
}

table.MsoTableColorfulGridAccent6{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(253,233,217);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent6FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(251,212,180);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent6LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(251,212,180);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent6FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(227,108,9);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent6LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(227,108,9);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent6OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableColorfulGridAccent6OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 6";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(251,202,162);
}

table.MsoTableColorfulGridAccent5{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-tstyle-rowband-size:0;
mso-tstyle-colband-size:0;
mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
mso-tstyle-shading:rgb(218,238,243);
mso-border-insideh:0.5000pt solid rgb(255,255,255);
mso-para-margin:0pt;
mso-para-margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-family:'Times New Roman';
color:rgb(0,0,0);
font-size:10.0000pt;
mso-ansi-language:#0400;
mso-fareast-language:#0400;
mso-bidi-language:#0400;
}

table.MsoTableColorfulGridAccent5FirstRow{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:first-row;
mso-tstyle-shading:rgb(182,221,232);
font-family:'Times New Roman';
font-weight:bold;
}

table.MsoTableColorfulGridAccent5LastRow{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:last-row;
mso-tstyle-shading:rgb(182,221,232);
font-family:'Times New Roman';
color:rgb(0,0,0);
font-weight:bold;
}

table.MsoTableColorfulGridAccent5FirstCol{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:first-column;
mso-tstyle-shading:rgb(49,132,155);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent5LastCol{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:last-column;
mso-tstyle-shading:rgb(49,132,155);
font-family:'Times New Roman';
color:rgb(255,255,255);
}

table.MsoTableColorfulGridAccent5OddColumn{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:odd-column;
mso-tstyle-shading:rgb(165,213,226);
}

table.MsoTableColorfulGridAccent5OddRow{
mso-style-name:"彩色网格 - 强调文字颜色 5";
mso-table-condition:odd-row;
mso-tstyle-shading:rgb(165,213,226);
}
@page{mso-page-border-surround-header:no;
	mso-page-border-surround-footer:no;}@page Section0{
margin-top:72.0000pt;
margin-bottom:72.0000pt;
margin-left:90.0000pt;
margin-right:90.0000pt;
size:612.0000pt 792.0000pt;
layout-grid:18.0000pt;
mso-header-margin:36.0000pt;
mso-footer-margin:36.0000pt;
}
div.Section0{page:Section0;}</style></head><body style="tab-interval:36pt;" ><!--StartFragment--><div class="Section0"  style="layout-grid:18.0000pt;" ><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:15.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Welcome to Luckyluna Technology Services!</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:15.0000pt;" >This User Agreement (hereinafter referred to as the 'Agreement') is entered into by Luckyluna Technology Co., Ltd. (hereinafter referred to as '</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:15.0000pt;" >' or 'we') and you (if the user is an individual) or the entity you represent (if the user is a legal entity). This Agreement governs your use of the products and services provided by Luckyluna Technology Co., Ltd., including its related services (hereinafter collectively referred to as 'the Service' or '</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;Services').</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Acceptance of the Agreement</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >By using our Service, you confirm your acceptance of this Agreement electronically and agree to be bound by its terms. If you or your guardian disagree with any part of this Agreement, you should immediately stop using </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;Services. If you are a minor using this Service without the consent of your legal guardian, you and your guardian shall bear all consequences arising from such use.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Section 1: Definitions</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >1.1 User: Refers to any natural person or legal entity using </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >'s Services. For clarity, users of partner services within the </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;ecosystem are also considered 'Users' under this Agreement and must comply with its terms.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >1.2 </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >: Refers to the image synthesis products developed using </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >'s proprietary algorithms and technology.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >1.3 Input: Refers to the text or data entered by the user in the dialogue box for using the Service.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >1.4 Output: Refers to the images, content, or other results generated by the Service based on the user's Input.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Section 2: Service Description</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >2.1 </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;utilizes advanced image synthesis algorithms to provide image generation products and services. Users input text or information in a dialogue box, and the Service responds with corresponding outputs, such as text or images. </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;may collect and analyze user Input and Output data to enhance the quality and intelligence of its services. Users can provide feedback on the output by actions such as liking or sharing the results.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >2.2 The specific features of the Service are subject to continuous updates and iterations based on user feedback, technological advancements, or changes in regulatory requirements.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >2.3 When using certain specific features of the Service, additional agreements or business rules (referred to as 'Specific Agreements') may apply. In the event of a conflict between this Agreement and any Specific Agreement, the terms of the Specific Agreement shall prevail.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >2.4 When using products or services provided by third parties (e.g., via plugins or links), you must comply with the third-party's user agreements, terms of use, and privacy policies. </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;shall not be held liable for third-party content or services, except as required by law.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Section 3: Account Registration and Login</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.1 To access the Service, you must complete the registration process by providing a valid account, password, and phone number. You must carefully review and agree to the terms of this User Agreement and Privacy Policy before completing the registration.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.2 The Service is primarily intended for adults. If you are under 18, you must read this Agreement with your legal guardian and obtain their consent before using the Service. Guardians should guide minors appropriately to ensure safe and responsible use of the Service.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.3 To comply with national regulations requiring real-name authentication, you must provide a valid phone number when registering. In addition, </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;may collect additional personal information in accordance with applicable laws to provide certain features.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.4 You are solely responsible for ensuring the legality of your account information, including your username, profile picture, and other personal details. The use of illegal or inappropriate content in your account is strictly prohibited. </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;reserves the right to review and verify user-submitted information.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.5 To ensure account security, </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;may periodically require identity verification through various means, such as verification codes, phone number authentication, or email verification. Failure to complete or refusal to undergo verification may result in suspension or restriction of services.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.6 Your account should not be shared with others. You are responsible for safeguarding your account credentials (e.g., username, password, verification codes). If you suspect unauthorized use, contact </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;immediately.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >3.7 You may terminate your account at any time, provided there are no unresolved transactions or obligations associated with it. Once an account is terminated, all associated data, including virtual assets, may be cleared and cannot be restored.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Section 4: User Obligations</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >4.1 Compliance with Laws</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Users must abide by all applicable laws, regulations, and policies when using the Service. The following actions are strictly prohibited:</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >- Uploading, publishing, or transmitting illegal or harmful content.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >- Infringing on the rights of others, including but not limited to intellectual property rights, privacy, and reputation.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >- Engaging in fraudulent, misleading, or malicious activities.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >4.2 Account Usage</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >The User is solely responsible for all activities conducted under their account. You must ensure that your account is not used for unauthorized purposes or by unauthorized parties.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >4.3 Content Ownership</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >While the User retains ownership of their Input, </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;reserves the right to use, analyze, and process such Input to improve its services. Users must not upload content that violates third-party rights or is otherwise prohibited by law.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >4.4 Feedback</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Users are encouraged to provide feedback on the Service to help </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;improve its features and functionality. By submitting feedback, the User grants </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;a perpetual, royalty-free license to use, modify, and implement such suggestions.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h2><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" >Section 5: Privacy Policy</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-size:13.0000pt;" ><o:p></o:p></span></h2><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >5.1 Data Collection</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;collects and processes personal information provided by Users, including but not limited to account registration details, usage patterns, and feedback. This data is used to enhance the Service's quality and performance.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >5.2 Data Protection</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;employs advanced security measures to protect User data from unauthorized access, disclosure, or misuse. However, no security system is entirely foolproof, and Users are advised to take their own precautions to safeguard personal information.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >5.3 Third-Party Sharing</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;may share User data with trusted third-party partners to provide certain services or comply with legal obligations. Such sharing is governed by strict confidentiality agreements.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >5.4 User Rights</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Users have the right to access, update, or delete their personal information in accordance with applicable laws. For any requests related to data privacy, Users may contact </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;via support@</span><span style="mso-spacerun:'yes';font-family:宋体;mso-ascii-font-family:Cambria;
mso-hansi-font-family:Cambria;mso-bidi-font-family:'Times New Roman';font-weight:normal;
font-size:11.0000pt;" ><font face="Cambria" >qiyueai</font></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >.com.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h4 style="mso-pagination:widow-orphan;" ><span class="30"  style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';font-weight:normal;" >Section 6: Service Modifications and Termination</span><i><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-style:italic;font-size:11.0000pt;" ><o:p></o:p></span></i></h4><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >6.1 Modifications</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest reserves the right to modify, suspend, or terminate the Service (or any part thereof) at its discretion, with or without prior notice.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >6.2 Termination by User</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Users may discontinue use of the Service at any time. Upon termination, all associated data and account information may be permanently deleted.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >6.3 Termination by </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest may suspend or terminate a User's account if they violate the terms of this Agreement or engage in prohibited activities.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h4 style="mso-pagination:widow-orphan;" ><span class="30"  style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';font-weight:normal;" >Section 7: Disclaimer and Limitation of Liability</span><i><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-style:italic;font-size:11.0000pt;" ><o:p></o:p></span></i></h4><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >7.1 No Warranty</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >The Service is provided "as is" and "as available." </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;makes no guarantees regarding the accuracy, reliability, or availability of the Service.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >7.2 Liability Limitation</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >To the fullest extent permitted by law, </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;shall not be liable for any indirect, incidental, or consequential damages arising from the use of the Service.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h4 style="mso-pagination:widow-orphan;" ><span class="30"  style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';font-weight:normal;" >Section 8: Governing Law and Dispute Resolution</span><i><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-style:italic;font-size:11.0000pt;" ><o:p></o:p></span></i></h4><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >8.1 Governing Law</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:'ＭＳ 明朝';mso-ascii-font-family:Cambria;
mso-hansi-font-family:Cambria;mso-bidi-font-family:'Times New Roman';font-weight:normal;
font-size:11.0000pt;" ><font face="Cambria" >This Agreement shall be governed by the laws of the People's Republic of China (mainland).</font></span><span style="mso-spacerun:'yes';font-family:'ＭＳ 明朝';mso-ascii-font-family:Cambria;
mso-hansi-font-family:Cambria;mso-bidi-font-family:'Times New Roman';font-weight:normal;
font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >8.2 Dispute Resolution</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >Any disputes arising from this Agreement shall be resolved through arbitration or mediation, as specified in the relevant legal jurisdiction.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><h4 style="mso-pagination:widow-orphan;" ><span class="30"  style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';font-weight:normal;" >Section 9: Miscellaneous</span><i><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'ＭＳ ゴシック';
mso-bidi-font-family:'Times New Roman';color:rgb(79,129,189);font-weight:normal;
font-style:italic;font-size:11.0000pt;" ><o:p></o:p></span></i></h4><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >9.1 Entire Agreement</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >This Agreement constitutes the entire understanding between the User and </span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >MovieNest</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >&nbsp;regarding the use of the Service.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >9.2 Severability</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><br></span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" >If any provision of this Agreement is found to be invalid or unenforceable, the remaining provisions shall remain in full force and effect.</span><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal ><span style="mso-spacerun:'yes';font-family:Cambria;mso-fareast-font-family:'ＭＳ 明朝';
mso-bidi-font-family:'Times New Roman';font-weight:normal;font-size:11.0000pt;" ><o:p>&nbsp;</o:p></span></p></div><!--EndFragment--></body></html>