import 'package:logger/logger.dart';
import 'package:pieces_ai/utils/http_utils/http_util.dart';
import '../../model/user/user_resp.dart';

var logger = Logger(printer: PrettyPrinter());

/// 登录接口类
class HttpsUser {
  static const String loginUrl = '/movie/nest/login';

  /// 登录方法
  Future<UserRespVo?> login(String email, String tid) async {
    try {
      // 构建请求参数
      Map<String, dynamic> param = await HttpUtil.withBaseParam();
      param['spec'] = {
        "email": email,
        "tid": tid,
      };

      // 发送POST请求
      var result = await HttpUtil.instance.client.post(
        HttpUtil.apiBaseUrl + loginUrl,
        data: param,
      );

      logger.d("登录请求参数：${param.toString()}");
      logger.d("登录响应值：${result}");

      // 检查响应数据
      if (result.data != null && result.data['code'] == 200) {
        logger.d("登录成功，返回数据：${result.data['data'].toString()}");

        // 解析并返回用户信息
        return UserRespVo.fromJson(result.data['data']);
      } else {
        logger.e("登录失败，错误信息：${result.data['message']}");
        return null;
      }
    } catch (e) {
      logger.e("登录异常：$e");
      return null;
    }
  }
}
