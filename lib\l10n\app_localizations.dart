import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja'),
    Locale('zh')
  ];

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get create;

  /// No description provided for @goods.
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get goods;

  /// No description provided for @me.
  ///
  /// In en, this message translates to:
  /// **'Me'**
  String get me;

  /// No description provided for @agree.
  ///
  /// In en, this message translates to:
  /// **'agree'**
  String get agree;

  /// No description provided for @disagree.
  ///
  /// In en, this message translates to:
  /// **'disagree'**
  String get disagree;

  /// No description provided for @mainOneClick.
  ///
  /// In en, this message translates to:
  /// **'One-click to create'**
  String get mainOneClick;

  /// No description provided for @drafts.
  ///
  /// In en, this message translates to:
  /// **'Drafts'**
  String get drafts;

  /// No description provided for @noDrafts.
  ///
  /// In en, this message translates to:
  /// **'No drafts, click to create'**
  String get noDrafts;

  /// No description provided for @oneClickMode.
  ///
  /// In en, this message translates to:
  /// **'One-click mode'**
  String get oneClickMode;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @haveDone.
  ///
  /// In en, this message translates to:
  /// **'Done\nClick to view'**
  String get haveDone;

  /// No description provided for @original.
  ///
  /// In en, this message translates to:
  /// **'Original'**
  String get original;

  /// No description provided for @unDone.
  ///
  /// In en, this message translates to:
  /// **'Not done'**
  String get unDone;

  /// No description provided for @generating.
  ///
  /// In en, this message translates to:
  /// **'Generating...'**
  String get generating;

  /// No description provided for @failed.
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// No description provided for @rename.
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @reMake.
  ///
  /// In en, this message translates to:
  /// **'Re-make'**
  String get reMake;

  /// No description provided for @earnPoints.
  ///
  /// In en, this message translates to:
  /// **'Earn'**
  String get earnPoints;

  /// No description provided for @reDownload.
  ///
  /// In en, this message translates to:
  /// **'Re-download'**
  String get reDownload;

  /// No description provided for @taskClaim.
  ///
  /// In en, this message translates to:
  /// **'Task claim'**
  String get taskClaim;

  /// No description provided for @resDownload.
  ///
  /// In en, this message translates to:
  /// **'Res download'**
  String get resDownload;

  /// No description provided for @notLogged.
  ///
  /// In en, this message translates to:
  /// **'Not logged in'**
  String get notLogged;

  /// No description provided for @vipExpired.
  ///
  /// In en, this message translates to:
  /// **'Membership expired'**
  String get vipExpired;

  /// No description provided for @myRole.
  ///
  /// In en, this message translates to:
  /// **'My role'**
  String get myRole;

  /// No description provided for @allCategory.
  ///
  /// In en, this message translates to:
  /// **'All categories'**
  String get allCategory;

  /// No description provided for @videoExport.
  ///
  /// In en, this message translates to:
  /// **'Video export'**
  String get videoExport;

  /// No description provided for @syntheticSubtitles.
  ///
  /// In en, this message translates to:
  /// **'Synthetic subtitles'**
  String get syntheticSubtitles;

  /// No description provided for @noSubtitles.
  ///
  /// In en, this message translates to:
  /// **'The video synthesized after cancellation has no subtitles'**
  String get noSubtitles;

  /// No description provided for @exPortVideo.
  ///
  /// In en, this message translates to:
  /// **'Export video'**
  String get exPortVideo;

  /// No description provided for @exportingInfo.
  ///
  /// In en, this message translates to:
  /// **'Exporting... Please wait'**
  String get exportingInfo;

  /// No description provided for @synthesisProgress.
  ///
  /// In en, this message translates to:
  /// **'Video synthesis in progress...'**
  String get synthesisProgress;

  /// No description provided for @storyboard.
  ///
  /// In en, this message translates to:
  /// **'Storyboard'**
  String get storyboard;

  /// No description provided for @exportSuccessfulAndSave.
  ///
  /// In en, this message translates to:
  /// **'Export successful, saved to album'**
  String get exportSuccessfulAndSave;

  /// No description provided for @basicSettings.
  ///
  /// In en, this message translates to:
  /// **'Basic settings'**
  String get basicSettings;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @aiWrite.
  ///
  /// In en, this message translates to:
  /// **'AI write'**
  String get aiWrite;

  /// No description provided for @uploadAudio.
  ///
  /// In en, this message translates to:
  /// **'Upload Audio'**
  String get uploadAudio;

  /// No description provided for @enterText.
  ///
  /// In en, this message translates to:
  /// **'Enter Text'**
  String get enterText;

  /// No description provided for @aiEnterExample.
  ///
  /// In en, this message translates to:
  /// **'Enter example: Children\'s Day'**
  String get aiEnterExample;

  /// No description provided for @selectStyle.
  ///
  /// In en, this message translates to:
  /// **'Select style'**
  String get selectStyle;

  /// No description provided for @numStoryboards.
  ///
  /// In en, this message translates to:
  /// **'Num storyboards'**
  String get numStoryboards;

  /// No description provided for @aiWriteTips.
  ///
  /// In en, this message translates to:
  /// **'Note: Ai automatically generates copy and prompts, deducting 2 points for each storyboard'**
  String get aiWriteTips;

  /// No description provided for @nowDeductionPoints.
  ///
  /// In en, this message translates to:
  /// **'Current deduction points:'**
  String get nowDeductionPoints;

  /// No description provided for @ttsRoles.
  ///
  /// In en, this message translates to:
  /// **'TTS roles'**
  String get ttsRoles;

  /// No description provided for @moreSettings.
  ///
  /// In en, this message translates to:
  /// **'More settings'**
  String get moreSettings;

  /// No description provided for @screenRatio.
  ///
  /// In en, this message translates to:
  /// **'Screen ratio'**
  String get screenRatio;

  /// No description provided for @pleaseEnterText.
  ///
  /// In en, this message translates to:
  /// **'Please enter a article'**
  String get pleaseEnterText;

  /// No description provided for @nOperationIsRequired.
  ///
  /// In en, this message translates to:
  /// **'No operation is required for re-production'**
  String get nOperationIsRequired;

  /// No description provided for @speed.
  ///
  /// In en, this message translates to:
  /// **'Speed'**
  String get speed;

  /// No description provided for @fixedCharacter.
  ///
  /// In en, this message translates to:
  /// **'Fixed character'**
  String get fixedCharacter;

  /// No description provided for @globalSettings.
  ///
  /// In en, this message translates to:
  /// **'Global settings'**
  String get globalSettings;

  /// No description provided for @repOriText.
  ///
  /// In en, this message translates to:
  /// **'Replace text'**
  String get repOriText;

  /// No description provided for @subImgTask.
  ///
  /// In en, this message translates to:
  /// **'Submit image task'**
  String get subImgTask;

  /// No description provided for @subVideoTask.
  ///
  /// In en, this message translates to:
  /// **'Submit video task'**
  String get subVideoTask;

  /// No description provided for @someUnSaved.
  ///
  /// In en, this message translates to:
  /// **'Some changes un saved'**
  String get someUnSaved;

  /// No description provided for @clickToSaveAndExit.
  ///
  /// In en, this message translates to:
  /// **'Click OK to save the draft and exit'**
  String get clickToSaveAndExit;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @spanish.
  ///
  /// In en, this message translates to:
  /// **'Spanish'**
  String get spanish;

  /// No description provided for @japanese.
  ///
  /// In en, this message translates to:
  /// **'Japanese'**
  String get japanese;

  /// No description provided for @appSettings.
  ///
  /// In en, this message translates to:
  /// **'App settings'**
  String get appSettings;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @tutorial.
  ///
  /// In en, this message translates to:
  /// **'Tutorial'**
  String get tutorial;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy policy'**
  String get privacyPolicy;

  /// No description provided for @userAgreement.
  ///
  /// In en, this message translates to:
  /// **'User agreement'**
  String get userAgreement;

  /// No description provided for @pleaseRead.
  ///
  /// In en, this message translates to:
  /// **'Please read carefully before using and confirm that you agree to the above terms.'**
  String get pleaseRead;

  /// No description provided for @welcomeToMovieNest.
  ///
  /// In en, this message translates to:
  /// **'Welcome to MovieNest, in order to better protect your privacy and personal information security, we have formulated'**
  String get welcomeToMovieNest;

  /// No description provided for @versionInfo.
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get versionInfo;

  /// No description provided for @checkForUpdates.
  ///
  /// In en, this message translates to:
  /// **'Check for updates'**
  String get checkForUpdates;

  /// No description provided for @clickToDownloadNewVersion.
  ///
  /// In en, this message translates to:
  /// **'Click to download the new version'**
  String get clickToDownloadNewVersion;

  /// No description provided for @updateContent.
  ///
  /// In en, this message translates to:
  /// **'Update content'**
  String get updateContent;

  /// No description provided for @newVersionDownloading.
  ///
  /// In en, this message translates to:
  /// **'New version downloading'**
  String get newVersionDownloading;

  /// No description provided for @logOut.
  ///
  /// In en, this message translates to:
  /// **'Log out'**
  String get logOut;

  /// No description provided for @logOutInfo.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get logOutInfo;

  /// No description provided for @exit.
  ///
  /// In en, this message translates to:
  /// **'Exit'**
  String get exit;

  /// No description provided for @logoutSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Logout successful'**
  String get logoutSuccessful;

  /// No description provided for @logoutFailed.
  ///
  /// In en, this message translates to:
  /// **'Logout failed'**
  String get logoutFailed;

  /// No description provided for @logoutFailedInfo.
  ///
  /// In en, this message translates to:
  /// **'Logout failed, please try again later'**
  String get logoutFailedInfo;

  /// No description provided for @sighInSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Sign in successful'**
  String get sighInSuccessful;

  /// No description provided for @cacheClean.
  ///
  /// In en, this message translates to:
  /// **'Cache cleaning'**
  String get cacheClean;

  /// No description provided for @clickClean.
  ///
  /// In en, this message translates to:
  /// **'Click'**
  String get clickClean;

  /// No description provided for @thirdDataList.
  ///
  /// In en, this message translates to:
  /// **'Third-party data list'**
  String get thirdDataList;

  /// No description provided for @personalInfoList.
  ///
  /// In en, this message translates to:
  /// **'Personal information list'**
  String get personalInfoList;

  /// No description provided for @accountCancellation.
  ///
  /// In en, this message translates to:
  /// **'Account cancellation'**
  String get accountCancellation;

  /// No description provided for @accountCancellationInfo.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel your account?'**
  String get accountCancellationInfo;

  /// No description provided for @accountCancellationInfo1.
  ///
  /// In en, this message translates to:
  /// **'your account will be permanently deleted'**
  String get accountCancellationInfo1;

  /// No description provided for @aiGenerating.
  ///
  /// In en, this message translates to:
  /// **'AI generating...'**
  String get aiGenerating;

  /// No description provided for @setToStoryBoard.
  ///
  /// In en, this message translates to:
  /// **'Set to storyboard'**
  String get setToStoryBoard;

  /// No description provided for @imitation.
  ///
  /// In en, this message translates to:
  /// **'Imitation'**
  String get imitation;

  /// No description provided for @light.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// No description provided for @pov.
  ///
  /// In en, this message translates to:
  /// **'POV'**
  String get pov;

  /// No description provided for @promptDic.
  ///
  /// In en, this message translates to:
  /// **'Prompt dict'**
  String get promptDic;

  /// No description provided for @num.
  ///
  /// In en, this message translates to:
  /// **'Num'**
  String get num;

  /// No description provided for @style.
  ///
  /// In en, this message translates to:
  /// **'Style'**
  String get style;

  /// No description provided for @advanceSetting.
  ///
  /// In en, this message translates to:
  /// **'Advanced Setting'**
  String get advanceSetting;

  /// No description provided for @image.
  ///
  /// In en, this message translates to:
  /// **'Image'**
  String get image;

  /// No description provided for @video.
  ///
  /// In en, this message translates to:
  /// **'Video'**
  String get video;

  /// No description provided for @tts.
  ///
  /// In en, this message translates to:
  /// **'Dubbing'**
  String get tts;

  /// No description provided for @motion.
  ///
  /// In en, this message translates to:
  /// **'Motion'**
  String get motion;

  /// No description provided for @preview.
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get preview;

  /// No description provided for @webDetails.
  ///
  /// In en, this message translates to:
  /// **'Web details'**
  String get webDetails;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid email address'**
  String get invalidEmail;

  /// No description provided for @userHasDisabled.
  ///
  /// In en, this message translates to:
  /// **'User has been disabled'**
  String get userHasDisabled;

  /// No description provided for @userDoesNotExit.
  ///
  /// In en, this message translates to:
  /// **'User does not exist'**
  String get userDoesNotExit;

  /// No description provided for @passError.
  ///
  /// In en, this message translates to:
  /// **'Password error'**
  String get passError;

  /// No description provided for @loginFailed.
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// No description provided for @emailAlreadyInUse.
  ///
  /// In en, this message translates to:
  /// **'Email already in use'**
  String get emailAlreadyInUse;

  /// No description provided for @unknownOccurred.
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred'**
  String get unknownOccurred;

  /// No description provided for @operationNotAllowed.
  ///
  /// In en, this message translates to:
  /// **'Operation not allowed'**
  String get operationNotAllowed;

  /// No description provided for @weakPassword.
  ///
  /// In en, this message translates to:
  /// **'Password is too weak'**
  String get weakPassword;

  /// No description provided for @registrationFailed.
  ///
  /// In en, this message translates to:
  /// **'Registration failed'**
  String get registrationFailed;

  /// No description provided for @submitSuccess.
  ///
  /// In en, this message translates to:
  /// **'Submit successfully'**
  String get submitSuccess;

  /// No description provided for @generated.
  ///
  /// In en, this message translates to:
  /// **'(Generated)'**
  String get generated;

  /// No description provided for @notGenerated.
  ///
  /// In en, this message translates to:
  /// **'(Not generated)'**
  String get notGenerated;

  /// No description provided for @lockSeed.
  ///
  /// In en, this message translates to:
  /// **'Lock seed'**
  String get lockSeed;

  /// No description provided for @randomSeed.
  ///
  /// In en, this message translates to:
  /// **'Random seed'**
  String get randomSeed;

  /// No description provided for @setSeed.
  ///
  /// In en, this message translates to:
  /// **'Set seed'**
  String get setSeed;

  /// No description provided for @deleteDraft.
  ///
  /// In en, this message translates to:
  /// **'Delete draft'**
  String get deleteDraft;

  /// No description provided for @areYouSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get areYouSure;

  /// No description provided for @generateImage.
  ///
  /// In en, this message translates to:
  /// **'Generate image'**
  String get generateImage;

  /// No description provided for @unrecognizedRole.
  ///
  /// In en, this message translates to:
  /// **'Unrecognized role'**
  String get unrecognizedRole;

  /// No description provided for @unrecognizedScene.
  ///
  /// In en, this message translates to:
  /// **'Unrecognized scene'**
  String get unrecognizedScene;

  /// No description provided for @selectStartImageToVideo.
  ///
  /// In en, this message translates to:
  /// **'Select the first image'**
  String get selectStartImageToVideo;

  /// No description provided for @selectEndImageToVideo.
  ///
  /// In en, this message translates to:
  /// **'Select the last image'**
  String get selectEndImageToVideo;

  /// No description provided for @pleaseEnterTheMotionGuidePrompt.
  ///
  /// In en, this message translates to:
  /// **'Please enter the motion guide prompt words for generating the video, if not filled in, it will be automatically generated according to the picture'**
  String get pleaseEnterTheMotionGuidePrompt;

  /// No description provided for @otherSetting.
  ///
  /// In en, this message translates to:
  /// **'Other settings'**
  String get otherSetting;

  /// No description provided for @highQualityModeTakesTwiceTime.
  ///
  /// In en, this message translates to:
  /// **'(High-quality mode takes twice time, please be patient)'**
  String get highQualityModeTakesTwiceTime;

  /// No description provided for @mode.
  ///
  /// In en, this message translates to:
  /// **'Mode'**
  String get mode;

  /// No description provided for @fastMode.
  ///
  /// In en, this message translates to:
  /// **'Fast mode'**
  String get fastMode;

  /// No description provided for @highQualityMode.
  ///
  /// In en, this message translates to:
  /// **'High-quality mode'**
  String get highQualityMode;

  /// No description provided for @resolution.
  ///
  /// In en, this message translates to:
  /// **'Resolution'**
  String get resolution;

  /// No description provided for @sd.
  ///
  /// In en, this message translates to:
  /// **'SD'**
  String get sd;

  /// No description provided for @hd.
  ///
  /// In en, this message translates to:
  /// **'HD'**
  String get hd;

  /// No description provided for @play.
  ///
  /// In en, this message translates to:
  /// **'Play'**
  String get play;

  /// No description provided for @startDubbing.
  ///
  /// In en, this message translates to:
  /// **'Start dubbing'**
  String get startDubbing;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @dubbingFailed.
  ///
  /// In en, this message translates to:
  /// **'Dubbing failed'**
  String get dubbingFailed;

  /// No description provided for @noDubbingYet.
  ///
  /// In en, this message translates to:
  /// **'No dubbing yet'**
  String get noDubbingYet;

  /// No description provided for @textCannotBeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Text cannot be empty'**
  String get textCannotBeEmpty;

  /// No description provided for @generalWaitInfo.
  ///
  /// In en, this message translates to:
  /// **'During peak hours, it takes about 1 minute to generate a picture quickly, and about 5~10 minutes to generate a picture normally. The speed is slightly faster during off-peak hours. Please be patient.\\n\\nDo not exit during the generation of the picture. The recent average waiting time is 3 minutes and 16 seconds.'**
  String get generalWaitInfo;

  /// No description provided for @downloadSuccessAndSave.
  ///
  /// In en, this message translates to:
  /// **'Download successful, saved to album'**
  String get downloadSuccessAndSave;

  /// No description provided for @submitTask.
  ///
  /// In en, this message translates to:
  /// **'Submit task'**
  String get submitTask;

  /// No description provided for @imageQuality.
  ///
  /// In en, this message translates to:
  /// **'Image quality'**
  String get imageQuality;

  /// No description provided for @excellentCase.
  ///
  /// In en, this message translates to:
  /// **'Excellent case'**
  String get excellentCase;

  /// No description provided for @allMaterialsGeneratedByMovieNest.
  ///
  /// In en, this message translates to:
  /// **'All materials are generated by MovieNest'**
  String get allMaterialsGeneratedByMovieNest;

  /// No description provided for @theDefaultIsHDInfo.
  ///
  /// In en, this message translates to:
  /// **'The default is HD ordinary high-definition. FHD deducts 4 times the points, and QHD deducts 6 times the points. The higher the quality, the richer the picture details'**
  String get theDefaultIsHDInfo;

  /// No description provided for @aiPrompts.
  ///
  /// In en, this message translates to:
  /// **'Ai prompts'**
  String get aiPrompts;

  /// No description provided for @aiPromptsInfos.
  ///
  /// In en, this message translates to:
  /// **'After checking, Ai reasoning prompts will be added on the basis of the original prompts'**
  String get aiPromptsInfos;

  /// No description provided for @reDubbing.
  ///
  /// In en, this message translates to:
  /// **'Re-dubbing'**
  String get reDubbing;

  /// No description provided for @reDubbingInfos.
  ///
  /// In en, this message translates to:
  /// **'After checking, TTS dubbing will be re-generated'**
  String get reDubbingInfos;

  /// No description provided for @effective.
  ///
  /// In en, this message translates to:
  /// **'Effective range'**
  String get effective;

  /// No description provided for @allStoryboards.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get allStoryboards;

  /// No description provided for @videoStoryboard.
  ///
  /// In en, this message translates to:
  /// **'Video storyboard'**
  String get videoStoryboard;

  /// No description provided for @dynamicVideo.
  ///
  /// In en, this message translates to:
  /// **'Dynamic video'**
  String get dynamicVideo;

  /// No description provided for @dynamicVideoInfo.
  ///
  /// In en, this message translates to:
  /// **'After checking, each storyboard will generate a dynamic video'**
  String get dynamicVideoInfo;

  /// No description provided for @taskLimit.
  ///
  /// In en, this message translates to:
  /// **'The concurrent task limit has been reached. Maximum 3'**
  String get taskLimit;

  /// No description provided for @membershipRenewal.
  ///
  /// In en, this message translates to:
  /// **'Membership renewal'**
  String get membershipRenewal;

  /// No description provided for @iHaveReadAndAgree.
  ///
  /// In en, this message translates to:
  /// **'I have read and agree'**
  String get iHaveReadAndAgree;

  /// No description provided for @piecesAnimaMembershipAgreement.
  ///
  /// In en, this message translates to:
  /// **'《Membership Purchase Agreement》'**
  String get piecesAnimaMembershipAgreement;

  /// No description provided for @payNow.
  ///
  /// In en, this message translates to:
  /// **'Pay now'**
  String get payNow;

  /// No description provided for @pleaseReadAndAgreeMembership.
  ///
  /// In en, this message translates to:
  /// **'Please read and agree to the membership purchase agreement first'**
  String get pleaseReadAndAgreeMembership;

  /// No description provided for @pleaseSelectTypeOfMembership.
  ///
  /// In en, this message translates to:
  /// **'Please select the type of membership you want to purchase.'**
  String get pleaseSelectTypeOfMembership;

  /// No description provided for @gettingPaymentInfo.
  ///
  /// In en, this message translates to:
  /// **'Getting payment information...'**
  String get gettingPaymentInfo;

  /// No description provided for @paySuccess.
  ///
  /// In en, this message translates to:
  /// **'Payment successful'**
  String get paySuccess;

  /// No description provided for @payFailed.
  ///
  /// In en, this message translates to:
  /// **'Payment failed'**
  String get payFailed;

  /// No description provided for @membershipExpired.
  ///
  /// In en, this message translates to:
  /// **'Membership expired'**
  String get membershipExpired;

  /// No description provided for @pleaseEnterTheme.
  ///
  /// In en, this message translates to:
  /// **'Please enter a theme'**
  String get pleaseEnterTheme;

  /// No description provided for @pleaseWaitForStyleLoaded.
  ///
  /// In en, this message translates to:
  /// **'Please wait for the style to load'**
  String get pleaseWaitForStyleLoaded;

  /// No description provided for @myPoints.
  ///
  /// In en, this message translates to:
  /// **'My points'**
  String get myPoints;

  /// No description provided for @claim.
  ///
  /// In en, this message translates to:
  /// **'Claim'**
  String get claim;

  /// No description provided for @claimed.
  ///
  /// In en, this message translates to:
  /// **'Claimed'**
  String get claimed;

  /// No description provided for @checkToReceiveVip.
  ///
  /// In en, this message translates to:
  /// **'Check in To Receive Membership'**
  String get checkToReceiveVip;

  /// No description provided for @progress.
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// No description provided for @playRecord.
  ///
  /// In en, this message translates to:
  /// **'Play Record'**
  String get playRecord;

  /// No description provided for @updateReminders.
  ///
  /// In en, this message translates to:
  /// **'Movie/TV Update Reminders'**
  String get updateReminders;

  /// No description provided for @myDownload.
  ///
  /// In en, this message translates to:
  /// **'My Download'**
  String get myDownload;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @collection.
  ///
  /// In en, this message translates to:
  /// **'Collection'**
  String get collection;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @vipCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Limited Time Discount'**
  String get vipCardTitle;

  /// No description provided for @vipCardDescription.
  ///
  /// In en, this message translates to:
  /// **'Get a member and enjoy exclusive privileges'**
  String get vipCardDescription;

  /// No description provided for @vipCardFunction1.
  ///
  /// In en, this message translates to:
  /// **'Remove all  Ads'**
  String get vipCardFunction1;

  /// No description provided for @vipCardFunction2.
  ///
  /// In en, this message translates to:
  /// **'GUnlock Movies'**
  String get vipCardFunction2;

  /// No description provided for @vipCardFunction3.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Downloads'**
  String get vipCardFunction3;

  /// No description provided for @vipCardFunction4.
  ///
  /// In en, this message translates to:
  /// **'HD Resources'**
  String get vipCardFunction4;

  /// No description provided for @vipCardFunction5.
  ///
  /// In en, this message translates to:
  /// **'Unlock Cast'**
  String get vipCardFunction5;

  /// No description provided for @vipCardButton.
  ///
  /// In en, this message translates to:
  /// **'Get'**
  String get vipCardButton;

  /// No description provided for @categoriesTitle.
  ///
  /// In en, this message translates to:
  /// **'CATEGORIES'**
  String get categoriesTitle;

  /// No description provided for @categoriesSearchDesc.
  ///
  /// In en, this message translates to:
  /// **'Searcch Movies, Genres etc'**
  String get categoriesSearchDesc;

  /// No description provided for @deleteSuccess.
  ///
  /// In en, this message translates to:
  /// **'Delete Success'**
  String get deleteSuccess;

  /// No description provided for @deleteFailed.
  ///
  /// In en, this message translates to:
  /// **'Days Failed'**
  String get deleteFailed;

  /// No description provided for @deleting.
  ///
  /// In en, this message translates to:
  /// **'Deleting'**
  String get deleting;

  /// No description provided for @deleteSelected.
  ///
  /// In en, this message translates to:
  /// **'DeleteSelected'**
  String get deleteSelected;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ja', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ja': return AppLocalizationsJa();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
