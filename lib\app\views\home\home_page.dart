import 'package:flutter/material.dart';
import 'package:movie_nest/helpers/analytics_helper.dart';
import 'package:movie_nest/models/video.dart';
import 'package:movie_nest/widgets/video_card.dart';

class HomePage extends StatefulWidget {
  // ... (existing code)
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('home');
  }

  // 添加按钮点击埋点
  void _trackButtonClick(String buttonName) {
    AnalyticsHelper().trackButtonClick(buttonName, position: 'home');
  }

  // 添加视频点击埋点
  void _trackVideoClick(int videoId, String videoName) {
    AnalyticsHelper().trackViewItem('video', videoId.toString());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('MovieNest'),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () {
              _trackButtonClick('search_button');
              // ... existing search code ...
            },
          ),
        ],
      ),
      body: ListView.builder(
        itemCount: videos.length,
        itemBuilder: (context, index) {
          final video = videos[index];
          return GestureDetector(
            onTap: () {
              _trackVideoClick(video.id, video.name);
              // ... existing navigation code ...
            },
            child: VideoCard(video: video),
          );
        },
      ),
    );
  }
} 