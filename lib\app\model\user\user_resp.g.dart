// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VipInfo _$VipInfoFromJson(Map<String, dynamic> json) => VipInfo(
      endtimeAt: json['endtime_at'] as String?,
    );

Map<String, dynamic> _$VipInfoToJson(VipInfo instance) => <String, dynamic>{
      'endtime_at': instance.endtimeAt,
    };

UserRespVo _$UserRespVoFromJson(Map<String, dynamic> json) => UserRespVo(
      accessToken: json['access_token'] as String?,
      email: json['email'] as String?,
      uuid: json['uuid'] as String?,
      uname: json['uname'] as String?,
      userDesc: json['user_desc'] as String?,
      vipInfo: json['vip_info'] == null
          ? null
          : VipInfo.fromJson(json['vip_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserRespVoToJson(UserRespVo instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'email': instance.email,
      'uuid': instance.uuid,
      'uname': instance.uname,
      'user_desc': instance.userDesc,
      'vip_info': instance.vipInfo,
    };
