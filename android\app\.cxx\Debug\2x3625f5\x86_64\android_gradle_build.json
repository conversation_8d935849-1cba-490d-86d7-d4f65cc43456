{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\git_resource\\movienest-app\\android\\app\\.cxx\\Debug\\2x3625f5\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\git_resource\\movienest-app\\android\\app\\.cxx\\Debug\\2x3625f5\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\android_sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\android_sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}