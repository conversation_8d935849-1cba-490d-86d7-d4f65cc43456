import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../utils/toast.dart';
import '../bloc/bloc.dart';
import '../bloc/event.dart';
import '../bloc/state.dart';

class AppUpdatePanel extends StatelessWidget {
  const AppUpdatePanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UpdateBloc, UpdateState>(
      builder: _buildByUpdateState,
      listener: _listenerByUpdateState,
    );
  }

  Widget _buildProgress(BuildContext context, double progress, int appSize) {
    return Wrap(
        alignment: WrapAlignment.center,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Column(
            children: [
              Text(
                '${(progress * 100).toStringAsFixed(2)} %',
                style: const TextStyle(
                    height: 1, fontSize: 15, color: Colors.grey),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                '${convertFileSize((appSize * progress).floor())}/${convertFileSize(appSize)}',
                style: const TextStyle(
                    height: 1, fontSize: 13, color: Colors.grey),
              ),
            ],
          ),
          const SizedBox(
            width: 15,
          ),
          SizedBox(
            width: 25,
            height: 25,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              backgroundColor: Colors.grey,
              value: progress,
            ),
          )
        ]);
  }

  Widget _buildByUpdateState(BuildContext context, UpdateState state) {
    String info = AppLocalizations.of(context).checkForUpdates;
    Widget trail = const SizedBox.shrink();
    if (state is ShouldUpdateState) {
      info = AppLocalizations.of(context).clickToDownloadNewVersion;
      trail = Wrap(
          direction: Axis.horizontal,
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            Wrap(
              direction: Axis.vertical,
              alignment: WrapAlignment.center,
              children: [
                Text(
                  '${state.oldVersion} --> ${state.info.appVersion} ',
                  style: const TextStyle(
                      height: 1, fontSize: 15, color: Colors.grey),
                ),
                Text(
                  AppLocalizations.of(context).updateContent +
                      ':\n${utf8.decode(base64Decode(state.info.update_content))} ',
                  style: const TextStyle(
                      height: 1, fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            Container(
              width: 10,
              height: 10,
              decoration: const BoxDecoration(
                  color: Colors.red, shape: BoxShape.circle),
            ),
            const SizedBox(width: 5),
            const Icon(Icons.update, color: Colors.green)
          ]);
    }
    if (state is CheckLoadingState) {
      trail = const CupertinoActivityIndicator();
    }
    if (state is DownloadingState) {
      info = AppLocalizations.of(context).newVersionDownloading;
      trail = _buildProgress(context, state.progress, state.appSize);
    }

    return InkWell(
      child: Wrap(
        direction: Axis.vertical,
        alignment: WrapAlignment.center,
        runAlignment: WrapAlignment.center,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Text(
            info,
            style: const TextStyle(fontSize: 13),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: trail,
          )
        ],
      ),
      onTap: () => _tapByState(state, context),
    );
  }

  void _tapByState(UpdateState state, BuildContext context) {
    print("_tapByState:" + state.toString());
    if (state is NoUpdateState) {
      BlocProvider.of<UpdateBloc>(context)
          .add(const CheckUpdate(appName: 'MovieNest'));
    }
    if (state is ShouldUpdateState) {
      if (Platform.isIOS) {
        // ios 跳转应用商店
        // RUpgrade.upgradeFromAppStore('**********', false);
        return;
      } else if (Platform.isMacOS) {
        // 跳转官网
        return;
      }

      // 处理下载的事件
      BlocProvider.of<UpdateBloc>(context)
          .add(DownloadEvent(appInfo: state.info));
    }
  }

  void _listenerByUpdateState(BuildContext context, UpdateState state) {
    if (state is NoUpdateState) {
      if (state.isChecked) {
        Toast.success(context, '当前应用已是最新版本!');
      }
    }
  }

  String convertFileSize(int size) {
    double result = size / 1024.0;
    if (result < 1024) {
      return "${result.toStringAsFixed(2)} Kb";
    } else if (result > 1024 && result < 1024 * 1024) {
      return "${(result / 1024).toStringAsFixed(2)} Mb";
    } else {
      return "${(result / 1024 / 1024).toStringAsFixed(2)} Gb";
    }
  }
}
