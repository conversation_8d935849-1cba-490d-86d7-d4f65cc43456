import 'package:flutter/material.dart';
import '../../../../utils/analytics_helper.dart';

class SettingsPage extends StatefulWidget {
  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  void initState() {
    super.initState();
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('settings');
  }

  // 添加按钮点击埋点
  void _trackButtonClick(String buttonName) {
    AnalyticsHelper().trackButtonClick(buttonName, position: 'settings');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Settings'),
      ),
      body: ListView(
        children: [
          ListTile(
            leading: Icon(Icons.language),
            title: Text('Language'),
            onTap: () {
              _trackButtonClick('language_button');
              // ... existing language settings code ...
            },
          ),
          ListTile(
            leading: Icon(Icons.notifications),
            title: Text('Notifications'),
            onTap: () {
              _trackButtonClick('notifications_button');
              // ... existing notification settings code ...
            },
          ),
          ListTile(
            leading: Icon(Icons.privacy_tip),
            title: Text('Privacy'),
            onTap: () {
              _trackButtonClick('privacy_button');
              // ... existing privacy settings code ...
            },
          ),
          ListTile(
            leading: Icon(Icons.help),
            title: Text('Help & Support'),
            onTap: () {
              _trackButtonClick('help_button');
              // ... existing help & support code ...
            },
          ),
          ListTile(
            leading: Icon(Icons.logout),
            title: Text('Logout'),
            onTap: () {
              _trackButtonClick('logout_button');
              // ... existing logout code ...
            },
          ),
        ],
      ),
    );
  }
} 