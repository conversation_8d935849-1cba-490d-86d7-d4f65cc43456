[{"image": "assets/images/anim_draw.webp", "name": "基础绘制", "info": "收录一些基础图形绘制案例，这些案例对初涉绘制的编程者会非常友好。通过这些案例，可以学会点、线、矩形、圆、圆弧、文字、图片等基本图形的绘制方法，了解 Canvas、Paint、Path 等绘制中核心对象的使用。"}, {"image": "assets/images/draw_bg3.webp", "name": "动画手势", "info": "收录一些动画和手势的绘制案例，这些案例会让绘制更具有操作性。通过这些案例，可以学会动画和手势的使用，如滑动、旋转、缩放、移动等效果，让绘制不再只是静态展现。"}, {"image": "assets/images/base_draw.webp", "name": "粒子绘制", "info": "收录一些粒子相关的绘制案例，这些案例将是绘制的顶级操作。通过这些案例，可以学会如何使用粒子来绘制惊艳的视觉效果，如粒子时钟、粒子爆炸、粒子背景等效果，让绘制拥有无限可能。"}, {"image": "assets/images/draw_bg4.webp", "name": "趣味绘制", "info": "收录一些比较有趣的绘制案例，让我们一起在这里一起体验绘制的乐趣、编程的乐趣和智慧的乐趣吧。"}, {"image": "assets/images/caver.webp", "name": "艺术画廊", "info": "收录一些殿堂级的绘制案例，这些案例将是绘制的巅峰作品，它们的没有任何的实用性，也不为任何需求而生，它们仅是因为存在而存在，是人类智慧和表达的媒介，称谓艺术。"}]