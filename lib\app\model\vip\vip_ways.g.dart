// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_ways.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VipWays _$VipWaysFromJson(Map<String, dynamic> json) => VipWays(
      cpack: json['cpack'] as String,
      desc: json['desc'] as String,
      discount: Discount.fromJson(json['discount'] as Map<String, dynamic>),
      id: (json['id'] as num).toInt(),
      iosPay: json['ios_pay'] as String,
      originPrice: (json['origin_price'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      title: json['title'] as String,
      way: (json['way'] as num).toInt(),
    );

Map<String, dynamic> _$VipWaysToJson(VipWays instance) => <String, dynamic>{
      'cpack': instance.cpack,
      'desc': instance.desc,
      'discount': instance.discount,
      'id': instance.id,
      'ios_pay': instance.iosPay,
      'origin_price': instance.originPrice,
      'price': instance.price,
      'title': instance.title,
      'way': instance.way,
    };

Discount _$DiscountFromJson(Map<String, dynamic> json) => Discount(
      desc: json['desc'] as String,
      hint: json['hint'] as String,
    );

Map<String, dynamic> _$DiscountToJson(Discount instance) => <String, dynamic>{
      'desc': instance.desc,
      'hint': instance.hint,
    };
