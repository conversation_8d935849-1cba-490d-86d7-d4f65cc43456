// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoRespVo _$VideoRespVoFromJson(Map<String, dynamic> json) => VideoRespVo(
      coverImage: json['cover_image'] as String?,
      description: json['description'] as String?,
      duration: (json['duration'] as num).toInt(),
      id: (json['id'] as num).toInt(),
      isFree: json['is_free'] as bool,
      name: json['name'] as String,
      playLink: json['play_link'] as String,
      region: json['region'] as String?,
      releaseTime: json['release_time'] == null
          ? null
          : DateTime.parse(json['release_time'] as String),
      score: (json['score'] as num?)?.toDouble(),
      subtitle: json['subtitle_link'] as String?,
      extraSubtitle: (json['other_subtitle'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      type: json['type'] as String,
      year: (json['year'] as num).toInt(),
    );

Map<String, dynamic> _$VideoRespVoToJson(VideoRespVo instance) =>
    <String, dynamic>{
      'cover_image': instance.coverImage,
      'description': instance.description,
      'duration': instance.duration,
      'id': instance.id,
      'is_free': instance.isFree,
      'name': instance.name,
      'play_link': instance.playLink,
      'region': instance.region,
      'release_time': instance.releaseTime?.toIso8601String(),
      'score': instance.score,
      'type': instance.type,
      'year': instance.year,
      'subtitle_link': instance.subtitle,
      'other_subtitle': instance.extraSubtitle,
    };
