import 'dart:math';

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/views/detail/video_search_more.dart';
import 'package:pieces_ai/utils/analytics_helper.dart';

import '../../../components/custom_widget/hotel_booking/hotel_app_theme.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class VideoSearch extends StatefulWidget {
  const VideoSearch({Key? key}) : super(key: key);

  @override
  State<VideoSearch> createState() => _VideoSearchState();
}

class _VideoSearchState extends State<VideoSearch> {
  TextEditingController _searchController = TextEditingController();
  int currentState = 0; // 0 表示搜索，1 表示搜索结果
  bool _useYtsApi = false; // 控制是否使用YTS API
  final List<String> popularSearchTerms = [
    'Sex',
    'Comedy',
    'Drama',
    'Horror',
    'Romance',
    'Sci-Fi',
    'Thriller',
    'Documentary'
  ];

  @override
  void initState() {
    super.initState();
    _shufflePopularSearchTerms();

    logger.d('搜索页初始化');

    // 延迟获取路由参数
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments;

      logger.d('搜索页当前路由参数: $args');

      if (args is String) {
        logger.d('接收到的查询参数: $args');
        _searchController.text = args; // 自动填充到输入框
        _performSearch(args); // 自动执行搜索
      }
    });
  }

  void _shufflePopularSearchTerms() {
    popularSearchTerms.shuffle(Random());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String value) {
    if (value.trim().isEmpty) return;
    
    // 搜索埋点
    AnalyticsHelper().trackSearch(
      value.trim(), 
      _useYtsApi ? 'yts_api' : 'default'
    );
    
    videoSearchMoreKey.currentState?.refresh(words: value);
    setState(() {
      currentState = 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: HotelAppTheme.buildDarkTheme(),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0x4D000000), Color(0xB3C50101)],
          ),
        ),
        child: Scaffold(
          backgroundColor: Color(0xcD000000),
          appBar: PreferredSize(
            preferredSize: Size.zero,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              title: const Text('Video Search'),
            ),
          ),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          prefixIcon: Icon(Icons.search, color: Colors.grey),
                          hintText: 'Input...',
                          hintStyle: TextStyle(color: Colors.grey),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.1),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                            borderSide: BorderSide.none,
                          ),
                        ),
                        onSubmitted: (value) {
                          //去除前后空格
                          value.trim();
                          _performSearch(value);
                        },
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        _useYtsApi ? Icons.movie : Icons.movie_outlined,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        setState(() {
                          _useYtsApi = !_useYtsApi;
                          if (currentState == 1 && _searchController.text.isNotEmpty) {
                            _performSearch(_searchController.text);
                          }
                        });
                      },
                      tooltip: _useYtsApi ? 'YTS API' : '默认搜索',
                    ),
                    IconButton(
                      icon: Icon(Icons.cancel, color: Colors.white),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          currentState = 0;
                        });
                      },
                    ),
                  ],
                ),
              ),
              if (currentState == 0)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Popular Search',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              if (currentState == 0)
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Wrap(
                      spacing: 8.0,
                      runSpacing: 4.0,
                      children: List.generate(8, (index) {
                        return ActionChip(
                          label: Text(
                            '${popularSearchTerms[index]}',
                            style: TextStyle(color: Colors.white70),
                          ),
                          backgroundColor: Color(0xFF252525),
                          shadowColor: Colors.black,
                          elevation: 3,
                          side: BorderSide(color: Color(0xFF500000), width: 1),
                          avatar: CircleAvatar(
                            backgroundColor: Color(0xFF700000),
                            radius: 10,
                            child: Text('${index + 1}',
                              style: TextStyle(fontSize: 10, color: Colors.white70),
                            ),
                          ),
                          onPressed: () {
                            String words = popularSearchTerms[index];
                            _searchController.text = words;
                            _performSearch(words);
                          },
                        );
                      }),
                    ),
                  ),
                  flex: 7,
                ),
              if (currentState == 1)
                Expanded(
                  child: VideoSearchMore(
                    key: videoSearchMoreKey,
                    words: _searchController.text,
                    useYtsApi: _useYtsApi,
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }
}
