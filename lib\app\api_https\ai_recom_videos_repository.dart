import 'package:pieces_ai/app/model/VideoType.dart';

import '../model/videos/video_resp.dart';

abstract class RecomVideosRepository {

  //获取服务器所有可用的视频类型
  Future<List<VideoType>> loadVideoTypeList();

  // 获取服务器分类视频数据
  Future<List<VideoRespVo>> loadTypeData(int page,int count, int type_id,String region,int year,double score,int sort);

  //分页加载推荐视频
  Future<Map<String,List<VideoRespVo>>> loadRecomVideoList(VideoType videoType);

  //视频过滤查询接口,支持多条件查询，支持分页，支持排序
  Future<List<VideoRespVo>> loadSearchVideoList(int page, int count,String words);
}
