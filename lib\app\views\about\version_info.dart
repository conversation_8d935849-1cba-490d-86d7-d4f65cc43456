import 'package:components/toly_ui/toly_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../app_update/views/app_update_panel.dart';
import 'version/version_shower.dart';

/// create by blueming.wu on 2024/2/7

class VersionInfo extends StatelessWidget {
  const VersionInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: ConstrainedBox(
        constraints: const BoxConstraints.expand(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            _buildTop(context),
            // _buildCenter(context),

            Spacer(),
            const Divider(
              height: 1,
            ),
            Wrap(
              direction: Axis.vertical,
              crossAxisAlignment: WrapCrossAlignment.center,
              spacing: 4,
              children: <Widget>[
                // FeedbackWidget(
                //     onPressed: (){
                //       _launchURL("https://github.com/toly1994328/FlutterUnit");
                //     },
                //     child: const Text('《查看本项目Github仓库》',style: TextStyle(fontSize: 12,color: Color(0xff616C84),),)),
                const Text(
                  'Power By MovieNest',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const Text(
                  'Copyright © 2020-2024 ',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTop(BuildContext context) {
    return Wrap(
      direction: Axis.vertical,
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 40,
      children: [
        Text(
          AppLocalizations.of(context).versionInfo,
          style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
        ),
        Wrap(
          spacing: 60,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            CircleImage(
              borderSize: 1,
              image: AssetImage("assets/images/ic_launcher.png"),
              size: 80,
            ),
            Wrap(
              direction: Axis.vertical,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  'MovieNest(Pro)',
                  style: TextStyle(fontSize: 20),
                ),
                VersionShower()
              ],
            )
          ],
        ),
      ],
    );
  }

  Widget _buildCenter(BuildContext context) {
    // const TextStyle labelStyle = TextStyle(fontSize: 13);
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, right: 20, top: 20),
      child: const AppUpdatePanel(),
    );
  }
}
