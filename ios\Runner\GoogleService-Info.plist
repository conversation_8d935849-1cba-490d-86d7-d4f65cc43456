<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>172739951489-p5v6rd6p962h5vqc28d6pm0gnum9glbo.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.172739951489-p5v6rd6p962h5vqc28d6pm0gnum9glbo</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>172739951489-1jj3o88vh60l9r8b4dn09racve3ctgs7.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBewhWvuDf4IVq1qnVKtXqnNEFmdNSVA6o</string>
	<key>GCM_SENDER_ID</key>
	<string>172739951489</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.doujing.movienest</string>
	<key>PROJECT_ID</key>
	<string>movienest-fdedb</string>
	<key>STORAGE_BUCKET</key>
	<string>movienest-fdedb.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<true></true>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:172739951489:ios:ea97309c7365b04f5b8680</string>
	<key>DATABASE_URL</key>
	<string>https://movienest-fdedb-default-rtdb.firebaseio.com</string>
</dict>
</plist>