import 'package:app/app.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/api_https/impl/https_recom_videos.dart';
import 'package:pieces_ai/app/model/VideoType.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';

import '../../../widget_ui/mobile/ui_view/area_list_view.dart';
import '../../../widget_ui/mobile/ui_view/title_view.dart';
import 'filters_screen.dart';
import 'hotel_app_theme.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class HotelHomeScreen extends StatefulWidget {
  @override
  _HotelHomeScreenState createState() => _HotelHomeScreenState();
}

class _HotelHomeScreenState extends State<HotelHomeScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  AnimationController? animationController;
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  late PageController _pageController;

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now().add(const Duration(days: 5));

  final HttpsRecomVideos _httpsRecomVideos = HttpsRecomVideos();
  Color left = Colors.black;
  List<VideoType> videoTypes = [];
  List<Widget> _pages = [];
  int _currentIndex = 0;
  List<bool> _loadedFlags = [];
  bool _isSwiping = false;


  @override
  void initState() {
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    _videoTypeFuture = _httpsRecomVideos.loadVideoTypeList();

    // 提前初始化PageController
    _pageController = PageController(initialPage: _currentIndex);
    super.initState();
  }

  late Future<List<VideoType>> _videoTypeFuture;

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    animationController?.dispose();
    super.dispose();
  }

  Widget _buildLazyPage(VideoType videoType) {
    return FutureBuilder<Map<String, List<VideoRespVo>>>(
      future: _httpsRecomVideos.loadRecomVideoList(videoType),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Loading failed, please click refresh',
                  style: TextStyle(color: Colors.red),
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // 触发重新加载
                      _httpsRecomVideos.loadRecomVideoList(videoType);
                    });
                  },
                  child: Text('Refresh'),
                ),
              ],
            ),
          );
        }
        return KeepAliveWrapper(
            child: _buildContent(snapshot.data!, videoType));
      },
    );
  }

  Widget _buildContent(
      Map<String, List<VideoRespVo>> videosMap, VideoType videoType) {
    List<VideoRespVo> recomVideos = videosMap[WorkType.recom.name] ?? [];
    List<VideoRespVo> bannerVideos = videosMap[WorkType.banner.name] ?? [];
    List<VideoRespVo> newReleaseVideos =
        videosMap[WorkType.new_release.name] ?? [];
    List<VideoRespVo> highScoreVideos =
        videosMap[WorkType.high_score.name] ?? [];

    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        // logger.d("滚动通知");
        // if (notification is ScrollEndNotification && _isSwiping) {
        //   setState(() => _isSwiping = false);
        //   _loadCurrentPage();
        // }
        return false;
      },
      child: ListView(
        physics: ClampingScrollPhysics(),
        children: [
          if (bannerVideos.isNotEmpty) _buildBanner(bannerVideos),
          if (videoType.name == "Featured") ...[
            const SizedBox(height: 20),
            TitleView(
                titleTxt: 'You May Also Like',
                subTxt: 'more',
                videoTypes: videoTypes),
            AreaListView(videoRespVoList: recomVideos),
          ],
          TitleView(
              titleTxt: 'New released', subTxt: 'more', videoTypes: videoTypes),
          AreaListView(videoRespVoList: newReleaseVideos),
          TitleView(
              titleTxt: 'High score', subTxt: 'more', videoTypes: videoTypes),
          AreaListView(videoRespVoList: highScoreVideos)
        ],
      ),
    );
  }

  ///顶部banner
  Widget _buildBanner(List<VideoRespVo> bannerVideos) {
    return Padding(
      padding: const EdgeInsets.only(left: 0, right: 0, top: 10),
      child: CarouselSlider(
        options: CarouselOptions(
          aspectRatio: 712 / 396,
          autoPlay: true,
          pauseAutoPlayInFiniteScroll: true,
          enlargeCenterPage: true,
          viewportFraction: 0.95,
          enlargeFactor: 0.12,
          autoPlayInterval: Duration(seconds: 5),
          autoPlayAnimationDuration: Duration(milliseconds: 1000),
          autoPlayCurve: Curves.fastOutSlowIn,
          enableInfiniteScroll: true,
          // padEnds: false,
          // enlargeStrategy : CenterPageEnlargeStrategy.zoom,
          scrollDirection: Axis.horizontal,
        ),
        items: bannerVideos.map((i) => _buildBannerItem(i)).toList(),
      ),
    );
  }

  Widget _buildBannerItem(VideoRespVo video) {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, UnitRouter.video_detail,
          arguments: video),
      child: Stack(
        children: [
          Positioned(
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              child: AspectRatio(
            aspectRatio: 16/9,
            child: CachedNetworkImage(
              imageUrl: video.coverImage!,
              fit: BoxFit.cover,
              placeholder: (context, url) =>
                  Center(child: CircularProgressIndicator()),
              errorWidget: (context, url, error) => Icon(Icons.error),
            ),
          )),
          //左下角显示标题
          Positioned(
            left: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.only(left: 10, bottom: 15),
              child: Text(
                video.name,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Theme(
      data: HotelAppTheme.buildDarkTheme(),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0x4D000000).withOpacity(0.5),
              Color(0xB3C50101).withOpacity(0.5)
            ],
          ),
        ),
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize: Size.zero,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              title: const Text('MovieNest'),
            ),
          ),
          backgroundColor: Color(0xdd121212),
          body: Column(
            children: <Widget>[
              Expanded(
                child: NestedScrollView(
                  controller: _scrollController,
                  headerSliverBuilder: (context, innerBoxIsScrolled) => [
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) => Padding(
                          padding: EdgeInsets.only(top: 18),
                          child: getSearchBarUI(),
                        ),
                        childCount: 1,
                      ),
                    ),
                  ],
                  body: FutureBuilder<List<VideoType>>(
                    future: _videoTypeFuture,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Center(child: CircularProgressIndicator());
                      }
                      if (snapshot.hasError) {
                        return Center(child: Text('Error: ${snapshot.error}'));
                      }
                      videoTypes = snapshot.data!;
                      _initializePages(videoTypes);
                      return Column(
                        children: [
                          _buildTabBar(videoTypes),
                          Expanded(child: _buildTabContent()),
                        ],
                      );
                    },
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void _initializePages(List<VideoType> types) {
    if (_pages.isNotEmpty) return;

    _pages = List.generate(types.length, (index) => Container());
    _loadedFlags = List.generate(types.length, (index) => false);
    _tabController = TabController(length: types.length, vsync: this);

    // 修改TabController监听逻辑
    _tabController.addListener(_handleTabChange);
    // 预加载第一个页面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadPage(0);
      }
    });
  }


  void _handleTabChange() {
    if (!_tabController.indexIsChanging && _tabController.index != _currentIndex) {
      final page = _tabController.index;
      logger.d("程序触发Tab切换: $page");
      _handleManualTabChange(page);
    }
  }

  void _handleManualTabChange(int page) {
    _pageController.jumpToPage(page); // 改为直接跳转
    setState(() => _currentIndex = page);
    _loadPage(page);
  }


  void _loadPage(int page) {
    if (!_loadedFlags[page] && mounted) {
      logger.d("加载页面$page");
      setState(() {
        _loadedFlags[page] = true;
        _pages[page] = _buildLazyPage(videoTypes[page]);
      });
    }
  }

  Widget _buildTabBar(List<VideoType> types) {
    return TabBar(
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      labelColor: HotelAppTheme.buildDarkTheme().primaryColor,
      unselectedLabelColor: Colors.white,
      controller: _tabController,
      labelStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      indicatorColor: Color(0xFFFE4600),
      tabs: types.map((type) => Tab(text: type.name)).toList(),
      onTap: (index) => _handleManualTabChange(index),
    );
  }

  Widget _buildTabContent() {
    return PageView.builder(
      controller: _pageController,
      itemCount: videoTypes.length,
      onPageChanged: (index) {
        logger.d("用户滑动切换页面: $index");
        if (!_tabController.indexIsChanging) {
          _tabController.animateTo(index); // 仅更新Tab状态
        }
        setState(() => _currentIndex = index);
        _loadPage(index);
      },
      itemBuilder: (context, index) => _pages[index],
    );
  }


  Widget getSearchBarUI() {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Container(
                decoration: BoxDecoration(
                  // border: Border.all(
                  //   color: HotelAppTheme.buildDarkTheme().primaryColor,
                  //   width: 1.0,
                  // ),
                  color: Color(0x22FE4600),
                  borderRadius: const BorderRadius.all(
                    Radius.circular(10.0),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, top: 8, bottom: 8),
                  child: InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, UnitRouter.video_search);
                    },
                    child: Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.magnifyingGlass,
                          size: 20,
                          color: HotelAppTheme.buildDarkTheme().primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'The Watchers',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF909090),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              focusColor: Colors.transparent,
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.grey.withOpacity(0.2),
              borderRadius: const BorderRadius.all(
                Radius.circular(4.0),
              ),
              onTap: () {
                Navigator.push<dynamic>(
                  context,
                  SlidePageRoute(
                    child: FiltersScreen(videoTypes: videoTypes),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Row(
                  children: <Widget>[
                    Text(
                      'Filter',
                      style: TextStyle(
                        fontWeight: FontWeight.w100,
                        fontSize: 16,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(Icons.filter_alt_sharp, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class KeepAliveWrapper extends StatefulWidget {
  final Widget child;

  const KeepAliveWrapper({required this.child});

  @override
  _KeepAliveWrapperState createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
