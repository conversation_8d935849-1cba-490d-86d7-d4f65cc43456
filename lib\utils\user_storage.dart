import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pieces_ai/authentication/models/user.dart';
import 'package:app/app/cons/sp.dart';

class UserStorage {
  static final UserStorage _instance = UserStorage._internal();
  static SharedPreferences? _prefs;

  factory UserStorage() {
    return _instance;
  }

  UserStorage._internal();

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// 保存用户数据到本地
  Future<bool> saveUser(User user) async {
    if (_prefs == null) {
      await init();
    }
    final userJson = json.encode(user.toJson());
    return await _prefs!.setString(SpKey.userKey, userJson);
  }

  /// 从本地获取用户数据
  Future<User?> getUser() async {
    if (_prefs == null) {
      await init();
    }
    final userJson = _prefs!.getString(SpKey.userKey);
    if (userJson == null) return null;
    return User.fromJson(json.decode(userJson));
  }

  /// 清除本地用户数据
  Future<bool> clearUser() async {
    if (_prefs == null) {
      await init();
    }
    return await _prefs!.remove(SpKey.userKey);
  }
} 