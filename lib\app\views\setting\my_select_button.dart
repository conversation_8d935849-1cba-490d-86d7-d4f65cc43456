import 'package:flutter/material.dart';

class MySelectButton extends StatefulWidget {
  final double? width;
  final int? heigth;
  final double? lableWidth;
  final String lableText;
  final List<String> list;
  final String selectItem;
  final onSelectChange textChange;

  const MySelectButton(
      {Key? key,
      this.width = 300,
      this.heigth,
      this.lableWidth,
      required this.lableText,
      required this.list,
      required this.selectItem,
      required this.textChange})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyInputButtonState();
}

typedef onSelectChange = void Function(String);

class _MyInputButtonState extends State<MySelectButton> {
  String _selectedValue = '';

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.selectItem;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4.0),
      child: Container(
        color: Color(0xFFA6A6A6),
        width: widget.width,
        height: 36,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4.0),
              child: Container(
                width: widget.lableWidth,
                height: 36,
                alignment: Alignment.center,
                color: Colors.black,
                child: Text(
                  widget.lableText,
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Expanded(
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: DropdownButton<String>(
                      icon: const Icon(Icons.arrow_downward),
                      underline: Container(
                        height: 0,
                        color: Colors.transparent,
                      ),
                      iconSize: 20,
                      iconEnabledColor: Colors.black,
                      isExpanded: true,
                      itemHeight: 48,
                      menuMaxHeight: 220,
                      borderRadius: BorderRadius.circular(8),
                      items: widget.list
                          .map((String e) => DropdownMenuItem<String>(
                              value: e, child: Text(e)))
                          .toList(),
                      onChanged: (String? value) {
                        print(value);
                        setState(() {
                          _selectedValue = value!;
                        });
                        widget.textChange(value!);
                      },
                      value: _selectedValue,
                    )))
          ],
        ),
      ),
    );
  }
}
