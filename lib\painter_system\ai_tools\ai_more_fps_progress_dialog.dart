import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/material.dart';
import 'package:image_compare_2/image_compare_2.dart';
import 'package:path/path.dart' as path;
import 'package:pieces_ai/utils/http_utils/http_util.dart';

import '../../utils/file_utils/file_util.dart';

///Ai视频补帧进度显示
class MoreFpsProgressDialog extends StatefulWidget {
  final Function(String) onComplete;
  final String videoPath;
  final String videoName;
  final int fpsTotal;
  final int newFps;

  const MoreFpsProgressDialog({
    super.key,
    required this.onComplete,
    required this.videoPath,
    required this.videoName,
    required this.fpsTotal,
    required this.newFps,
  });

  @override
  _ProgressDialogState createState() => _ProgressDialogState();
}

class _ProgressDialogState extends State<MoreFpsProgressDialog> {
  double totalProgress = 0.0;
  String fpsProgressText = "";
  int count = 0;

  @override
  void initState() {
    _startVideo();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
                Color(0xFF12CDD9)), // 设置CircularProgressIndicator颜色
          ),
          SizedBox(height: 20),
          Text(
            fpsProgressText,
            style: TextStyle(
              color: Color(0xFF12CDD9), // 设置文字颜色
              fontSize: 16,
              decoration: TextDecoration.none,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startVideo() async {
    var moreFpsVideoPath;
    if (widget.videoPath.startsWith("http")) {
      //下载视频到本地
      String videoSavePath = await FileUtil.getVideoCopyMp4Folder();
      String videoFilePath = videoSavePath +
          FileUtil.getFileSeparate() +
          widget.videoName +
          ".mp4";
      await HttpUtil.instance.client.download(widget.videoPath, videoFilePath,
          onReceiveProgress: (int get, int total) {
        String progress = ((get / total) * 100).toStringAsFixed(2);
        setState(() {
          fpsProgressText = "下载视频：" + '$progress%';
        });
      });
      moreFpsVideoPath =
          await _nextStepVideoFps(videoFilePath, widget.videoName);
      await File(videoFilePath).delete();
    } else {
      moreFpsVideoPath =
          await _nextStepVideoFps(widget.videoPath, widget.videoName);
    }
    // Navigator.pop(context);
    widget.onComplete.call(moreFpsVideoPath);
  }

  ///Ai视频补帧下一步
  Future<String> _nextStepVideoFps(String videoPath, String videoName) async {
    String rootDirectory = path.dirname(path.absolute(videoPath));
    String aiFpsRootPath = await FileUtil.getVideoAiFpsFolder(rootDirectory);
    // 解析后的Frame图片
    String imageInputFrame =
        aiFpsRootPath + FileUtil.getFileSeparate() + "inputFrame";
    if (!await Directory(imageInputFrame).exists()) {
      await Directory(imageInputFrame).create(recursive: true);
    }
    String imageOutputFrame =
        aiFpsRootPath + FileUtil.getFileSeparate() + "outputFrame";
    if (!await Directory(imageOutputFrame).exists()) {
      await Directory(imageOutputFrame).create(recursive: true);
    }
    // String audioFilePath =
    //     videoSavePath + FileUtil.getFileSeparate() + videoName + ".mp3";
    // //不阻塞
    // if (!await Directory(imageOut).exists()) {
    //   await Directory(imageOut).create(recursive: true);
    // } else {
    //   //表示这个视频已经解析过
    //   print("当前视频已经解析过：" + videoPath);
    //   await Directory(imageOut).delete(recursive: true);
    //   await Directory(imageOut).create(recursive: true);
    // }
    final ffmpegDirectory = Directory.current;
    //
    // setState(() {
    //   fpsProgressText = "分离音频...";
    // });
    // final cliCommandAudio = FfmpegCommand.simple(
    //   ffmpegPath: directory.path + "\\ffmpeg.exe",
    //   inputs: [
    //     FfmpegInput.asset(videoPath),
    //   ],
    //   args: [
    //     CliArg.logLevel(LogLevel.info),
    //     const CliArg(name: 'vn'),
    //     const CliArg(name: 'y'),
    //     const CliArg(name: 'c:a', value: "mp3"),
    //     // const CliArg(name: 'acodec', value: "libmp3lame"),
    //     // TODO: need to generalize knowledge of when to use vsync -2
    //     // const CliArg(name: 'vsync', value: '2'),
    //   ],
    //   outputFilepath: audioFilePath,
    // );
    // final processAudio = await Ffmpeg().run(cliCommandAudio);
    // processAudio.stderr.transform(utf8.decoder).listen((data) {
    //   // print(data);
    // });
    // await processAudio.exitCode;
    // if (await File(audioFilePath).exists()) {
    //   print('DONE mp3 地址为:' + audioFilePath);
    //   widget.draftRender.audioPath = audioFilePath;
    // }

    setState(() {
      fpsProgressText = "开始视频解析...";
    });
    String rootCli = ffmpegDirectory.path + "\\ffmpeg.exe";
    if (Platform.isMacOS) {
      rootCli = ffmpegDirectory.path + "/ffmpeg";
    }

    setState(() {
      fpsProgressText = "开始启动Ai补帧...";
    });
    // 指定exe文件路径和命令参数
    String exePath = ffmpegDirectory.path + "\\rife-ncnn-vulkan";
    List<String> arguments = [
      "-i",
      imageInputFrame,
      "-n",
      widget.fpsTotal.toString(),
      "-m",
      "rife-v4.6",
      "-o",
      imageOutputFrame
    ];
    // 启动进程
    Process processMoreFps = await Process.start(exePath, arguments);
    // 获取进程的标准输出流
    processMoreFps.stdout.transform(utf8.decoder).listen((data) {
      print('stdout: $data');
    });

    // 获取进程的标准错误流
    processMoreFps.stderr.transform(utf8.decoder).listen((data) {
      print('stderr: $data');
    });
    await processMoreFps.exitCode;
    print('DONE more FPS:');

    setState(() {
      fpsProgressText = "重新生成视频...";
    });

    String ffmpegPath = ffmpegDirectory.path + "\\ffmpeg.exe";
    String newVideoPath =
        aiFpsRootPath + FileUtil.getFileSeparate() + "fps_24_" + videoName;
    List<String> argumentsMergerVideo = [
      "-y",
      "-framerate", widget.newFps.toString(),
      "-i", imageOutputFrame + FileUtil.getFileSeparate() + "%08d.png",
      "-c:a", "copy",
      "-crf", "15",
      "-c:v", "libx264",
      "-pix_fmt", "yuv420p",
      // "-b:v", "8M", // 设置视频比特率为5Mbps（可以根据需求调整）
      newVideoPath
    ];
    // 启动进程
    Process processMergerVideo =
        await Process.start(ffmpegPath, argumentsMergerVideo);
    // 获取进程的标准输出流
    processMergerVideo.stdout.transform(utf8.decoder).listen((data) {
      print('stdout: $data');
    });

    // 获取进程的标准错误流
    processMergerVideo.stderr.transform(utf8.decoder).listen((data) {
      print('stderr: $data');
    });
    await processMergerVideo.exitCode;
    print('DONE 重新合成视频:');

    //清理内容
    FileUtil.deleteFolderContent(imageInputFrame);
    FileUtil.deleteFolderContent(imageOutputFrame);

    // final imageOutDir = Directory(imageOut);
    // List<String> images = [];
    //
    // await for (FileSystemEntity entity in imageOutDir.list()) {
    //   if (entity is File && entity.path.endsWith('.jpg')) {
    //     images.add(entity.path);
    //   }
    // }
    //
    // // 耗时操作
    // // List<String> deleteFiles = await compute(findUnusedImages, new ComputeInput(images, (message) => {
    // //   debugPrint("进度："+message)
    // //   // fpsProgressText = message
    // // }));
    // List<String> deleteFiles =
    //     await runBackgroundTask(images,(message){
    //       setState(() {
    //         fpsProgressText = message;
    //       });
    //     });
    //
    // // 删除没用的文件
    // for (var deletePath in deleteFiles) {
    //   //sync阻塞
    //   File(deletePath).deleteSync();
    // }
    //
    // final imagesLeft = imageOutDir
    //     .listSync()
    //     .whereType<File>()
    //     .where((file) => file.path.endsWith('.jpg'))
    //     .map((file) => file.path)
    //     .toList();
    //
    // //生成空的
    // TweetScript tweetScript = TweetScript.generateEmptyByImages(imagesLeft);
    // tweetScript.title = widget.draftRender.name;
    // tweetScript.icon = widget.aiStyleChild.icon;
    //
    // int styleType = 0;
    // //设置风格
    // Map<String, dynamic> presetInfo =
    //     jsonDecode(widget.aiStyleChild.presetInfo);
    // String styleName = presetInfo['style_name'];
    // if (styleName.isEmpty) {
    //   tweetScript.aiPaint.styleName = widget.aiStyleChild.modelFileName;
    // } else {
    //   tweetScript.aiPaint.styleName = presetInfo['style_name'];
    // }
    // tweetScript.aiPaint.sampling = presetInfo['sampling'];
    // tweetScript.aiPaint.steps = presetInfo['steps'];
    // tweetScript.aiPaint.lora = presetInfo['lora'];
    // tweetScript.aiPaint.negativePrompt = presetInfo['negative_prompt'];
    // //这里还要重新赋值一下
    // if (presetInfo.containsKey("hd")) {
    //   // Map<String,dynamic> hdMap = jsonDecode(presetInfo['hd']);
    //   tweetScript.aiPaint.hd.modelType = presetInfo['hd']['model_type'];
    //   tweetScript.aiPaint.hd.scale = 2.0;
    //   tweetScript.aiPaint.hd.strength = presetInfo['hd']['strength'];
    //   print("下发包含hd参数：" + presetInfo.toString());
    // }
    //
    // tweetScript.aiPaint.prompt = presetInfo['prompt'];
    // tweetScript.aiPaint.cfgScale = presetInfo['cfg_scale'];
    // tweetScript.aiPaint.modelClass = presetInfo['model_class'];
    //
    // if (widget.aiStyleChild.id == -123) {//本地SD风格类型ID
    //   styleType = 1;
    // }
    //
    //
    // //设置音色
    // tweetScript.tts.type = widget.aiTtsStyle.type;
    // tweetScript.tts.style = widget.aiTtsStyle.style;
    //
    // widget.draftRender.tweetScript = tweetScript;
    // widget.draftRender.styleId = widget.aiStyleChild.id;
    // widget.draftRender.styleType = styleType;
    // return widget.draftRender;
    Navigator.of(context).pop();
    return newVideoPath;
  }

  ///超大量后台执行线程
  Future<List<String>> runBackgroundTask(
      List<String> images, Function(String) updateTextDisplay) async {
    // 创建一个ReceivePort来接收后台线程的消息
    ReceivePort receivePort = ReceivePort();
    // 创建一个Isolate，并传入ReceivePort的sendPort作为参数
    Isolate isolate = await Isolate.spawn(
        findUnusedImages, new ComputeInput(images, receivePort.sendPort));
    // 创建一个Completer来等待结果
    Completer<List<String>> completer = Completer();

    // 监听ReceivePort接收到的消息
    receivePort.listen((message) {
      if (message is String) {
        updateTextDisplay.call(message);
        debugPrint("收到消息：" + message.toString());
      } else {
        // 收到消息后关闭ReceivePort和Isolate，并将结果返回给UI线程
        receivePort.close();
        isolate.kill(priority: Isolate.immediate);
        completer.complete(message);
      }
    });

    // 返回Completer的Future
    return completer.future;
  }

  static Future<List<String>> findUnusedImages(
      ComputeInput computeInput) async {
    List<String> deleteFiles = [];
    List<String> images = computeInput.images;
    String firstImage = images[0];

    for (int i = 1; i < images.length; i++) {
      final secondImage = images[i];
      int start = DateTime.now().millisecondsSinceEpoch;

      final similarity = await calculateSimilarity(firstImage, secondImage);
      debugPrint("算法耗时：${DateTime.now().millisecondsSinceEpoch - start}");
      debugPrint("差异度：" + similarity.toString());

      if (similarity < 0.025) {
        deleteFiles.add(images[i]);
      } else {
        firstImage = secondImage;
      }

      // 计算进度
      double progressPercent = (i + 1) / images.length * 100;
      String progress = "Ai分析视频中(当前进度：${progressPercent.toStringAsFixed(0)}%)";
      computeInput.sendPort.send(progress);
    }
    computeInput.sendPort.send(deleteFiles);
    return deleteFiles;
  }

  static Future<double> calculateSimilarity(String first, String second) async {
    debugPrint("first imag:" + first + " second image:" + second);
    var file1 = File(first);
    var file2 = File(second);
    var byteResult = await compareImages(
        src1: file1, src2: file2, algorithm: ChiSquareDistanceHistogram());
    return byteResult;
  }
}

class ComputeInput {
  List<String> images;
  SendPort sendPort;

  ComputeInput(this.images, this.sendPort);
}
