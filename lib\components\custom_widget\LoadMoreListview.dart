import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../app/api_https/ai_recom_videos_repository.dart';
import '../../app/api_https/impl/https_recom_videos.dart';
import '../../app/model/videos/video_resp.dart';

class PullToRefreshAndLoadMore extends StatefulWidget {
  @override
  _PullToRefreshAndLoadMoreState createState() =>
      _PullToRefreshAndLoadMoreState();
}

class _PullToRefreshAndLoadMoreState extends State<PullToRefreshAndLoadMore> {
  final List<VideoRespVo> _items = [];
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMore = true; // 表示是否还有更多数据可加载
  final RecomVideosRepository _httpsRecomVideos = HttpsRecomVideos();
  late Future<void> _initialLoadFuture;
  int _page = 0; // 页码变量

  @override
  void initState() {
    super.initState();
    _initialLoadFuture = _initialLoad();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _initialLoad() async {
    List<VideoRespVo> newItems =
        await _httpsRecomVideos.loadTypeData(_page, 10,1, "", 2015, 0, 0);
    setState(() {
      _items.addAll(newItems);
    });
  }

  Future<void> _onRefresh() async {
    _page = 0; // 重置页码
    List<VideoRespVo> newItems =
        await _httpsRecomVideos.loadTypeData(_page, 10,1, "", 2015, 0, 0);
    setState(() {
      _items.clear();
      _items.addAll(newItems);
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !_isLoadingMore &&
        _hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;
    setState(() {
      _isLoadingMore = true;
    });

    _page++; // 增加页码
    List<VideoRespVo> newItems =
        await _httpsRecomVideos.loadTypeData(_page, 10,1, "", 2015, 0, 0);

    if (mounted) {
      setState(() {
        _items.addAll(newItems);
        if (newItems.isEmpty) {
          _hasMore = false;
        }
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initialLoadFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error loading data'));
        } else {
          return RefreshIndicator(
            onRefresh: _onRefresh,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _hasMore ? _items.length + 1 : _items.length,
              itemBuilder: (context, index) {
                if (index == _items.length && _hasMore) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                return _buildItem(_items[index]);
              },
            ),
          );
        }
      },
    );
  }

  _buildItem(VideoRespVo item) {
    return Ink(
      padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .pushNamed(UnitRouter.video_detail, arguments: item);
        },
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cover image
              Container(
                width: 100,
                height: 150,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(item.coverImage ?? ""),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              SizedBox(width: 16.0),
              // Movie details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name
                    Text(
                      item.name ?? "",
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.0),
                    // Time
                    Text(
                      DateFormat('yyyy-MM-dd HH:mm:ss')
                          .format(item.releaseTime ?? DateTime.now()),
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 8.0),
                    // Category
                    Text(
                      item.type,
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: 8.0),
                    // Description
                    Text(
                      item.description ?? "",
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.0),
                    // Rating
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.yellow[700],
                          size: 20.0,
                        ),
                        SizedBox(width: 4.0),
                        Text(
                          item.score?.toString() ?? "N/A",
                          style: TextStyle(
                            fontSize: 14.0,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
