import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:pieces_ai/app/model/diy/diy_roles.dart';

import '../../../../app/gen/toly_icon_p.dart';

/// 本地草稿单个草稿的视图
class MyRoleListItem extends StatefulWidget {
  final CustomRolePicture diyAiRole;
  final Function(CustomRolePicture)? onDeleteItemClick;
  final Function(CustomRolePicture)? onClickItemClick;
  final Function(CustomRolePicture)? onEditItemClick;

  const MyRoleListItem({
    Key? key,
    this.onDeleteItemClick,
    this.onClickItemClick,
    this.onEditItemClick,
    required this.diyAiRole,
  }) : super(key: key);

  @override
  State<MyRoleListItem> createState() => _DraftListItemState();
}

class _DraftListItemState extends State<MyRoleListItem>
    with AutomaticKeepAliveClientMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant MyRoleListItem oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: () {
        widget.onClickItemClick?.call(widget.diyAiRole);
      },
      child: widget.diyAiRole.id == -3
          ? Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AspectRatio(
                  aspectRatio: 1.0, // 强制宽高比为1:1
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Color(0xFFCCCCCC),
                    ),
                    child: IconButton(
                      color: Color(0xFF808080),
                      icon: Icon(
                        TolyIconP.add,
                        size: 40,
                      ),
                      onPressed: () =>
                          widget.onClickItemClick?.call(widget.diyAiRole),
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  widget.diyAiRole.name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AspectRatio(
                  aspectRatio: 1.0, // 强制宽高比为1:1
                  child: Container(
                    child: _buildChild(context),
                    decoration: BoxDecoration(
                      image: widget.diyAiRole.icon.contains("https://+")
                          ? DecorationImage(
                              image: FileImage(File(
                                  widget.diyAiRole.icon.split("https://+")[1])),
                              fit: BoxFit.cover,
                            )
                          : DecorationImage(
                              image: CachedNetworkImageProvider(
                                  widget.diyAiRole.icon),
                              fit: BoxFit.cover,
                            ),
                      borderRadius: const BorderRadius.all(Radius.circular(8)),
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  widget.diyAiRole.name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
    );
  }

  _buildChild(BuildContext context) {
    return Stack(
      children: <Widget>[
        // _buildTitle(themeColor),
        Align(
          alignment: Alignment.topRight,
          child: _buildPop(),
        ),
      ],
    );
  }

  final Map<String, IconData> map = const {
    "删除": Icons.add_comment,
  };

  Widget _buildPop() {
    return PopupMenuButton<String>(
      tooltip: '',
      child: Container(
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Color(0x8C000000),
        ),
        child: Icon(Icons.more_horiz),
        width: 30,
        height: 30,
      ),
      itemBuilder: (context) => buildPopItems(),
      offset: const Offset(0, 40),
      color: const Color(0xFF1C1C1C),
      elevation: 1,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8))),
      onSelected: (e) {
        print(e);
        if (e == '更名') {
        } else if ("删除" == e) {
          widget.onDeleteItemClick?.call(widget.diyAiRole);
        }
      },
      onCanceled: () => print('onCanceled'),
    );
  }

  List<PopupMenuItem<String>> buildPopItems() {
    return map.keys
        .toList()
        .map((e) => PopupMenuItem<String>(
            value: e,
            height: 30,
            child: Wrap(
              spacing: 5,
              children: <Widget>[
                // Icon(map[e], color: Colors.blue),
                Text(e,
                    style: const TextStyle(color: Colors.white, fontSize: 12)),
              ],
            )))
        .toList();
  }
}
