import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:pieces_ai/app/navigation/mobile/state/appState.dart';
import 'package:pieces_ai/widget_ui/desk_ui/category_panel/categories/categories_home_page.dart';
import 'package:pieces_ai/widget_ui/desk_ui/category_panel/my_cloud/my_cloud_home_page.dart';
import 'package:provider/provider.dart';

import '../../../authentication/blocs/authentic/bloc.dart';
import '../../../authentication/blocs/authentic/state.dart';
import '../../../authentication/views/mobile/login/login_page.dart';
import '../../../components/custom_widget/hotel_booking/hotel_app_theme.dart';
import '../../../components/custom_widget/hotel_booking/hotel_home_screen.dart';
import '../../../utils/toast.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class PieceMobileNavigation extends StatefulWidget {
  const PieceMobileNavigation({super.key});

  @override
  _UnitDeskNavigationState createState() => _UnitDeskNavigationState();
}

class _UnitDeskNavigationState extends State<PieceMobileNavigation> {
  int _currentIndex = 0;
  final List<Widget> _pages = [
    KeepAlivePage(child: HotelHomeScreen()),
    KeepAlivePage(child: CategoriesHomePage()),
    KeepAlivePage(
        child: MyCloudHomePage(
      key: myRolesKey,
    )),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var state = Provider.of<AppState>(context, listen: false);
      state.setPageIndex = 0;
    });
    // Set immersive status bar
    // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    //   statusBarColor: Colors.transparent, // Make status bar transparent
    //   statusBarIconBrightness: Brightness.dark, // Set status bar icon color
    // ));
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _body() {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: _listenLoginState,
      builder: (context, state) {
        return IndexedStack(
          index: _currentIndex,
          children: _pages,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, dynamic result) {
          // int currentIndex = context.read<AppState>().pageIndex;
          // logger.d("didPop:$didPop,result:$result,currentIndex:$currentIndex");
          // if (currentIndex != 0) {
          //   Provider.of<AppState>(context, listen: false).setPageIndex = 0;
          // } else {
          //   //退出应用
          //   SystemNavigator.pop();
          // }
          int currentIndex = _currentIndex;
          logger.d("didPop:$didPop,result:$result,currentIndex:$currentIndex");
          if (currentIndex != 0) {
            setState(() {
              _currentIndex = 0;
            });
          } else {
            //退出应用
            SystemNavigator.pop();
          }
        },
        child: Scaffold(
            bottomNavigationBar: BottomNavigationBar(
              backgroundColor: Color(0xFF121212),
              currentIndex: _currentIndex,
              selectedItemColor: HotelAppTheme.buildDarkTheme().primaryColor,
              unselectedItemColor: Colors.white70,
              onTap: (index) {
                if (_currentIndex != 1) {
                  // TODO:有个奇怪的软键盘弹出bug
                  FocusManager.instance.primaryFocus?.unfocus();
                }
                setState(() {
                  _currentIndex = index;
                });
              },
              // unselectedFontSize: 10,
              // selectedFontSize: 12,
              items: [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: AppLocalizations.of(context).create,
                ),
                BottomNavigationBarItem(
                    icon: Icon(Icons.category),
                    label: AppLocalizations.of(context).goods),
                BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.person_alt),
                    label: AppLocalizations.of(context).me),
              ],
            ),
            resizeToAvoidBottomInset: false,
            body: _body()));
  }

  void _listenLoginState(BuildContext context, AuthState state) {
    logger.d("首页顶部 收到登录成功通知:$state, User:${state.props}");
    if (state is AuthSuccess) {
      logger.d("登录成功:${state.user}");
    }
    if (state is AuthFailure) {
      Toast.error(context, '登录失败！');
    }
  }
}

class KeepAlivePage extends StatefulWidget {
  final Widget child;

  const KeepAlivePage({Key? key, required this.child}) : super(key: key);

  @override
  _KeepAlivePageState createState() => _KeepAlivePageState();
}

class _KeepAlivePageState extends State<KeepAlivePage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
