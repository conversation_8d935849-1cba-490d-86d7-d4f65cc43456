                        -HD:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\android_sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=D:\android_sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=D:\android_sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\android_sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\git_resource\movienest-app\build\app\intermediates\cxx\Debug\2x3625f5\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\git_resource\movienest-app\build\app\intermediates\cxx\Debug\2x3625f5\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\git_resource\movienest-app\android\app\.cxx\Debug\2x3625f5\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2