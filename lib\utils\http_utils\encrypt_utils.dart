import 'dart:convert';

import 'package:encrypt/encrypt.dart' as encrypt;

class EncryptUtils {
  static final key = encrypt.Key.fromUtf8('00012movienest12');
  static final iv = encrypt.IV.fromSecureRandom(16); // 每次生成新的IV

  static final encrypter = encrypt.Encrypter(
      encrypt.AES(key, mode: encrypt.AESMode.cbc, padding: 'PKCS7'));

  static String encryptData(String data) {
    final encrypted = encrypter.encrypt(data, iv: iv);
    // 创建包含加密数据和IV的Map
    final jsonData = {'data': encrypted.base64, 'iv': base64Encode(iv.bytes)};
    // 将整个Map转换为JSON字符串并Base64编码
    return base64Encode(utf8.encode(json.encode(jsonData)));
  }

  static String decryptData(String jsonData) {
    final encryptedData = encrypt.Encrypted.fromBase64(jsonData);
    return encrypter.decrypt(encryptedData, iv: iv);
  }
}
