class HotelListData {
  HotelListData({
    this.imagePath = '',
    this.titleTxt = '',
    this.subTxt = "",
    this.dist = 1.8,
    this.reviews = 80,
    this.rating = 4.5,
    this.perNight = 180,
  });

  String imagePath;
  String titleTxt;
  String subTxt;
  double dist;
  double rating;
  int reviews;
  int perNight;

  static List<HotelListData> getData() {
    List<HotelListData> list = [];
    list.add(HotelListData(
      imagePath: 'assets/images/hotel/hotel_1.png',
      titleTxt: 'Hotel 1',
      subTxt: '200 Main Road, New York',
      dist: 2.0,
      reviews: 80,
      rating: 4.4,
      perNight: 180,
    ));
    list.add(HotelListData(
      imagePath: 'assets/images/hotel/hotel_2.png',
      titleTxt: 'Hotel 2',
      subTxt: '200 Main Road, New York',
      dist: 1.8,
      reviews: 74,
      rating: 4.5,
      perNight: 200,
    ));
    list.add(HotelListData(
      imagePath: 'assets/images/hotel/hotel_3.png',
      titleTxt: 'Hotel 3',
      subTxt: '200 Main Road, New York',
      dist: 5.0,
      reviews: 62,
      rating: 4.0,
      perNight: 60,
    ));
    list.add(HotelListData(
      imagePath: 'assets/images/hotel/hotel_4.png',
      titleTxt: 'Hotel 4',
      subTxt: '200 Main Road, New York',
      dist: 3.2,
      reviews: 90,
      rating: 4.5,
      perNight: 240,
    ));
    list.add(HotelListData(
      imagePath: 'assets/images/hotel/hotel_5.png',
      titleTxt: 'Hotel 5',
      subTxt: '200 Main Road, New York',
      dist: 2.0,
      reviews: 128,
      rating: 4.0,
      perNight: 220,
    ));
    return list;
  }
}
