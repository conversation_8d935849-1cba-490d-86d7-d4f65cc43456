import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:pieces_ai/app/model/VideoType.dart';
import 'package:pieces_ai/components/custom_widget/hotel_booking/hotel_app_theme.dart';

import '../../../app/api_https/ai_recom_videos_repository.dart';
import '../../../app/api_https/impl/https_recom_videos.dart';
import '../../../app/model/videos/video_resp.dart';

///使用GlobalKey来刷新
GlobalKey<_FiltersLoadMoreState> filtersLoadMoreKey = GlobalKey();

class FiltersLoadMore extends StatefulWidget {
  const FiltersLoadMore({Key? key}) : super(key: key);

  @override
  _FiltersLoadMoreState createState() => _FiltersLoadMoreState();
}

class _FiltersLoadMoreState extends State<FiltersLoadMore> {
  final List<VideoRespVo> _items = [];
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMore = true; // 表示是否还有更多数据可加载
  final RecomVideosRepository _httpsRecomVideos = HttpsRecomVideos();
  late Future<void> _initialLoadFuture;
  int _page = 0; // 页码变量
  int year = 2025;
  int sort = 0;
  int count = 15;
  int type_id = 6;
  String region = "United States";
  GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _initialLoadFuture = _initialLoad();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _initialLoad() async {
    List<VideoRespVo> newItems = await await _httpsRecomVideos.loadTypeData(
        _page, count, type_id, region, year, 0, sort);
    setState(() {
      _items.addAll(newItems);
    });
  }

  ///暴漏给外部的刷新方法
  Future<void> refresh(
      {int? year, String? region, int? type_id, int? sort}) async {
    logger.d("刷新数据 主动refresh");
    if (year != null) {
      this.year = year;
    }
    if (region != null) {
      this.region = region;
    }
    if (type_id != null) {
      this.type_id = type_id;
    }
    if (sort != null) {
      this.sort = sort;
    }
    _hasMore = true;
    //回到顶部
    _scrollController.jumpTo(0);
    _refreshIndicatorKey.currentState?.show();
    // await _onRefresh();
  }

  Future<void> _onRefresh() async {
    logger.d("_onRefresh 刷新数据");
    _page = 0; // 重置页码
    List<VideoRespVo> newItems = await _httpsRecomVideos.loadTypeData(
        _page, count, type_id, region, year, 0, sort);
    setState(() {
      _items.clear();
      _items.addAll(newItems);
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !_isLoadingMore &&
        _hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;
    setState(() {
      _isLoadingMore = true;
    });

    _page++; // 增加页码
    List<VideoRespVo> newItems = await _httpsRecomVideos.loadTypeData(
        _page, count, type_id, region, year, 0, sort);

    if (mounted) {
      setState(() {
        _items.addAll(newItems);
        if (newItems.isEmpty) {
          _hasMore = false;
        }
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initialLoadFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
              child: CircularProgressIndicator(
            color: HotelAppTheme.buildDarkTheme().primaryColor,
          ));
        } else if (snapshot.hasError) {
          return Center(child: Text('Error loading data'));
        } else {
          return RefreshIndicator(
            key: _refreshIndicatorKey,
            color: HotelAppTheme.buildDarkTheme().primaryColor,
            onRefresh: _onRefresh,
            child: GridView.builder(
              controller: _scrollController,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // Number of items per row
                childAspectRatio: 0.6, // Adjust the aspect ratio as needed
                mainAxisSpacing: 8.0,
                crossAxisSpacing: 8.0,
              ),
              padding: EdgeInsets.symmetric(horizontal: 8),
              itemCount: _hasMore ? _items.length + 1 : _items.length,
              itemBuilder: (context, index) {
                if (index == _items.length && _hasMore) {
                  return Center(
                      child: CircularProgressIndicator(
                        color: HotelAppTheme.buildDarkTheme().primaryColor,
                      ));
                }
                return _buildItem(_items[index]);
              },
            ),
          );
        }
      },
    );
  }

  _buildItem(VideoRespVo item) {
    return Ink(
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .pushNamed(UnitRouter.video_detail, arguments: item);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover image
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.0),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(item.coverImage ?? ""),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              flex: 8,
            ),
            // Movie details
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    item.name ?? "",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13.0,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                  ),
                  Text(
                    item.description ?? "",
                    style: TextStyle(
                      fontSize: 11.0,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }
}
