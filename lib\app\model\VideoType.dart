import 'package:json_annotation/json_annotation.dart';

part 'VideoType.g.dart';

@JsonSerializable()
class VideoType {
    
    ///ID 编号
    @Json<PERSON>ey(name: "id")
    int id;
    
    ///名称
    @<PERSON><PERSON><PERSON><PERSON>(name: "name")
    String name;

    VideoType({
        required this.id,
        required this.name,
    });

    VideoType copyWith({
        int? id,
        String? name,
    }) => 
        VideoType(
            id: id ?? this.id,
            name: name ?? this.name,
        );

    factory VideoType.fromJson(Map<String, dynamic> json) => _$VideoTypeFromJson(json);

    Map<String, dynamic> toJson() => _$VideoTypeToJson(this);
}