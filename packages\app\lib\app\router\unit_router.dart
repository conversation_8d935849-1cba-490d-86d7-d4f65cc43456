// ignore_for_file: constant_identifier_names
class UnitRouter {
  static const String widget_detail = '/widget_detail';
  static const String widget_scene_edit = '/widget_scene_edit';
  static const String text_2_video = '/text_2_video';

  static const String detail = 'detail';
  static const String search = 'search_bloc';
  static const String nav = 'nav';

  static const String collect = 'CollectPage';
  static const String point = 'IssuesPointPage';
  static const String point_detail = 'IssuesDetailPage';

  static const String setting = 'SettingPage';
  static const String font_setting = 'FountSettingPage';
  static const String theme_color_setting = 'ThemeColorSettingPage';
  static const String code_style_setting = 'CodeStyleSettingPage';
  static const String item_style_setting = 'ItemStyleSettingPage';
  static const String version_info = 'VersionInfo';
  static const String login = 'login';

  static const String ai_style_edit = 'ai_style_edit';
  static const String issues_point = 'IssuesPointPage';

  static const String attr = 'AttrUnitPage';
  static const String bug = 'BugUnitPage';
  static const String layout = 'LayoutUnitPage';
  static const String about_me = 'AboutMePage';
  static const String about_app = 'AboutAppPage';
  static const String register = 'register';

  static const String data_manage = 'DataManagePage';
  static const String article_detail = 'article_detail';

  static const String vip_center = 'vip_center';

  static const String video_detail = 'video_detail';
  static const String video_type_list = 'video_type_list';
  static const String video_search = 'video_search';
  static const String video_filter = 'video_filter';
}
