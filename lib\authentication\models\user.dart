import 'package:json_annotation/json_annotation.dart';

/// create by
part 'user.g.dart';

@JsonSerializable()
class User {
  final String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: "auth_Token")
  final String? authToken;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'head_icon')
  final String headIcon;
  final int gender;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'v_type')
  final int vType;
  final int pegg;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  final int userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'uuid')
  final String? uuid;

  @Json<PERSON><PERSON>(name: 'vip_end_time')
  int? vipEnd;

  ///1非会员 2月卡等级 3季卡等级 4年卡等级 5终身等级
  @<PERSON><PERSON><PERSON><PERSON>(name: 'v_right_level')
  int? vipLevel = 0;

  User({
    required this.name,
    required this.pegg,
    required this.gender,
    required this.vType,
    required this.userId,
    this.uuid,
    required this.headIcon,
    this.vipEnd,
    this.vipLevel,
    required this.authToken,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
