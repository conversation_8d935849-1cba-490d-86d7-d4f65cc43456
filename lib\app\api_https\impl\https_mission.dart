import 'package:flutter/cupertino.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../model/vip/mission.dart';

const String getMissionList = '/mission/list';
const String getMissionDetail = '/mission/detail';

///没人任务相关接口
class HttpsMission {
  Future<MissionPegg?> getMissionPegg() async {
    //查询此用户的当天的是否完成了任务的记录
    var map = await Supabase.instance.client
        .from("t_fission_record")
        .select('count')
        .eq('uid', GlobalInfo().user.userId)
        .eq('createtime', DateTime.now().toString().substring(0, 10));
    int count = map[0]['count'] as int;
    debugPrint("count" + count.toString());
    //这里还有个bug，需要根据mid查询t_fission_info表中的数据

    PostgrestList postgrestList = await Supabase.instance.client
        .from("t_fission_info")
        .select()
        .eq('status', 1);
    List<Mission> missionData = [];
    for (var item in postgrestList) {
      debugPrint("item" + item.toString());
      missionData.add(Mission(
        award: item['award'],
        complete: count,
        condition: item['condition'],
        executeUrl: '',
        icon: item['icon'],
        limitedType: item['limited_type'],
        linkUrl: '',
        mid: item['id'],
        name: item['name'],
        type: item['type'],
        vipType: item['vip_type'],
      ));
    }

    PostgrestList userList = await Supabase.instance.client
        .from("t_user_info")
        .select()
        .eq('uid', GlobalInfo().user.userId);
    int pegg = 0;
    for (var item in userList) {
      pegg = item['pegg'];
    }

    return MissionPegg(
        award: 100, maxAward: 100, mission: missionData, pegg: pegg);
  }

  Future<bool> submitMission({required int mid, required int pegg}) async {
    //向t_fission_record表中插入一条记录
    try {
      var result =
          await Supabase.instance.client.from("t_fission_record").upsert([
        {
          'uid': GlobalInfo().user.userId,
          'mid': mid,
          'createtime': DateTime.now().toString().substring(0, 10),
          'updatetime': DateTime.now().toString().substring(0, 10),
        }
      ]);
      //将任务皮蛋数更新到用户表
      bool update = await Supabase.instance.client.rpc('increment_pegg',
          params: {'v_uid': GlobalInfo().user.userId, 'sum_pegg': pegg});
      debugPrint("result" + result.toString() + "update" + update.toString());
      return update;
    } catch (e) {
      debugPrint("e" + e.toString());
      return false;
    }
  }
}
