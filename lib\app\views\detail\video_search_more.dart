import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../app/api_https/ai_recom_videos_repository.dart';
import '../../../app/api_https/impl/https_recom_videos.dart';
import '../../../app/api_https/impl/yts_api_service.dart';
import '../../../app/model/videos/video_resp.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

///使用GlobalKey来刷新
GlobalKey<_VideoSearchMoreState> videoSearchMoreKey = GlobalKey();

class VideoSearchMore extends StatefulWidget {
  final String words;
  final bool useYtsApi;
  const VideoSearchMore({Key? key, required this.words, this.useYtsApi = false}) : super(key: key);

  @override
  _VideoSearchMoreState createState() => _VideoSearchMoreState();
}

class _VideoSearchMoreState extends State<VideoSearchMore> {
  final List<VideoRespVo> _items = [];
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMore = true;
  final RecomVideosRepository _httpsRecomVideos = HttpsRecomVideos();
  final YtsApiService _ytsApiService = YtsApiService();
  late Future<void> _initialLoadFuture;
  int _page = 0;
  String words = "";
  GlobalKey<RefreshIndicatorState> _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    words = widget.words;
    _initialLoadFuture = _initialLoad();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _initialLoad() async {
    List<VideoRespVo> newItems = await _loadData();
    if (mounted) {
      setState(() {
        _items.clear();
        _items.addAll(newItems);
        _updateHasMore(newItems);
      });
    }
  }

  void _updateHasMore(List<VideoRespVo> newItems) {
    if (widget.useYtsApi) {
      // YTS API: 根据总电影数判断是否还有更多
      final totalItems = _ytsApiService.totalMovies;
      _hasMore = _items.length < totalItems;
      logger.d('YTS API - 当前加载: ${_items.length}, 总数: $totalItems, 是否有更多: $_hasMore');
    } else {
      // 默认API: 根据返回的数量判断是否还有更多
      _hasMore = newItems.length >= 15;
      logger.d('默认API - 本次加载: ${newItems.length}, 是否有更多: $_hasMore');
    }
  }

  Future<List<VideoRespVo>> _loadData() async {
    if (widget.useYtsApi) {
      return await _ytsApiService.searchMovies(words, page: _page + 1, limit: 15);
    } else {
      return await _httpsRecomVideos.loadSearchVideoList(_page, 15, words);
    }
  }

  Future<void> refresh({String? words}) async {
    this.words = words ?? "";
    _page = 0;
    _hasMore = true;
    _refreshIndicatorKey.currentState?.show();
  }

  Future<void> _onRefresh() async {
    _page = 0;
    List<VideoRespVo> newItems = await _loadData();
    if (mounted) {
      setState(() {
        _items.clear();
        _items.addAll(newItems);
        _updateHasMore(newItems);
      });
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore || !_hasMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });

    _page++;
    List<VideoRespVo> newItems = await _loadData();

    if (mounted) {
      setState(() {
        _items.addAll(newItems);
        _updateHasMore(newItems);
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initialLoadFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error loading data'));
        } else {
          return RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _onRefresh,
            child: GridView.builder(
              controller: _scrollController,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // Number of items per row
                childAspectRatio: 0.6, // Adjust the aspect ratio as needed
                mainAxisSpacing: 8.0,
                crossAxisSpacing: 8.0,
              ),
              padding: EdgeInsets.symmetric(horizontal: 8),
              itemCount: _hasMore ? _items.length + 1 : _items.length,
              itemBuilder: (context, index) {
                if (index == _items.length && _hasMore) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                return _buildItem(_items[index]);
              },
            ),
          );
        }
      },
    );
  }

  _buildItem(VideoRespVo item) {
    return Ink(
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .pushNamed(UnitRouter.video_detail, arguments: item);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover image
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.0),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(item.coverImage ?? ""),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              flex: 8,
            ),
            // Movie details
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    item.name ?? "",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13.0,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                  ),
                  Text(
                    item.description ?? "",
                    style: TextStyle(
                      fontSize: 11.0,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }
}
