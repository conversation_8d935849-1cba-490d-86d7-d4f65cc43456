
import 'model/model.dart';
import 'package:storage/storage.dart';


/// 说明: 负责草稿数据的存储和操作接口
abstract class DraftRepository {
  // 获取所有收藏集
  Future<List<Draft>> loadDrafts();

  // 获取 所有收藏集 及 收藏集对应的组件 id 列表
  Future<List<Draft>> loadCategoryData();


  //添加一条草稿数据，并返回自增长的id
  Future<int> addOneDraft(Draft draft);

  //更新收藏集
  Future<bool> updateCategory(Draft draft);

  Future<void> deleteCategory(int id);

  Future<void> toggleCategory(int categoryId, int widgetId);
}