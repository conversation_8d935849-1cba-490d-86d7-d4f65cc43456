// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_2_video_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Text2VideoParam _$Text2VideoParamFromJson(Map<String, dynamic> json) =>
    Text2VideoParam(
      actions: (json['actions'] as List<dynamic>)
          .map((e) => Action.fromJson(e as Map<String, dynamic>))
          .toList(),
      duration: (json['duration'] as num).toInt(),
      lora: json['lora'] as String?,
      negativePrompt: json['negative_prompt'] as String,
      prompt: json['prompt'] as String,
      quality: (json['quality'] as num).toInt(),
      ratio: (json['ratio'] as num).toInt(),
      sampling: json['sampling'] as String?,
      styleName: json['style_name'] as String,
      vae: json['vae'] as String?,
      mode: json['mode'] as String?,
    );

Map<String, dynamic> _$Text2VideoParamToJson(Text2VideoParam instance) =>
    <String, dynamic>{
      'actions': instance.actions,
      'duration': instance.duration,
      'lora': instance.lora,
      'negative_prompt': instance.negativePrompt,
      'prompt': instance.prompt,
      'quality': instance.quality,
      'ratio': instance.ratio,
      'sampling': instance.sampling,
      'style_name': instance.styleName,
      'vae': instance.vae,
      'mode': instance.mode,
    };

Action _$ActionFromJson(Map<String, dynamic> json) => Action(
      frame: json['frame'] as String,
    );

Map<String, dynamic> _$ActionToJson(Action instance) => <String, dynamic>{
      'frame': instance.frame,
    };
