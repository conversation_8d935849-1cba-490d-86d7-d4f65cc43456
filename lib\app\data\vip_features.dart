// vip_features.dart

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class VipFeature {
  final String imagePath;
  final String imagePathYelloew;
  final String text;

  VipFeature(this.imagePath, this.imagePathYelloew, this.text);
}

List<VipFeature> getVipFeatures(BuildContext context) {
  return [
    VipFeature(
        'assets/images/icon/<EMAIL>',
        'assets/images/icon/ad@3x_yellow.png',
        AppLocalizations.of(context).vipCardFunction1),
    VipFeature(
        'assets/images/icon/<EMAIL>',
        'assets/images/icon/movie@3x_yellow.png',
        AppLocalizations.of(context).vipCardFunction2),
    VipFeature(
        'assets/images/icon/<EMAIL>',
        'assets/images/icon/download@3x_yellow.png',
        AppLocalizations.of(context).vipCardFunction3),
    VipFeature(
        'assets/images/icon/<EMAIL>',
        'assets/images/icon/HD@3x_yellow.png',
        AppLocalizations.of(context).vipCardFunction4),
    VipFeature(
        'assets/images/icon/<EMAIL>',
        'assets/images/icon/TV@3x_yellow.png',
        AppLocalizations.of(context).vipCardFunction5),
  ];
}
