// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mission.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MissionPegg _$MissionPeggFromJson(Map<String, dynamic> json) => MissionPegg(
      award: (json['award'] as num).toInt(),
      maxAward: (json['max_award'] as num).toInt(),
      mission: (json['mission'] as List<dynamic>)
          .map((e) => Mission.fromJson(e as Map<String, dynamic>))
          .toList(),
      pegg: (json['pegg'] as num).toInt(),
    );

Map<String, dynamic> _$MissionPeggToJson(MissionPegg instance) =>
    <String, dynamic>{
      'award': instance.award,
      'max_award': instance.maxAward,
      'mission': instance.mission,
      'pegg': instance.pegg,
    };

Mission _$MissionFromJson(Map<String, dynamic> json) => Mission(
      award: (json['award'] as num).toInt(),
      complete: (json['complete'] as num).toInt(),
      condition: (json['condition'] as num).toInt(),
      executeUrl: json['executeUrl'] as String,
      icon: json['icon'] as String,
      limitedType: json['limitedType'] as String,
      linkUrl: json['linkUrl'] as String,
      mid: (json['mid'] as num).toInt(),
      name: json['name'] as String,
      type: (json['type'] as num).toInt(),
      vipType: (json['vipType'] as num).toInt(),
    );

Map<String, dynamic> _$MissionToJson(Mission instance) => <String, dynamic>{
      'award': instance.award,
      'complete': instance.complete,
      'condition': instance.condition,
      'executeUrl': instance.executeUrl,
      'icon': instance.icon,
      'limitedType': instance.limitedType,
      'linkUrl': instance.linkUrl,
      'mid': instance.mid,
      'name': instance.name,
      'type': instance.type,
      'vipType': instance.vipType,
    };
