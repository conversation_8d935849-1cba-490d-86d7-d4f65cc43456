import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2021/1/17
/// contact me <NAME_EMAIL>
/// 说明: 

class IconInput extends StatelessWidget {

  final Widget textFiled;
  final IconData icon;

  const IconInput({Key? key,required this.textFiled,required this.icon}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return   Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withOpacity(0.5),
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(10.0),
      ),
      margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
      child: Row(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 15.0),
            child: Icon(
              icon,
              color: Colors.grey,
            ),
          ),
          Container(
            height: 20.0,
            width: 1.0,
            color: Colors.grey.withOpacity(0.5),
            margin: const EdgeInsets.only(left: 00.0, right: 10.0),
          ),
          Expanded(
            child: textFiled,
          )
        ],
      ),
    );
  }
}
