import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_login/flutter_login.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/api_https/impl/https_user_encryption.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/authentication/models/user.dart' as pieces_user;
import 'package:pieces_ai/utils/analytics_helper.dart';
import 'package:pieces_ai/utils/toast.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

import '../../../../app/navigation/mobile/theme/theme.dart';
import '../../../blocs/authentic/bloc.dart';
import '../../../blocs/authentic/event.dart';
import '../../../../app/api_https/impl/https_user.dart';
import '../../../../app/model/user/user_resp.dart';
import '../../../../utils/user_storage.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

///登录页面
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  void initState() {
    super.initState();
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('login');
  }

  // 添加按钮点击埋点
  void _trackButtonClick(String buttonName) {
    AnalyticsHelper().trackButtonClick(buttonName, position: 'login');
  }

  Duration get loginTime => const Duration(milliseconds: 250);

  Future<String?> _authUser(LoginData data, BuildContext context) async {
    logger.d('Name: ${data.name}, Password: ${data.password}');
    AuthResponse? userCredential;
    final HttpsUserEncryption _httpsUser = new HttpsUserEncryption();

    try {
      userCredential = await Supabase.instance.client.auth
          .signInWithPassword(password: data.password, email: data.name);
      // 登录成功，处理登录后的逻辑
      logger.d('登录成功: ${userCredential.user?.email}');
      
      // Singular埋点 - 登录成功
      await AnalyticsHelper().trackLogin('email', success: true, userId: userCredential.user?.id);
      
      // 获取用户信息
      if (userCredential.user != null) {
        // 向服务器更新用户信息
        UserRespVo? userRespVo = await _httpsUser.login(
            userCredential.user!.email!, userCredential.user!.id);
            
        logger.d('获取到的用户信息: $userRespVo');

        if (userRespVo != null) {
          // 登录成功，将用户信息存储到全局
          var user = pieces_user.User(
              name: userRespVo.uname ?? userCredential.user!.email!,
              pegg: 1000,
              gender: 0,
              vType: 0,
              userId: userRespVo.accessToken.hashCode,
              uuid: userRespVo.uuid,
              vipEnd: userRespVo.vipInfo?.endtimeAt != null ? 
                DateTime.parse(userRespVo.vipInfo!.endtimeAt!).millisecondsSinceEpoch : -1,
              vipLevel: userRespVo.vipInfo != null ? 4 : 0,
              headIcon:
                  'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
              authToken: userRespVo.accessToken);

          GlobalInfo.instance.user = user;
          // 保存用户数据到本地
          await UserStorage().saveUser(user);
          logger.d('邮箱登录成功，用户数据已保存: ${user.toString()}');
          
          // 更新用户状态
          BlocProvider.of<AuthBloc>(context).add(
            UpdateAuthInfo(user: user),
          );
        }
      }
    } on AuthException catch (e) {
      // 处理认证异常
      switch (e.code) {
        default:
          logger.d('登录失败: ${e.message}');
          
          // Singular埋点 - 登录失败
          await AnalyticsHelper().trackLogin('email', success: false);
          
          return e.message;
      }
    } catch (e) {
      // 处理其他异常
      logger.d('发生未知错误: $e');
      
      // Singular埋点 - 登录失败（未知错误）
      await AnalyticsHelper().trackLogin('email', success: false);
      
      return AppLocalizations.of(context).unknownOccurred;
    }

    // 如果没有用户信息，返回错误
    if (userCredential?.user == null) {
      logger.d('用户不存在');
      
      // Singular埋点 - 登录失败（用户不存在）
      await AnalyticsHelper().trackLogin('email', success: false);
      
      return 'User not exists';
    }
    
    return null;
  }

  Future<String?> _signupUser(SignupData data, BuildContext context) async {
    logger.d('Signup Name: ${data.name}, Password: ${data.password}');
    //使用firebase注册用户
    AuthResponse userCredential;
    final HttpsUserEncryption _httpsUser = new HttpsUserEncryption();

    try {
      userCredential = await Supabase.instance.client.auth
          .signUp(password: data.password ?? "", email: data.name);
          
      // Singular埋点 - 注册成功
      await AnalyticsHelper().trackRegister('email', success: true, userId: userCredential.user?.id);
      
      // 获取用户信息
      if (userCredential.user != null) {
        // 向服务器创建/更新用户信息
        UserRespVo? userRespVo = await _httpsUser.login(
            userCredential.user!.email!, userCredential.user!.id);
            
        logger.d('注册后获取到的用户信息: $userRespVo');

        if (userRespVo != null) {
          // 注册成功，将用户信息存储到全局
          var user = pieces_user.User(
              name: userRespVo.uname ?? userCredential.user!.email!,
              pegg: 1000,
              gender: 0,
              vType: 0,
              userId: userRespVo.accessToken.hashCode,
              uuid: userRespVo.uuid,
              vipEnd: userRespVo.vipInfo?.endtimeAt != null ? 
                DateTime.parse(userRespVo.vipInfo!.endtimeAt!).millisecondsSinceEpoch : -1,
              vipLevel: userRespVo.vipInfo != null ? 4 : 0,
              headIcon:
                  'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
              authToken: userRespVo.accessToken);

          GlobalInfo.instance.user = user;
          // 保存用户数据到本地
          await UserStorage().saveUser(user);
          logger.d('注册成功，用户数据已保存: ${user.toString()}');
          
          // 更新用户状态
          BlocProvider.of<AuthBloc>(context).add(
            UpdateAuthInfo(user: user),
          );
        }
      }
    } on AuthException catch (e) {
      // 处理认证异常
      switch (e.code) {
        default:
          logger.d('注册失败: ${e.message}');
          
          // Singular埋点 - 注册失败
          await AnalyticsHelper().trackRegister('email', success: false);
          
          return e.message;
      }
    } catch (e) {
      // 处理其他异常
      logger.d('发生未知错误: $e');
      
      // Singular埋点 - 注册失败（未知错误）
      await AnalyticsHelper().trackRegister('email', success: false);
      
      return AppLocalizations.of(context).unknownOccurred;
    }
    
    // 如果没有用户信息，返回错误
    if (userCredential.user == null) {
      logger.d('注册异常：用户对象为空');
      
      // Singular埋点 - 注册失败（用户创建失败）
      await AnalyticsHelper().trackRegister('email', success: false);
      
      return 'User not exists';
    }
    
    return null;
  }

  Future<String> _recoverPassword(String name) {
    debugPrint('Name: $name');
    return Future.delayed(loginTime).then((_) {
      return 'User not exists';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        // appBar: AppBar(
        //   //增加退出按钮
        //   leading: IconButton(
        //     icon: const Icon(Icons.arrow_back),
        //     onPressed: () {
        //       Navigator.pop(context);
        //     },
        //   ),
        // ),
        body: FlutterLogin(
      loginProviders: <LoginProvider>[
        LoginProvider(
          icon: FontAwesomeIcons.google,
          label: 'Google',
          callback: () async {
            _trackButtonClick('google_login_button');
            logger.d('Starting Google sign in');
            try {
              // Initialize GoogleSignIn
              final GoogleSignIn googleSignIn = GoogleSignIn(
                scopes: [
                  'email',
                  // 'https://www.googleapis.com/auth/contacts.readonly',
                  "https://www.googleapis.com/auth/userinfo.profile"
                ],
              );

              // Trigger the authentication flow
              GoogleSignInAccount? googleUser =
                  await googleSignIn.signIn();
              if (googleUser == null) {
                logger.d('Google sign in aborted');
                
                // Singular埋点 - Google登录取消
                await AnalyticsHelper().trackLogin('google', success: false);
                
                return 'Sign in aborted';
              }

              // Obtain auth details
              final GoogleSignInAuthentication googleAuth =
                  await googleUser.authentication;

              final firebase_auth.AuthCredential credential = firebase_auth.GoogleAuthProvider.credential(
                accessToken: googleAuth.accessToken,
                idToken: googleAuth.idToken,
              );
              final firebase_auth.User? user = (await firebase_auth.FirebaseAuth.instance.signInWithCredential(credential)).user;

              //
              // IdTokenResult? idTokenResult = await user?.getIdTokenResult(true);

              // Create Supabase OAuth credential
              // final AuthResponse res =
              //     await Supabase.instance.client.auth.signInWithIdToken(
              //   provider: OAuthProvider.google,
              //   idToken: googleAuth.idToken,
              //   accessToken: googleAuth.accessToken,
              // );
              // final User? user = res.user;

              if (user != null) {
                // Get user info from your server
                final HttpsUserEncryption _httpsUser = HttpsUserEncryption();
                UserRespVo? userRespVo =
                    await _httpsUser.login(user.email!, user.uid);

                if (userRespVo != null) {
                  // Store user info globally
                  var user = pieces_user.User(
                      name: userRespVo.uname ?? "",
                      pegg: 1000,
                      gender: 0,
                      vType: 0,
                      userId: userRespVo.accessToken.hashCode,
                      uuid: userRespVo.uuid,
                      vipEnd: userRespVo.vipInfo?.endtimeAt != null ? 
                        DateTime.parse(userRespVo.vipInfo!.endtimeAt!).millisecondsSinceEpoch : -1,
                      vipLevel: userRespVo.vipInfo != null ? 4 : 0,
                      headIcon:
                          'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
                      authToken: userRespVo.accessToken);

                  GlobalInfo.instance.user = user;
                  // 保存用户数据到本地
                  await UserStorage().saveUser(user);
                  logger.d('Google登录成功，用户数据已保存: ${user.toString()}');
                  
                  // Singular埋点 - Google登录成功
                  await AnalyticsHelper().trackLogin('google', success: true, userId: user.uuid);
                  
                  // 更新用户状态
                  BlocProvider.of<AuthBloc>(context).add(
                    UpdateAuthInfo(user: user),
                  );
                  
                  return null;
                }
              }

              // Singular埋点 - Google登录失败
              await AnalyticsHelper().trackLogin('google', success: false);
              
              return 'Failed to authenticate with server';
            } catch (e) {
              logger.d('Google sign in error: $e');
              
              // Singular埋点 - Google登录异常
              await AnalyticsHelper().trackLogin('google', success: false);
              
              return 'An error occurred during sign in: $e';
            }
          },
        ),
        // LoginProvider(
        //   icon: FontAwesomeIcons.facebookF,
        //   label: 'Facebook',
        //   callback: () async {
        //     debugPrint('start facebook sign in');
        //     // final LoginResult result = await FacebookAuth.instance.login(); // by default we request the email and the public profile
        //     final LoginResult result = await FacebookAuth.instance.login(
        //       // permissions: ['public_profile', 'email'],
        //     );
        //     // or FacebookAuth.i.login()
        //     if (result.status == LoginStatus.success) {
        //       // you are logged
        //       final AccessToken accessToken = result.accessToken!;
        //       final userData = await FacebookAuth.instance.getUserData();
        //       logger.d('Facebook sign in successful');
        //       //获取userdata的用户名和id
        //       logger.d('Facebook user name: ${userData['name']}');
        //
        //       // Get user info from your server
        //       final HttpsUserEncryption _httpsUser = HttpsUserEncryption();
        //       UserRespVo? userRespVo =
        //       await _httpsUser.login(userData['email'], userData['id']);
        //
        //       if (userRespVo != null) {
        //       // Store user info globally
        //       var user = pieces_user.User(
        //       name: userRespVo.uname ?? "",
        //       pegg: 1000,
        //       gender: 0,
        //       vType: 0,
        //       userId: userRespVo.accessToken.hashCode,
        //       uuid: userRespVo.uuid,
        //       vipLevel: 4,
        //       headIcon:
        //       'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
        //       authToken: userRespVo.accessToken);
        //
        //       GlobalInfo.instance.user = user;
        //       logger.d('Google sign in successful');
        //       return null;
        //     } else {
        //       print(result.status);
        //       print(result.message);
        //     }
        //   }},
        // ),
        // LoginProvider(
        //   icon: FontAwesomeIcons.linkedinIn,
        //   callback: () async {
        //     debugPrint('start linkdin sign in');
        //     await Future.delayed(loginTime);
        //     debugPrint('stop linkdin sign in');
        //     return null;
        //   },
        // ),
        // LoginProvider(
        //   icon: FontAwesomeIcons.githubAlt,
        //   callback: () async {
        //     debugPrint('start github sign in');
        //     await Future.delayed(loginTime);
        //     debugPrint('stop github sign in');
        //     return null;
        //   },
        // ),
      ],
      theme: LoginTheme(
        primaryColor: AppColor.darkGrey,
        pageColorLight: Colors.black54,
        pageColorDark: Color(0xFF121212),
        titleStyle: const TextStyle(
          color: Colors.white,
          fontFamily: 'Quicksand',
          letterSpacing: 1,
        ),
      ),
      // logo: const AssetImage('assets/images/ic_launcher.png'),
      title: 'MovieNest',
      logoTag: 'MovieNest',
      onLogin: (LoginData data) async {
        _trackButtonClick('email_login_button');
        logger.d('onLogin: Name: ${data.name}, Password: ${data.password}');
        String? result = await _authUser(data, context);
        if (result != null) {
          // Toast.error(context, result);
          return result;
        } else
          return null;
      },
      onSignup: (SignupData data) async {
        _trackButtonClick('email_signup_button');
        String? result = await _signupUser(data, context);
        logger.d(
            'onSignup: Name: ${data.name}, Password: ${data.password}, Result: $result');
        Toast.success(context, AppLocalizations.of(context).sighInSuccessful);
        if (result != null) {
          // Toast.error(context, result);
          return result;
        } else
          return null;
      },
      onSubmitAnimationCompleted: () {
        logger.d('onSubmitAnimationCompleted');
        //发出登录成功事件
        BlocProvider.of<AuthBloc>(context).add(
          UpdateAuthInfo(user: GlobalInfo.instance.user),
        );
        //退出当前页面
        Navigator.pop(context);
      },
      // confirmSignupRequired: (LoginData data) {
      //   logger
      //       .d('confirmSignupRequired Signup Name: ${data.name}, Password: ${data.password}');
      //   return Future.value(true);
      // },
      onResendCode: (SignupData data) async {
        String? result = await _signupUser(data, context);
        logger.d(
            'Resend Code Name:: ${data.name}, Password: ${data.password}, Result: $result');
        if (result != null) {
          // Toast.error(context, result);
          return result;
        } else
          return null;
        return Future.value(null);
      },
      onConfirmSignup: (String str, LoginData data) async {
        logger.d(
            'Confirm Signup Name: ${data.name}, Password: ${data.password}, 验证码: $str');
        // 验证邮箱验证码
        String? msg = await verifyOtp(data.name, str);
        if (msg == null) {
          // 验证成功
          Navigator.pop(context);
          return null;
        } else {
          // 验证失败
          return Future.value(msg);
        }
      },
      onRecoverPassword: _recoverPassword,
    ));
  }

  //success is return null
  Future<String?> verifyOtp(String email, String otp) async {
    try {
      final response = await Supabase.instance.client.auth.verifyOTP(
        email: email,
        token: otp,
        type: OtpType.signup, // 使用 signup 类型
      );
      if (response.user != null) {
        logger.d('User verified successfully!');
        return null;
      }
    } catch (e) {
      logger.d('Error verifying OTP: $e');
      return '$e';
    }
    return null;
  }
}
