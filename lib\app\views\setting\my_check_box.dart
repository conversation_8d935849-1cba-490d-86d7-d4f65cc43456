import 'package:flutter/material.dart';

class My<PERSON>heckBox extends StatefulWidget {
  final int? width;
  final int? heigth;
  final String lableText;
  final bool current;
  final onCheckChange onChange;

  const MyCheckBox(
      {Key? key,
      this.width,
      this.heigth,
      required this.lableText,
      required this.current,
      required this.onChange})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyInputButtonState();
}

typedef onCheckChange = void Function(bool state);

class _MyInputButtonState extends State<MyCheckBox> {
  bool _selectedValue = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _selectedValue = widget.current;
  }

  Color getColor(Set<MaterialState> states) {
    const Set<MaterialState> interactiveStates = <MaterialState>{
      MaterialState.pressed,
      MaterialState.hovered,
      MaterialState.focused,
    };
    if (states.any(interactiveStates.contains) && !_selectedValue) {
      return Colors.grey;
    }
    if (_selectedValue) {
      return Color(0xFF12CDD9);
    }
    return Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 320,
        padding: EdgeInsets.only(left: 20, right: 25),
        child: Row(
          children: [
            Checkbox(
              checkColor: Color(0xFFffffff),
              fillColor: MaterialStateProperty.resolveWith(getColor),
              value: _selectedValue,
              onChanged: (bool? value) {
                setState(() {
                  _selectedValue = value!;
                  widget.onChange(value);
                });
              },
            ),
            Text(
              widget.lableText,
              style: TextStyle(color: Color(0xFFA6A6A6)),
            )
          ],
        ));
  }
}
