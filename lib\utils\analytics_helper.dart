import 'package:flutter/foundation.dart';
import 'package:singular_flutter_sdk/singular.dart';
import 'package:singular_flutter_sdk/singular_iap.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

/// Singular SDK 和 Firebase Analytics 埋点工具类 - B面应用专用
/// 所有B面埋点都带有"b_"前缀
class AnalyticsHelper {
  static final AnalyticsHelper _instance = AnalyticsHelper._internal();
  static final FirebaseAnalytics _firebaseAnalytics = FirebaseAnalytics.instance;
  
  factory AnalyticsHelper() => _instance;
  
  AnalyticsHelper._internal();

  /// 检查SDK初始化状态
  Future<void> checkInitialization() async {
    try {
      // 尝试发送测试事件来验证SDK是否正常工作
      await trackCustomEvent('b_sdk_init_check', {'timestamp': DateTime.now().millisecondsSinceEpoch.toString()});
      debugPrint('埋点状态: 正常初始化');
    } catch (e) {
      debugPrint('埋点状态检查异常: $e');
      debugPrint('埋点状态: 未初始化或异常');
    }
  }

  /// 登录事件埋点
  Future<void> trackLogin(String loginType, {bool success = false, String? userId}) async {
    try {
      // 更新用户ID
      if (success && userId != null && userId.isNotEmpty) {
        Singular.setCustomUserId(userId);
        await _firebaseAnalytics.setUserId(id: userId);
        debugPrint('设置用户ID: $userId');
      }

      // 记录登录事件
      Map<String, Object> eventArgs = {
        'login_type': loginType,
        'success': success ? 'true' : 'false',
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_login', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_login',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_login - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 注册事件埋点
  Future<void> trackRegister(String registerType, {bool success = false, String? userId}) async {
    try {
      // 更新用户ID
      if (success && userId != null && userId.isNotEmpty) {
        Singular.setCustomUserId(userId);
        await _firebaseAnalytics.setUserId(id: userId);
        debugPrint('设置用户ID: $userId');
      }

      // 记录注册事件
      Map<String, Object> eventArgs = {
        'register_type': registerType,
        'success': success ? 'true' : 'false',
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_sign_up', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_sign_up',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_sign_up - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// VIP购买事件埋点（iOS）
  Future<void> trackIosPurchase(
    String productId, 
    double price, 
    String currency, 
    String transactionId, 
    String receipt,
    {String? planType}
  ) async {
    try {
      // 创建苹果IAP对象
      SingularIOSIAP singularPurchase = SingularIOSIAP(
        price,
        currency,
        productId,
        transactionId,
        receipt
      );

      debugPrint('准备发送iOS购买埋点: 产品=$productId, 价格=$price $currency, 交易ID=$transactionId');
      
      // 记录IAP事件
      Map<String, Object> eventArgs = {
        'product_id': productId,
        'price': price,
        'currency': currency,
        'transaction_id': transactionId,
      };
      
      if (planType != null) {
        eventArgs['plan_type'] = planType;
      }
      
      // Singular 埋点
      if (eventArgs.isNotEmpty) {
        Singular.inAppPurchaseWithAttributes('b_purchase', singularPurchase, eventArgs);
      } else {
        Singular.inAppPurchase('b_purchase', singularPurchase);
      }
      
      // Firebase 埋点
      await _firebaseAnalytics.logPurchase(
        currency: currency,
        value: price,
        items: [
          AnalyticsEventItem(
            itemId: productId,
            itemName: productId,
            price: price,
          ),
        ],
        transactionId: transactionId,
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_purchase - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// VIP购买事件埋点（Android）
  Future<void> trackAndroidPurchase(
    String productId, 
    double price, 
    String currency, 
    String signature, 
    String receipt,
    {String? planType}
  ) async {
    try {
      // 创建安卓IAP对象
      SingularAndroidIAP singularPurchase = SingularAndroidIAP(
        price,
        currency,
        signature,
        receipt
      );

      debugPrint('准备发送Android购买埋点: 产品=$productId, 价格=$price $currency, 签名=${signature.substring(0, 10)}...');
      
      // 记录IAP事件
      Map<String, Object> eventArgs = {
        'product_id': productId,
        'price': price,
        'currency': currency,
        'signature': signature,
      };
      
      if (planType != null) {
        eventArgs['plan_type'] = planType;
      }
      
      // Singular 埋点
      if (eventArgs.isNotEmpty) {
        Singular.inAppPurchaseWithAttributes('b_purchase', singularPurchase, eventArgs);
      } else {
        Singular.inAppPurchase('b_purchase', singularPurchase);
      }
      
      // Firebase 埋点
      await _firebaseAnalytics.logPurchase(
        currency: currency,
        value: price,
        items: [
          AnalyticsEventItem(
            itemId: productId,
            itemName: productId,
            price: price,
          ),
        ],
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_purchase - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 播放视频事件埋点
  Future<void> trackVideoPlay(int videoId, String videoName, {String? source}) async {
    try {
      Map<String, Object> eventArgs = {
        'video_id': videoId.toString(),
        'video_name': videoName,
      };
      
      if (source != null) {
        eventArgs['source'] = source;
      }
      
      // Singular 埋点
      Singular.eventWithArgs('b_play_video', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_play_video',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_play_video - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 搜索事件埋点
  Future<void> trackSearch(String keyword, String searchType) async {
    try {
      Map<String, Object> eventArgs = {
        'keyword': keyword,
        'search_type': searchType,
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_search', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_search',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_search - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 自定义事件埋点
  Future<void> trackCustomEvent(String eventName, Map<String, dynamic> eventArgs) async {
    try {
      // 确保事件名称有B面前缀
      String finalEventName = eventName.startsWith('b_') ? eventName : 'b_$eventName';
      
      // 转换参数类型
      Map<String, Object> convertedArgs = Map<String, Object>.from(eventArgs);
      
      // Singular 埋点
      Singular.eventWithArgs(finalEventName, convertedArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: finalEventName,
        parameters: convertedArgs,
      );
      
      debugPrint('埋点成功: $finalEventName - $convertedArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }
  
  /// 检查全部配置
  Future<void> debugPrintAllConfig() async {
    try {
      debugPrint('====== SDK 调试信息 ======');
      // Singular 配置
      debugPrint('Singular API Key: xiway_dae2e281');
      debugPrint('Singular Secret: 724b2d633addd42da4684ff782567844');
      debugPrint('B面标识: app_side=b_side');
      debugPrint('==============================');
    } catch (e) {
      debugPrint('获取配置失败: $e');
    }
  }

  /// APP启动事件埋点（冷启动/热启动）
  Future<void> trackAppOpen({bool isColdStart = true}) async {
    try {
      Map<String, Object> eventArgs = {
        'start_type': isColdStart ? 'cold' : 'warm',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_app_open', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_app_open',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_app_open - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 首次安装打开事件埋点
  Future<void> trackFirstOpen() async {
    try {
      Map<String, Object> eventArgs = {
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_first_open', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_first_open',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_first_open - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 登出事件埋点
  Future<void> trackLogout(String userId) async {
    try {
      Map<String, Object> eventArgs = {
        'user_id': userId,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_logout', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_logout',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_logout - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 页面浏览事件埋点
  Future<void> trackPageView(String pageName) async {
    try {
      Map<String, Object> eventArgs = {
        'page_name': pageName,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_view_page', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_view_page',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_view_page - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 按钮点击事件埋点
  Future<void> trackButtonClick(String buttonName, {String? position}) async {
    try {
      Map<String, Object> eventArgs = {
        'button_name': buttonName,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      if (position != null) {
        eventArgs['position'] = position;
      }
      
      // Singular 埋点
      Singular.eventWithArgs('b_click_button', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_click_button',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_click_button - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 分享事件埋点
  Future<void> trackShare(String contentType, {String? contentId}) async {
    try {
      Map<String, Object> eventArgs = {
        'content_type': contentType,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      if (contentId != null) {
        eventArgs['content_id'] = contentId;
      }
      
      // Singular 埋点
      Singular.eventWithArgs('b_share', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_share',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_share - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 收藏事件埋点
  Future<void> trackAddToFavorites(String itemType, String itemId) async {
    try {
      Map<String, Object> eventArgs = {
        'item_type': itemType,
        'item_id': itemId,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_add_to_favorites', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_add_to_favorites',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_add_to_favorites - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 查看详情事件埋点
  Future<void> trackViewItem(String itemType, String itemId) async {
    try {
      Map<String, Object> eventArgs = {
        'item_type': itemType,
        'item_id': itemId,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_view_item', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_view_item',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_view_item - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 开始结算事件埋点
  Future<void> trackStartCheckout(String orderId, double amount) async {
    try {
      Map<String, Object> eventArgs = {
        'order_id': orderId,
        'amount': amount.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_start_checkout', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_start_checkout',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_start_checkout - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 开始免费试用事件埋点
  Future<void> trackIapTrialStart(String productId) async {
    try {
      Map<String, Object> eventArgs = {
        'product_id': productId,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_iap_trial_start', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_iap_trial_start',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_iap_trial_start - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }

  /// 开始订阅事件埋点
  Future<void> trackIapSubscriptionStart(String productId, String planType) async {
    try {
      Map<String, Object> eventArgs = {
        'product_id': productId,
        'plan_type': planType,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString()
      };
      
      // Singular 埋点
      Singular.eventWithArgs('b_iap_subscription_start', eventArgs);
      
      // Firebase 埋点
      await _firebaseAnalytics.logEvent(
        name: 'b_iap_subscription_start',
        parameters: eventArgs,
      );
      
      debugPrint('埋点成功: b_iap_subscription_start - $eventArgs');
    } catch (e) {
      debugPrint('埋点异常: $e');
    }
  }
} 