allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter()
        maven {
            url "https://maven.aliyun.com/repository/google"
        }
        maven {
            url "https://maven.aliyun.com/repository/jcenter"
        }
        maven {
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}