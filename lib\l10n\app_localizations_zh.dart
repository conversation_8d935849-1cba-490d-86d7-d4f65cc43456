// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get create => 'Home';

  @override
  String get goods => 'Categories';

  @override
  String get me => 'Me';

  @override
  String get agree => 'agree';

  @override
  String get disagree => 'disagree';

  @override
  String get mainOneClick => 'One-click to create';

  @override
  String get drafts => 'Drafts';

  @override
  String get noDrafts => 'No drafts, click to create';

  @override
  String get oneClickMode => 'One-click mode';

  @override
  String get title => 'Title';

  @override
  String get submit => 'Submit';

  @override
  String get haveDone => 'Done\nClick to view';

  @override
  String get original => 'Original';

  @override
  String get unDone => 'Not done';

  @override
  String get generating => 'Generating...';

  @override
  String get failed => 'Failed';

  @override
  String get rename => 'Rename';

  @override
  String get delete => 'Delete';

  @override
  String get export => 'Export';

  @override
  String get reMake => 'Re-make';

  @override
  String get earnPoints => 'Earn';

  @override
  String get reDownload => 'Re-download';

  @override
  String get taskClaim => 'Task claim';

  @override
  String get resDownload => 'Res download';

  @override
  String get notLogged => 'Not logged in';

  @override
  String get vipExpired => 'Membership expired';

  @override
  String get myRole => 'My role';

  @override
  String get allCategory => 'All categories';

  @override
  String get videoExport => 'Video export';

  @override
  String get syntheticSubtitles => 'Synthetic subtitles';

  @override
  String get noSubtitles => 'The video synthesized after cancellation has no subtitles';

  @override
  String get exPortVideo => 'Export video';

  @override
  String get exportingInfo => 'Exporting... Please wait';

  @override
  String get synthesisProgress => 'Video synthesis in progress...';

  @override
  String get storyboard => 'Storyboard';

  @override
  String get exportSuccessfulAndSave => 'Export successful, saved to album';

  @override
  String get basicSettings => 'Basic settings';

  @override
  String get next => 'Next';

  @override
  String get aiWrite => 'AI write';

  @override
  String get uploadAudio => 'Upload Audio';

  @override
  String get enterText => 'Enter Text';

  @override
  String get aiEnterExample => 'Enter example: Children\'s Day';

  @override
  String get selectStyle => 'Select style';

  @override
  String get numStoryboards => 'Num storyboards';

  @override
  String get aiWriteTips => 'Note: Ai automatically generates copy and prompts, deducting 2 points for each storyboard';

  @override
  String get nowDeductionPoints => 'Current deduction points:';

  @override
  String get ttsRoles => 'TTS roles';

  @override
  String get moreSettings => 'More settings';

  @override
  String get screenRatio => 'Screen ratio';

  @override
  String get pleaseEnterText => 'Please enter a article';

  @override
  String get nOperationIsRequired => 'No operation is required for re-production';

  @override
  String get speed => 'Speed';

  @override
  String get fixedCharacter => 'Fixed character';

  @override
  String get globalSettings => 'Global settings';

  @override
  String get repOriText => 'Replace text';

  @override
  String get subImgTask => 'Submit image task';

  @override
  String get subVideoTask => 'Submit video task';

  @override
  String get someUnSaved => 'Some changes un saved';

  @override
  String get clickToSaveAndExit => 'Click OK to save the draft and exit';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Spanish';

  @override
  String get japanese => 'Japanese';

  @override
  String get appSettings => 'App settings';

  @override
  String get settings => 'Settings';

  @override
  String get tutorial => 'Tutorial';

  @override
  String get about => 'About';

  @override
  String get privacyPolicy => 'Privacy policy';

  @override
  String get userAgreement => 'User agreement';

  @override
  String get pleaseRead => 'Please read carefully before using and confirm that you agree to the above terms.';

  @override
  String get welcomeToMovieNest => 'Welcome to MovieNest, in order to better protect your privacy and personal information security, we have formulated';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get checkForUpdates => 'Check for updates';

  @override
  String get clickToDownloadNewVersion => 'Click to download the new version';

  @override
  String get updateContent => 'Update content';

  @override
  String get newVersionDownloading => 'New version downloading';

  @override
  String get logOut => 'Log out';

  @override
  String get logOutInfo => 'Are you sure you want to log out?';

  @override
  String get exit => 'Exit';

  @override
  String get logoutSuccessful => 'Logout successful';

  @override
  String get logoutFailed => 'Logout failed';

  @override
  String get logoutFailedInfo => 'Logout failed, please try again later';

  @override
  String get sighInSuccessful => 'Sign in successful';

  @override
  String get cacheClean => 'Cache cleaning';

  @override
  String get clickClean => 'Click';

  @override
  String get thirdDataList => 'Third-party data list';

  @override
  String get personalInfoList => 'Personal information list';

  @override
  String get accountCancellation => 'Account cancellation';

  @override
  String get accountCancellationInfo => 'Are you sure you want to cancel your account?';

  @override
  String get accountCancellationInfo1 => 'your account will be permanently deleted';

  @override
  String get aiGenerating => 'AI generating...';

  @override
  String get setToStoryBoard => 'Set to storyboard';

  @override
  String get imitation => 'Imitation';

  @override
  String get light => 'Light';

  @override
  String get pov => 'POV';

  @override
  String get promptDic => 'Prompt dict';

  @override
  String get num => 'Num';

  @override
  String get style => 'Style';

  @override
  String get advanceSetting => 'Advanced Setting';

  @override
  String get image => 'Image';

  @override
  String get video => 'Video';

  @override
  String get tts => 'Dubbing';

  @override
  String get motion => 'Motion';

  @override
  String get preview => 'Preview';

  @override
  String get webDetails => 'Web details';

  @override
  String get invalidEmail => 'Invalid email address';

  @override
  String get userHasDisabled => 'User has been disabled';

  @override
  String get userDoesNotExit => 'User does not exist';

  @override
  String get passError => 'Password error';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get emailAlreadyInUse => 'Email already in use';

  @override
  String get unknownOccurred => 'An unknown error occurred';

  @override
  String get operationNotAllowed => 'Operation not allowed';

  @override
  String get weakPassword => 'Password is too weak';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get submitSuccess => 'Submit successfully';

  @override
  String get generated => '(Generated)';

  @override
  String get notGenerated => '(Not generated)';

  @override
  String get lockSeed => 'Lock seed';

  @override
  String get randomSeed => 'Random seed';

  @override
  String get setSeed => 'Set seed';

  @override
  String get deleteDraft => 'Delete draft';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get generateImage => 'Generate image';

  @override
  String get unrecognizedRole => 'Unrecognized role';

  @override
  String get unrecognizedScene => 'Unrecognized scene';

  @override
  String get selectStartImageToVideo => 'Select the first image';

  @override
  String get selectEndImageToVideo => 'Select the last image';

  @override
  String get pleaseEnterTheMotionGuidePrompt => 'Please enter the motion guide prompt words for generating the video, if not filled in, it will be automatically generated according to the picture';

  @override
  String get otherSetting => 'Other settings';

  @override
  String get highQualityModeTakesTwiceTime => '(High-quality mode takes twice time, please be patient)';

  @override
  String get mode => 'Mode';

  @override
  String get fastMode => 'Fast mode';

  @override
  String get highQualityMode => 'High-quality mode';

  @override
  String get resolution => 'Resolution';

  @override
  String get sd => 'SD';

  @override
  String get hd => 'HD';

  @override
  String get play => 'Play';

  @override
  String get startDubbing => 'Start dubbing';

  @override
  String get save => 'Save';

  @override
  String get dubbingFailed => 'Dubbing failed';

  @override
  String get noDubbingYet => 'No dubbing yet';

  @override
  String get textCannotBeEmpty => 'Text cannot be empty';

  @override
  String get generalWaitInfo => 'During peak hours, it takes about 1 minute to generate a picture quickly, and about 5~10 minutes to generate a picture normally. The speed is slightly faster during off-peak hours. Please be patient.\\n\\nDo not exit during the generation of the picture. The recent average waiting time is 3 minutes and 16 seconds.';

  @override
  String get downloadSuccessAndSave => 'Download successful, saved to album';

  @override
  String get submitTask => 'Submit task';

  @override
  String get imageQuality => 'Image quality';

  @override
  String get excellentCase => 'Excellent case';

  @override
  String get allMaterialsGeneratedByMovieNest => 'All materials are generated by MovieNest';

  @override
  String get theDefaultIsHDInfo => 'The default is HD ordinary high-definition. FHD deducts 4 times the points, and QHD deducts 6 times the points. The higher the quality, the richer the picture details';

  @override
  String get aiPrompts => 'Ai prompts';

  @override
  String get aiPromptsInfos => 'After checking, Ai reasoning prompts will be added on the basis of the original prompts';

  @override
  String get reDubbing => 'Re-dubbing';

  @override
  String get reDubbingInfos => 'After checking, TTS dubbing will be re-generated';

  @override
  String get effective => 'Effective range';

  @override
  String get allStoryboards => 'All';

  @override
  String get videoStoryboard => 'Video storyboard';

  @override
  String get dynamicVideo => 'Dynamic video';

  @override
  String get dynamicVideoInfo => 'After checking, each storyboard will generate a dynamic video';

  @override
  String get taskLimit => 'The concurrent task limit has been reached. Maximum 3';

  @override
  String get membershipRenewal => 'Membership renewal';

  @override
  String get iHaveReadAndAgree => 'I have read and agree';

  @override
  String get piecesAnimaMembershipAgreement => '《Membership Purchase Agreement》';

  @override
  String get payNow => 'Pay now';

  @override
  String get pleaseReadAndAgreeMembership => 'Please read and agree to the membership purchase agreement first';

  @override
  String get pleaseSelectTypeOfMembership => 'Please select the type of membership you want to purchase.';

  @override
  String get gettingPaymentInfo => 'Getting payment information...';

  @override
  String get paySuccess => 'Payment successful';

  @override
  String get payFailed => 'Payment failed';

  @override
  String get membershipExpired => 'Membership expired';

  @override
  String get pleaseEnterTheme => 'Please enter a theme';

  @override
  String get pleaseWaitForStyleLoaded => 'Please wait for the style to load';

  @override
  String get myPoints => 'My points';

  @override
  String get claim => 'Claim';

  @override
  String get claimed => 'Claimed';

  @override
  String get checkToReceiveVip => 'Check in To Receive Membership';

  @override
  String get progress => 'Progress';

  @override
  String get playRecord => 'Play Record';

  @override
  String get updateReminders => 'Movie/TV Update Reminders';

  @override
  String get myDownload => 'My Download';

  @override
  String get done => 'Done';

  @override
  String get edit => 'Edit';

  @override
  String get collection => 'Collection';

  @override
  String get share => 'Share';

  @override
  String get vipCardTitle => 'Limited Time Discount';

  @override
  String get vipCardDescription => 'Get a member and enjoy exclusive privileges';

  @override
  String get vipCardFunction1 => 'Remove all  Ads';

  @override
  String get vipCardFunction2 => 'GUnlock Movies';

  @override
  String get vipCardFunction3 => 'Unlimited Downloads';

  @override
  String get vipCardFunction4 => 'HD Resources';

  @override
  String get vipCardFunction5 => 'Unlock Cast';

  @override
  String get vipCardButton => 'Get';

  @override
  String get categoriesTitle => 'CATEGORIES';

  @override
  String get categoriesSearchDesc => 'Searcch Movies, Genres etc';

  @override
  String get deleteSuccess => 'Delete Success';

  @override
  String get deleteFailed => 'Days Failed';

  @override
  String get deleting => 'Deleting';

  @override
  String get deleteSelected => 'DeleteSelected';
}
