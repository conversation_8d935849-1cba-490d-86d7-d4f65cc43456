import 'package:json_annotation/json_annotation.dart';

part 'ai_tts_style.g.dart';

@JsonSerializable()
class AiTtsStyle {
  ///适合年龄段 1老年2青年3少年4孩童
  @<PERSON><PERSON><PERSON><PERSON>(name: "age")
  int age;

  ///收费标准：xx字/皮蛋
  @Json<PERSON><PERSON>(name: "charge")
  int charge;

  ///图标url
  @Json<PERSON><PERSON>(name: "icon")
  String icon;
  @JsonKey(name: "id")
  int id;

  ///语种，1: 中文 2: 英文 3: 西班牙语
  @<PERSON>son<PERSON>ey(name: "language")
  int language;

  ///1 阿里TTS 2 思必驰3微软4魔音工坊
  @<PERSON>son<PERSON><PERSON>(name: "mode")
  int mode;

  ///音色名
  @Json<PERSON><PERSON>(name: "name")
  String name;

  ///排序层级
  @JsonKey(name: "order")
  int order;

  ///适合性别1男性2女性
  @JsonKey(name: "sex")
  int sex;

  ///音色的情绪语气，
  @Json<PERSON><PERSON>(name: "style")
  String? style;

  ///标签，多个标签以英文逗号隔开
  @J<PERSON><PERSON><PERSON>(name: "tag")
  String tag;

  ///合成tts所需音色type（由voice_type和mode组合而来）
  @JsonKey(name: "type")
  String type;

  ///试听声音链接，原速
  @JsonKey(name: "url")
  String url;

  ///0 免费；1会员免费；2会员收费
  @JsonKey(name: "vip")
  int vip;

  ///音色类型
  @JsonKey(name: "voice_type")
  String voiceType;

  AiTtsStyle({
    required this.age,
    required this.charge,
    required this.icon,
    required this.id,
    required this.language,
    required this.mode,
    required this.name,
    required this.order,
    required this.sex,
    this.style,
    required this.tag,
    required this.type,
    required this.url,
    required this.vip,
    required this.voiceType,
  });

  AiTtsStyle copyWith({
    int? age,
    int? charge,
    String? icon,
    int? id,
    int? language,
    int? mode,
    String? name,
    int? order,
    int? sex,
    String? style,
    String? tag,
    String? type,
    String? url,
    int? vip,
    String? voiceType,
  }) =>
      AiTtsStyle(
        age: age ?? this.age,
        charge: charge ?? this.charge,
        icon: icon ?? this.icon,
        id: id ?? this.id,
        language: language ?? this.language,
        mode: mode ?? this.mode,
        name: name ?? this.name,
        order: order ?? this.order,
        sex: sex ?? this.sex,
        style: style ?? this.style,
        tag: tag ?? this.tag,
        type: type ?? this.type,
        url: url ?? this.url,
        vip: vip ?? this.vip,
        voiceType: voiceType ?? this.voiceType,
      );

  factory AiTtsStyle.fromJson(Map<String, dynamic> json) =>
      _$AiTtsStyleFromJson(json);

  Map<String, dynamic> toJson() => _$AiTtsStyleToJson(this);
}
