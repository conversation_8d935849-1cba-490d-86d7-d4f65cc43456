import 'package:cached_network_image/cached_network_image.dart';
import 'package:components/toly_ui/ti/circle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:just_audio/just_audio.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/config/ai_tts_style.dart';

import '../../../../components/custom_widget/hotel_booking/hotel_app_theme.dart';

/// create by blueming.wu
/// Ai推文音色选择专用widget
GlobalKey<_StyleGridViewState> ttsStyleKey = GlobalKey();

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class TtsStylesGridView extends StatefulWidget {
  const TtsStylesGridView(
      {Key? key,
      required this.aiTtsStyleList,
      required this.aiTtsModelChanged,
      required this.initSpeed,
      required this.onTtsOpen,
      required this.draftType,
      required this.selectType,
      this.ttsEnable,
      this.openSwitch,
      required this.player})
      : super(key: key);

  final List<AiTtsStyle> aiTtsStyleList;
  final Function(AiTtsStyle, double) aiTtsModelChanged;
  final Function(bool) onTtsOpen;
  final AudioPlayer player;
  final int draftType;
  final double initSpeed;
  final String selectType;
  final bool? ttsEnable;
  final bool? openSwitch;

  @override
  _StyleGridViewState createState() => _StyleGridViewState();
}

class _StyleGridViewState extends State<TtsStylesGridView> {
  late final AudioPlayer player;

  //语速调节时使用的初始值
  double _speed = 1.0;
  bool ttsEnable = true;

  //试听时是否开启语速调节
  bool openSwitch = true;
  late AiTtsStyle aiTtsStyle;

  @override
  void initState() {
    super.initState();
    ttsEnable = widget.ttsEnable ?? true;
    openSwitch = widget.openSwitch ?? true;
    logger.d("widget.selectType:${widget.selectType}");
    for (int i = 0; i < widget.aiTtsStyleList.length; i++) {
      if (widget.aiTtsStyleList[i].type == widget.selectType) {
        aiTtsStyle = widget.aiTtsStyleList[i];
        logger.d("选中的音色是:${aiTtsStyle.type}");
        break;
      }
    }
    _speed = widget.initSpeed;
    // widget.aiTtsModelChanged(widget.aiTtsStyleList[selectedIndex], _speed);
    player = widget.player;
    player.setSpeed(_speed);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // List<Widget> widgets = [];
    // widgets.add();
    logger.d("重新构建了");
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        _buildTitle(widget.aiTtsStyleList),
        const SizedBox(height: 16),
        if (ttsEnable)
          Expanded(
            child: _buildTtsTabByLanguage(widget.aiTtsStyleList),
          ),
        // Spacer(),
        Row(
          children: [
            Circle(
              color: Color(0xFF12CDD9),
              radius: 5,
            ),
            Padding(
              padding: EdgeInsets.only(left: 12),
              child: Text(
                AppLocalizations.of(context).speed,
                style: TextStyle(fontSize: 12),
              ),
            )
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
                child: Slider(
              value: _speed,
              min: 0.8,
              max: 2.0,
              // divisions: 20,
              label: _speed.toStringAsFixed(2),
              activeColor: Color(0xFF12CDD9),
              inactiveColor: Colors.green.withAlpha(99),
              onChanged: (value) {
                setState(() {
                  _speed = value;
                });
              },
              onChangeEnd: (value) {
                print("滑动结束:" + _speed.toStringAsFixed(1));
                widget.aiTtsModelChanged(aiTtsStyle, _speed);
                player.setSpeed(_speed);
                player.play();
              },
            )),
            Text(
              "x ${_speed.toStringAsFixed(2)}",
              style: TextStyle(fontSize: 18),
            )
          ],
        ),
        if (widget.draftType == 6)
          SizedBox(
            height: 50,
          )
      ],
    );
  }

  void setInputType(int type) {
    setState(() {
      ttsEnable = (type == 0 || type == 2);
    });
  }

  void setTtsEnable(bool enable) {
    setState(() {
      ttsEnable = enable;
    });
  }

  ///设置语速的值
  void setSpeed(double speed) {
    setState(() {
      _speed = speed;
    });
  }

  Widget _buildTitle(List<AiTtsStyle> aiTtsStyleList) {
    return Row(
      children: [
        Circle(
          color: Color(0xFF12CDD9),
          radius: 5,
        ),
        Padding(
          padding: EdgeInsets.only(left: 12),
          child: Text(
            AppLocalizations.of(context).ttsRoles,
            style: TextStyle(fontSize: 12),
          ),
        ),
        if (openSwitch)
          CupertinoSwitch(
            activeColor: ttsEnable
                ? HotelAppTheme.buildDarkTheme().primaryColor
                : Colors.grey.withOpacity(0.6),
            onChanged: (bool select) {
              setState(() {
                ttsEnable = select;
              });
              widget.onTtsOpen.call(ttsEnable);
            },
            value: ttsEnable,
          )
        // if (openSwitch)
        //   Text(
        //     widget.draftType == 3 ? "(关闭后该作品使用原视频音频)" : "(关闭后该作品将使用上传音频配音)",
        //     style: TextStyle(fontSize: 10),
        //   )
      ],
    );
  }

  ///使用Tab+TabBarView根据AiTtsStyle中的language字段构建对应的tab
  _buildTtsTabByLanguage(List<AiTtsStyle> aiTtsStyleList) {
    List<Tab> tabs = [];
    List<Widget> tabViews = [];
    // List<AiTtsStyle> chineseList = [];
    List<AiTtsStyle> englishList = [];
    List<AiTtsStyle> spanishList = [];
    //日语集合language=4
    List<AiTtsStyle> japaneseList = [];
    for (AiTtsStyle aiTtsStyle in aiTtsStyleList) {
      if (aiTtsStyle.language == 1) {
        // chineseList.add(aiTtsStyle);
      } else if (aiTtsStyle.language == 2) {
        englishList.add(aiTtsStyle);
      } else if (aiTtsStyle.language == 3) {
        spanishList.add(aiTtsStyle);
      } else if (aiTtsStyle.language == 4) {
        japaneseList.add(aiTtsStyle);
      }
    }
    // if (chineseList.isNotEmpty) {
    //   tabs.add(Tab(text: "中文"));
    //   tabViews.add(_buildGridView(chineseList));
    // }
    if (englishList.isNotEmpty) {
      tabs.add(Tab(text: AppLocalizations.of(context).english));
      tabViews.add(_buildGridView(englishList));
    }
    if (spanishList.isNotEmpty) {
      tabs.add(Tab(text: AppLocalizations.of(context).spanish));
      tabViews.add(_buildGridView(spanishList));
    }
    if (japaneseList.isNotEmpty) {
      tabs.add(Tab(text: AppLocalizations.of(context).japanese));
      tabViews.add(_buildGridView(japaneseList));
    }
    return DefaultTabController(
      length: tabs.length,
      child: Column(
        children: [
          TabBar(
            tabs: tabs,
            isScrollable: true,
            labelColor: Color(0xFF12CDD9),
            unselectedLabelColor: Color(0xFFA6A6A6),
            indicatorColor: Color(0xFF12CDD9),
          ),
          Expanded(
            child: TabBarView(
              children: tabViews,
            ),
          )
        ],
      ),
    );
  }

  ///构建音色选择的网格
  Widget _buildGridView(List<AiTtsStyle> aiTtsStyleList) => GridView.count(
        crossAxisCount: 5,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        childAspectRatio: 3 / 4,
        children: List.generate(
          aiTtsStyleList.length,
          (index) => _buildItem(aiTtsStyleList[index].name,
              aiTtsStyleList[index].icon, index, aiTtsStyleList[index]),
        ),
      );

  ///音色单项
  Container _buildItem(
          String title, String imageUrl, int index, AiTtsStyle aiTtsStyle) =>
      Container(
        alignment: Alignment.center,
        child: Padding(
          child: GestureDetector(
            onTap: () async {
              widget.aiTtsModelChanged(aiTtsStyle, _speed);
              logger.d("选中音色的url地址:" + aiTtsStyle.toJson().toString());
              setState(() {
                this.aiTtsStyle = aiTtsStyle;
              });
              await player.setUrl(aiTtsStyle.url);
              await player.play();
            },
            child: Column(
              children: [
                Flexible(
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: this.aiTtsStyle.type == aiTtsStyle.type
                            ? Color(0xFF12CDD9)
                            : Colors.transparent,
                        width: 2, // 设置边框宽度
                      ),
                      image: DecorationImage(
                        image: CachedNetworkImageProvider(imageUrl),
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                  ),
                  flex: 4,
                ),
                const SizedBox(height: 5),
                Text(
                  title,
                  maxLines: 1,
                  style:
                      const TextStyle(color: Color(0xFFA6A6A6), fontSize: 12),
                ),
              ],
            ),
          ),
          padding: const EdgeInsets.only(left: 2, top: 2, right: 2),
        ),
      );
}
