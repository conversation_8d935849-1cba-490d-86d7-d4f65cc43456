import 'dart:async';

import 'package:app/app/router/unit_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';
import 'package:pieces_ai/components/custom_widget/hotel_booking/hotel_app_theme.dart';
import 'package:pieces_ai/utils/analytics_helper.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:castscreen/castscreen.dart';

import '../../../authentication/blocs/authentic/bloc.dart';
import '../../../authentication/blocs/authentic/state.dart';
import '../../../authentication/models/user.dart';
import '../../api_https/impl/https_recom_videos.dart';
import '../../data/vip_features.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class VideoDetail extends StatefulWidget {
  final VideoRespVo recomVideo;

  const VideoDetail({Key? key, required this.recomVideo}) : super(key: key);

  @override
  State<VideoDetail> createState() => _VideoDetailState();
}

class _VideoDetailState extends State<VideoDetail>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 添加一个标志跟踪页面是否已销毁
  bool _disposed = false;
  final double infoHeight = 364.0;
  double opacity1 = 0.0;
  double opacity2 = 0.0;
  double opacity3 = 0.0;

  bool _isDescriptionExpanded = false;
  bool _isWebView = false;
  WebViewController? _webViewController;

  late Future<List<VideoRespVo>> _initialLoadFuture;
  final HttpsRecomVideos _httpsRecomVideos = HttpsRecomVideos();

  late final VideoPlayerController videoPlayerController;
  ChewieController? chewieController;

  // 添加字幕相关变量
  List<Subtitle> _subtitles = [];
  bool _subtitlesLoaded = false;

  // 修改用户
  User user = GlobalInfo.instance.user;
  late StreamSubscription<AuthState> _authSubscription;

  // Add these variables for casting
  List<Device> _castDevices = [];
  Device? _selectedCastDevice;
  bool _isSearchingDevices = false;
  bool _isCasting = false;

  @override
  void initState() {
    // Register observer to detect app lifecycle changes
    WidgetsBinding.instance.addObserver(this);
    logger.d("播放地址是${widget.recomVideo.playLink}");
    _initPlayer();
    
    // 添加播放视频埋点
    _trackVideoPlay();

    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('video_detail');

    Future.delayed(const Duration(seconds: 5), () {
      _checkVipStatus();
    });

    //监听用户登录成功的回调
    final authBloc = BlocProvider.of<AuthBloc>(context, listen: false);
    _authSubscription = authBloc.stream.listen((state) {
      if (state is AuthSuccess) {
        logger.d("視頻詳情頁接收到登錄成功消息");
        setState(() {
          user = GlobalInfo.instance.user;
          // 刷新用戶的收藏
          if (user.userId != -1) {
            _httpsRecomVideos
                .getCollectionStatus(
                    widget.recomVideo.id, user.authToken.toString())
                .then((value) {
              setState(() {
                _isCollected = value > 0;
              });
            });
          }
        });
      }
    });

    if (user.userId != -1) {
      _httpsRecomVideos
          .getCollectionStatus(widget.recomVideo.id, user.authToken.toString())
          .then((value) {
        setState(() {
          _isCollected = value > 0;
        });
      });
    }

    _initialLoadFuture = _httpsRecomVideos.loadSearchVideoList(0, 15, '');
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_disposed) return;

    if (state == AppLifecycleState.paused) {
      logger.d("应用进入后台，暂停播放");
      if (!_isCasting) {
        // Only pause local player if not casting
        chewieController?.pause();
      }
    } else if (state == AppLifecycleState.resumed) {
      logger.d("应用恢复前台，继续播放");
      if (!_isCasting) {
        // Only resume local player if not casting
        chewieController?.play();
      }
      user = GlobalInfo.instance.user;
    }
  }

  // 添加SRT解析方法
  List<Subtitle> _parseSrt(String srtContent) {
    final subtitles = <Subtitle>[];
    final sections = srtContent.split('\n\n');

    for (var section in sections) {
      final lines = section.split('\n');
      if (lines.length >= 3) {
        final timeLine = lines[1];
        final timeParts = timeLine.split(' --> ');
        if (timeParts.length == 2) {
          final start = _parseDuration(timeParts[0]);
          final end = _parseDuration(timeParts[1]);
          final text = lines.sublist(2).join('\n');

          subtitles.add(Subtitle(
            index: subtitles.length,
            start: start,
            end: end,
            text: text,
          ));
        }
      }
    }
    return subtitles;
  }

  Duration _parseDuration(String time) {
    try {
      final parts = time.split(',');
      final timePart = parts[0].trim();
      final milliPart = parts.length > 1 ? int.parse(parts[1]) : 0;

      final timeComponents = timePart.split(':');
      final hours = int.parse(timeComponents[0]);
      final minutes = int.parse(timeComponents[1]);
      final seconds = int.parse(timeComponents[2]);

      return Duration(
        hours: hours,
        minutes: minutes,
        seconds: seconds,
        milliseconds: milliPart,
      );
    } catch (e) {
      return Duration.zero;
    }
  }

  /// 检查VIP状态，提示用户续费VIP或登录
  Future<void> _checkVipStatus() async {
    if (_disposed) return;

    logger.d("检查VIP状态, isFree: ${widget.recomVideo.isFree}");
    // If content is premium (not free)
    if (!widget.recomVideo.isFree) {
      // Check if user has VIP and if it's expired
      logger.d("VIP到期时间: ${user.vipEnd}");

      if (user.userId == -1) {
        chewieController?.pause();
        if (!_disposed && mounted) {
          showLoginDialog();
        }
      } else {
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        if (user.vipEnd == null || user.vipEnd! < 0) {
          // VIP has expired
          if (chewieController != null) {
            chewieController!.pause();
          }
          // Show VIP expired dialog
          if (!_disposed && mounted) {
            showDialog(
              context: context,
              barrierDismissible: false,
              // Prevent dismissing by tapping outside
              builder: (context) {
                return AlertDialog(
                  backgroundColor: Colors.grey[900],
                  title: Text(
                    'VIP Expired',
                    style: TextStyle(color: Colors.white),
                  ),
                  content: Text(
                    'Your VIP has expired. Please renew to continue watching.',
                    style: TextStyle(color: Colors.grey[300]),
                  ),
                  actions: <Widget>[
                    TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor:
                            HotelAppTheme.buildDarkTheme().primaryColor,
                      ),
                      onPressed: () {
                        chewieController?.pause();
                        Navigator.of(context).pop();
                        Navigator.of(context).pop();
                        Navigator.of(context).pushNamed(UnitRouter.vip_center);
                      },
                      child: Text('OK'),
                    ),
                  ],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 8,
                );
              },
            );
          }
        }
      }
    }
  }

  void showLoginDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Login Required',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Please login to continue watching this video.',
            style: TextStyle(color: Colors.grey[300]),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: HotelAppTheme.buildDarkTheme().primaryColor,
              ),
              onPressed: () {
                chewieController?.pause();
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                Navigator.of(context).pushNamed(UnitRouter.login);
              },
              child: Text('OK'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 8,
        );
      },
    );
  }

  _initPlayer() async {
    if (_disposed) return;

    final playLink = widget.recomVideo.playLink;
    logger.d("初始化播放器，链接: $playLink");

    // 检查是否是vidsrc.xyz链接
    if (playLink.contains('vidsrc.xyz')) {
      setState(() {
        _isWebView = true;
      });
      
      _webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.black)
        ..setNavigationDelegate(
          NavigationDelegate(
            onNavigationRequest: (NavigationRequest request) {
              return NavigationDecision.navigate;
            },
            onPageFinished: (String url) {
              // 注入更详细的 CSS 来调整视频播放器样式
              _webViewController?.runJavaScript('''
                document.body.style.margin = '0';
                document.body.style.padding = '0';
                document.body.style.overflow = 'hidden';
                document.body.style.backgroundColor = 'black';
                
                // 调整视频播放器容器
                var videoContainer = document.querySelector('.vidsrc-player');
                if (videoContainer) {
                  videoContainer.style.width = '100%';
                  videoContainer.style.height = '100%';
                  videoContainer.style.position = 'relative';
                  videoContainer.style.paddingBottom = '56.25%';
                }
                
                // 调整视频元素
                var video = document.querySelector('video');
                if (video) {
                  video.style.width = '100%';
                  video.style.height = '100%';
                  video.style.position = 'absolute';
                  video.style.top = '0';
                  video.style.left = '0';
                  video.style.objectFit = 'contain';
                }
                
                // 调整控制栏
                var controls = document.querySelector('.vjs-control-bar');
                if (controls) {
                  controls.style.position = 'absolute';
                  controls.style.bottom = '0';
                  controls.style.width = '100%';
                  controls.style.zIndex = '9999';
                }
              ''');
            },
          ),
        )
        ..enableZoom(false)
        ..setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        ..setOnConsoleMessage((JavaScriptConsoleMessage message) {
          logger.d('WebView console: ${message.message}');
        })
        ..loadRequest(Uri.parse(playLink));

      return;
    }

    // 使用原有的视频播放器
    final savedPosition = await _httpsRecomVideos.loadPlaybackPosition(
        widget.recomVideo.id, user.authToken.toString());

    videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(playLink));
    await videoPlayerController.initialize();
    
    if (savedPosition.inSeconds > 0) {
      videoPlayerController.seekTo(savedPosition);
    }

    if (widget.recomVideo.subtitle != null &&
        widget.recomVideo.subtitle!.isNotEmpty) {
      try {
        logger.d("开始加载字幕: ${widget.recomVideo.subtitle}");
        final response = await http.get(Uri.parse(widget.recomVideo.subtitle!));
        if (response.statusCode == 200) {
          _subtitles = _parseSrt(response.body);
          logger.d("字幕加载成功，共加载${_subtitles.length}条字幕");
          setState(() => _subtitlesLoaded = true);
        }
      } catch (e) {
        logger.e("Failed to load subtitles: $e");
      }
    }

    final subtitles = Subtitles(_subtitles);

    chewieController = ChewieController(
      videoPlayerController: videoPlayerController,
      autoPlay: true,
      looping: false,
      showSubtitles: true,
      subtitle: subtitles,
      subtitleBuilder: (context, subtitle) => _buildSubtitle(subtitle),
      materialProgressColors: ChewieProgressColors(
        playedColor: Colors.red,
        handleColor: Colors.red,
        backgroundColor: Colors.grey,
        bufferedColor: Colors.white70,
      ),
      errorBuilder: (context, errorMessage) {
        return Center(
          child: Text(
            errorMessage,
            style: TextStyle(color: Colors.white),
          ),
        );
      },
    );

    if (_disposed) {
      videoPlayerController.dispose();
      return;
    }

    setState(() {});
  }

  Widget _buildSubtitle(String subtitle) {
    return Container(
      padding: EdgeInsets.all(10),
      child: HtmlWidget(
        subtitle.replaceAll('\n', '<br>'), // 将换行符替换为 <br> 标签
        textStyle: TextStyle(
          color: Colors.white,
          fontSize: 13,
          fontFamily: 'NotoSans', // 使用支持音乐符号的字体
        ),
        customStylesBuilder: (element) {
          // 自定义样式（可选）
          if (element.localName == 'i') {
            return {'font-style': 'italic'};
          }
          return null;
        },
      ),
    );
  }

  /// 解析带有简单HTML标签的字幕内容
  List<TextSpan> _parseHtmlSubtitle(String text) {
    final List<TextSpan> spans = [];
    String fontFamily = "NotoSans";
    double fontSize = 12;
    final RegExp htmlTagRegex = RegExp(r"<(/?)(i|b)>(.*?)</\2>");

    int currentIndex = 0;
    for (final match in htmlTagRegex.allMatches(text)) {
      // 添加匹配之前的普通文本
      if (match.start > currentIndex) {
        spans.add(
          TextSpan(
            text: text.substring(currentIndex, match.start),
            style: TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontFamily: fontFamily, // 使用支持音乐符号的字体
            ),
          ),
        );
      }

      // 处理匹配到的HTML标签
      final tag = match.group(2); // 标签类型（i 或 b）
      final content = match.group(3); // 标签内容
      final isClosingTag = match.group(1) == "/"; // 是否是闭合标签

      if (!isClosingTag && content != null) {
        TextStyle style;
        switch (tag) {
          case 'i':
            style = TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontStyle: FontStyle.italic,
              fontFamily: fontFamily, // 使用支持音乐符号的字体
            );
            break;
          case 'b':
            style = TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              fontFamily: fontFamily, // 使用支持音乐符号的字体
            );
            break;
          default:
            style = TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontFamily: fontFamily, // 使用支持音乐符号的字体
            );
        }

        spans.add(
          TextSpan(
            text: content,
            style: style,
          ),
        );
      }

      currentIndex = match.end;
    }

    // 添加剩余的普通文本
    if (currentIndex < text.length) {
      spans.add(
        TextSpan(
          text: text.substring(currentIndex),
          style: TextStyle(
            color: Colors.white,
            fontSize: fontSize,
            fontFamily: fontFamily, // 使用支持音乐符号的字体
          ),
        ),
      );
    }

    return spans;
  }

  // Method to search for casting devices
  Future<void> _searchCastDevices() async {
    setState(() {
      _isSearchingDevices = true;
    });

    try {
      final devices =
          await CastScreen.discoverDevice(timeout: const Duration(seconds: 5));

      setState(() {
        _castDevices = devices;
        _isSearchingDevices = false;
      });

      if (_castDevices.isNotEmpty) {
        _showCastDeviceSelector();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No casting devices found'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isSearchingDevices = false;
      });
      logger.e("Error discovering cast devices: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to discover casting devices'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // Show bottom sheet to select casting device
  void _showCastDeviceSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.cast, color: Colors.white),
                  SizedBox(width: 12),
                  Text(
                    'Cast to',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    icon: Icon(Icons.refresh, color: Colors.white),
                    onPressed: () async {
                      Navigator.pop(context);
                      await _searchCastDevices();
                    },
                  ),
                ],
              ),
              SizedBox(height: 16),
              ..._castDevices.map((device) => ListTile(
                    leading: Icon(
                      Icons.tv,
                      color: Colors.white,
                    ),
                    title: Text(
                      device.spec.friendlyName,
                      style: TextStyle(color: Colors.white),
                    ),
                    onTap: () {
                      _selectedCastDevice = device;
                      Navigator.pop(context);
                      _startCasting();
                    },
                  )),
            ],
          ),
        ),
      ),
    );
  }

  // Start casting to selected device
  Future<void> _startCasting() async {
    if (_selectedCastDevice == null) return;

    try {
      final alive = await _selectedCastDevice!.alive();
      if (!alive) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Selected device is not available')),
        );
        return;
      }

      // Pause local playback
      chewieController?.pause();

      // Start casting
      await _selectedCastDevice!.setAVTransportURI(
          SetAVTransportURIInput(widget.recomVideo.playLink));

      setState(() {
        _isCasting = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Casting to ${_selectedCastDevice!.spec.friendlyName}'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      logger.e("Error starting cast: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start casting'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // Stop casting
  Future<void> _stopCasting() async {
    if (_selectedCastDevice == null) return;

    try {
      final alive = await _selectedCastDevice!.alive();
      if (alive) {
        await _selectedCastDevice!.stop(const StopInput());
      }

      setState(() {
        _isCasting = false;
        _selectedCastDevice = null;
      });

      // Resume local playback
      chewieController?.play();
    } catch (e) {
      logger.e("Error stopping cast: $e");
    }
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    WidgetsBinding.instance.removeObserver(this);

    // 设置已销毁标志
    _disposed = true;

    // Calculate progress
    int progress = 0;
    int currentPosition = videoPlayerController.value.position.inSeconds;
    if (videoPlayerController.value.duration.inSeconds > 0) {
      progress =
          ((currentPosition / videoPlayerController.value.duration.inSeconds) *
                  100)
              .round();
    }

    // 已登录，可添加观看记录
    if (user.userId != -1) {
      _httpsRecomVideos.addViewHistory(widget.recomVideo.id, currentPosition,
          progress, user.authToken.toString());
    }

    // Add stopping cast when disposing
    if (_isCasting && _selectedCastDevice != null) {
      _stopCasting();
    }

    videoPlayerController.dispose();
    chewieController?.dispose();
    super.dispose();
  }

  // Share movie function
  void _shareMovie() {
    final String movieTitle = widget.recomVideo.name;
    final String shareMessage =
        "Check out this great movie: $movieTitle on MovieNest App!";
    Share.share(shareMessage);
  }

  bool _isCollected = false;

  // 添加按钮点击埋点
  void _trackButtonClick(String buttonName) {
    AnalyticsHelper().trackButtonClick(buttonName, position: 'video_detail');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: HotelAppTheme.buildDarkTheme().scaffoldBackgroundColor,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: <Widget>[
            Column(
              children: <Widget>[
                if (_isWebView && _webViewController != null)
                  _buildVideoPlayer()
                else
                  AspectRatio(
                    aspectRatio: 1.2,
                    child: _buildVideoPlayer(),
                  ),
              ],
            ),
            Positioned(
              top: (MediaQuery.of(context).size.width / 1.2) - 24.0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      _buildVipCardWidget(context),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 22.0, left: 12, right: 12, bottom: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    widget.recomVideo.name,
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 25,
                                      letterSpacing: 0.27,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                Row(
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isCollected
                                            ? Icons.favorite
                                            : Icons.favorite_border,
                                        color: _isCollected
                                            ? Colors.red
                                            : Colors.white,
                                      ),
                                      onPressed: () {
                                        _trackButtonClick('favorite_button');
                                        if (user.userId == -1) {
                                          chewieController?.pause();
                                          Navigator.of(context)
                                              .pushNamed(UnitRouter.login);
                                          return;
                                        }

                                        if (_isCollected) {
                                          _httpsRecomVideos
                                              .deleteCollection(
                                                  widget.recomVideo.id,
                                                  user.authToken.toString())
                                              .then((value) {
                                            setState(() {
                                              _isCollected = false;
                                            });
                                          });
                                        } else {
                                          _httpsRecomVideos
                                              .addCollection(
                                                  widget.recomVideo.id,
                                                  user.authToken.toString())
                                              .then((value) {
                                            setState(() {
                                              _isCollected = true;
                                            });
                                          });
                                        }
                                      },
                                    ),
                                    IconButton(
                                      icon: Icon(Icons.share,
                                          color: Colors.white),
                                      onPressed: () {
                                        _trackButtonClick('share_button');
                                        _shareMovie();
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            // Movie metadata row
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                if (widget.recomVideo.year != 0)
                                  _buildInfoChip('${widget.recomVideo.year}'),
                                if (widget.recomVideo.region != null)
                                  _buildInfoChip(widget.recomVideo.region!),
                                if (widget.recomVideo.type.isNotEmpty)
                                  _buildInfoChip(widget.recomVideo.type),
                                if (widget.recomVideo.duration > 0)
                                  _buildInfoChip(
                                      '${(widget.recomVideo.duration / 60).floor()}h ${widget.recomVideo.duration % 60}m'),
                              ],
                            ),
                            SizedBox(height: 16),
                            // Rating and premium badge
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.amber,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.star,
                                          color: Colors.black, size: 16),
                                      SizedBox(width: 4),
                                      Text(
                                        widget.recomVideo.score
                                                ?.toStringAsFixed(1) ??
                                            'N/A',
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 16),
                                // if (!widget.recomVideo.isFree)
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.red[700],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'Premium',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                            if (widget.recomVideo.description != null)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Description',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  LayoutBuilder(
                                    builder: (context, constraints) {
                                      // Calculate if text would overflow
                                      final textSpan = TextSpan(
                                        text: widget.recomVideo.description,
                                        style: TextStyle(
                                          color: Colors.grey[300],
                                          fontSize: 14,
                                          height: 1.5,
                                        ),
                                      );
                                      final textPainter = TextPainter(
                                        text: textSpan,
                                        textDirection: TextDirection.ltr,
                                        maxLines: 5,
                                      );
                                      textPainter.layout(
                                          maxWidth: constraints.maxWidth);

                                      final bool isTextOverflowing =
                                          textPainter.didExceedMaxLines;

                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.recomVideo.description!,
                                            style: TextStyle(
                                              color: Colors.grey[300],
                                              fontSize: 14,
                                              height: 1.5,
                                            ),
                                            maxLines: _isDescriptionExpanded
                                                ? null
                                                : 5,
                                            overflow: _isDescriptionExpanded
                                                ? null
                                                : TextOverflow.ellipsis,
                                          ),
                                          if (isTextOverflowing)
                                            TextButton(
                                              onPressed: () {
                                                setState(() {
                                                  _isDescriptionExpanded =
                                                      !_isDescriptionExpanded;
                                                });
                                              },
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    _isDescriptionExpanded
                                                        ? 'Show less'
                                                        : 'Show more',
                                                    style: TextStyle(
                                                        color: Colors.blue),
                                                  ),
                                                  Icon(
                                                    _isDescriptionExpanded
                                                        ? Icons
                                                            .keyboard_arrow_up
                                                        : Icons
                                                            .keyboard_arrow_down,
                                                    color: Colors.blue,
                                                    size: 16,
                                                  ),
                                                ],
                                              ),
                                            ),
                                        ],
                                      );
                                    },
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 12, right: 12),
                        child: Text(
                          'You may also like',
                          textAlign: TextAlign.left,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 22,
                            letterSpacing: 0.27,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      SizedBox(height: 8),
                      FutureBuilder<List<VideoRespVo>>(
                        future: _initialLoadFuture,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Center(
                                child: CircularProgressIndicator(
                              color:
                                  HotelAppTheme.buildDarkTheme().primaryColor,
                            ));
                          } else if (snapshot.hasError) {
                            return Center(child: Text('Error loading data'));
                          } else {
                            List<VideoRespVo> _items = snapshot.data!;
                            logger.i('Video list length: ${_items.length}');
                            return Container(
                              height: (_items.length / 3).ceil() * 220.0,
                              child: GridView.builder(
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  childAspectRatio: 0.6,
                                  mainAxisSpacing: 8.0,
                                  crossAxisSpacing: 8.0,
                                ),
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.symmetric(horizontal: 8),
                                itemCount: _items.length,
                                itemBuilder: (context, index) {
                                  return _buildItem(_items[index]);
                                },
                              ),
                            );
                          }
                        },
                      )
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
              child: SizedBox(
                width: AppBar().preferredSize.height,
                height: AppBar().preferredSize.height,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius:
                        BorderRadius.circular(AppBar().preferredSize.height),
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            )
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            _trackButtonClick('share_button');
            _shareMovie();
          },
          child: Icon(Icons.share),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: TextStyle(color: Colors.grey[300], fontSize: 12),
      ),
    );
  }

  _buildItem(VideoRespVo item) {
    return Ink(
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .pushNamed(UnitRouter.video_detail, arguments: item);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover image
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.0),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(item.coverImage ?? ""),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              flex: 8,
            ),
            // Movie details
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    item.name ?? "",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13.0,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    item.description ?? "",
                    style: TextStyle(
                      fontSize: 11.0,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }

  _buildVideoPlayer() {
    if (_isWebView && _webViewController != null) {
      return Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.width * 0.8, // WebView 使用更大的高度
        decoration: BoxDecoration(
          color: Colors.black,
        ),
        child: WebViewWidget(
          controller: _webViewController!,
        ),
      );
    }

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Stack(
        children: [
          Center(
            child: chewieController != null &&
                    chewieController!.videoPlayerController.value.isInitialized
                ? AspectRatio(
                    aspectRatio: videoPlayerController.value.aspectRatio,
                    child: Chewie(
                      controller: chewieController!,
                    ),
                  )
                : SizedBox(
                    width: 50,
                    height: 50,
                    child: CircularProgressIndicator(
                      color: HotelAppTheme.buildDarkTheme().primaryColor,
                    ),
                  ),
          ),

          // Add cast button overlay
          if (!_isWebView) ...[
            Positioned(
              top: 56,
              right: 16,
              child: _buildCastButton(),
            ),

            // Show casting indicator if currently casting
            if (_isCasting)
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.cast_connected, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'Casting to ${_selectedCastDevice?.spec.friendlyName}',
                          style: TextStyle(color: Colors.white),
                        ),
                        SizedBox(width: 8),
                        GestureDetector(
                          onTap: _stopCasting,
                          child: Icon(Icons.close, color: Colors.white, size: 20),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  // Cast button widget
  Widget _buildCastButton() {
    return Material(
      color: Colors.black.withOpacity(0.5),
      borderRadius: BorderRadius.circular(24),
      child: InkWell(
        borderRadius: BorderRadius.circular(24),
        onTap: _isSearchingDevices ? null : _searchCastDevices,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: _isSearchingDevices
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : Icon(
                  _isCasting ? Icons.cast_connected : Icons.cast,
                  color: Colors.white,
                  size: 24,
                ),
        ),
      ),
    );
  }

  Widget _buildSubtitleOption({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: Colors.white,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  // 创建VIP卡片组件的方法
  Widget _buildVipCardWidget(BuildContext context) {
    // VIP特性列表
    List<VipFeature> vipFeatures = getVipFeatures(context);

    return GestureDetector(
      onTap: () {
        chewieController?.pause();
        if (user.userId != -1) {
          //跳转到VIP购买页面
          Navigator.of(context).pushNamed(UnitRouter.vip_center);
        } else {
          Navigator.of(context).pushNamed(UnitRouter.login);
        }
      },
      child: Container(
        color: Color.fromARGB(255, 48, 33, 54),
        padding: const EdgeInsets.only(top: 8, left: 12, right: 12),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).vipCardTitle,
                        style: TextStyle(
                            color: Color(0xFFF5DFB1),
                            fontSize: 17,
                            fontWeight: FontWeight.bold),
                      ),
                      Text(
                        AppLocalizations.of(context).vipCardDescription,
                        style:
                            TextStyle(color: Color(0xFF9A90A0), fontSize: 11),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 4, horizontal: 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFFFBDFA6), Color(0xFFDBB360)],
                    ),
                    borderRadius: BorderRadius.circular(13),
                  ),
                  child: Text(
                    AppLocalizations.of(context).vipCardButton,
                    style: TextStyle(
                        color: Color.fromARGB(255, 48, 33, 54),
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8), // 添加间距
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子组件之间保持一定的间距
              children: List.generate(vipFeatures.length, (index) {
                return Expanded(
                  child: Container(
                    constraints:
                        BoxConstraints(maxWidth: 90, minHeight: 70), // 设置最大宽度
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start, // 确保内容顶部对齐
                      children: [
                        Image.asset(
                          vipFeatures[index].imagePath,
                          height: 40,
                          width: 40, // 调整图片大小以适应布局
                        ),
                        Text(
                          vipFeatures[index].text,
                          style:
                              TextStyle(fontSize: 8, color: Color(0xFF9A90A0)),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// 记录视频播放的埋点
  void _trackVideoPlay() {
    try {
      AnalyticsHelper().trackVideoPlay(
        widget.recomVideo.id, 
        widget.recomVideo.name ?? 'Unknown', 
        source: widget.recomVideo.playLink.contains('vidsrc.xyz') ? 'yts_api' : 'default'
      );
    } catch (e) {
      logger.e("视频播放埋点异常: $e");
    }
  }
}
