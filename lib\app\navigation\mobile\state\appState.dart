import 'package:flutter/material.dart';

class AppState extends ChangeNotifier {
  bool _isBusy = false;

  bool get isbusy => _isBusy;

  set isBusy(bool value) {
    if (value != _isBusy) {
      _isBusy = value;
      notifyListeners();
    }
  }

  int _pageIndex = 0;

  int get pageIndex {
    return _pageIndex;
  }

  set setPageIndex(int index) {
    _pageIndex = index;
    notifyListeners();
  }
}
