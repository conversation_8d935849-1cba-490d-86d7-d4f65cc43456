import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/api_https/impl/https_vip_repository.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

var logger = Logger(printer: PrettyPrinter());

class VipBuyQrCodePage extends StatefulWidget {
  final String consumeUrl;
  final int price;
  final Function(bool) onPaySuccess;

  const VipBuyQrCodePage({
    Key? key,
    required this.consumeUrl,
    required this.price,
    required this.onPaySuccess,
  }) : super(key: key);

  @override
  State<VipBuyQrCodePage> createState() => _VipBuyQrCodePageState();
}

class _VipBuyQrCodePageState extends State<VipBuyQrCodePage> {
  late Future<String> _qrCodeDataFuture;
  final HttpsVipRepository httpsVipRepository = HttpsVipRepository();
  Timer? _timer;
  bool isTimerRunning = false;
  String orderId = "";
  int leftSeconds = 300;

  @override
  void initState() {
    if (!isTimerRunning) {
      _startTimer();
    }
    super.initState();
    _qrCodeDataFuture = _getQrCodeData();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    isTimerRunning = true;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      //1皮蛋充值，2会员充值
      if (leftSeconds <= 0) {
        //倒计时时间到，直接退出
        widget.onPaySuccess.call(false);
        _timer?.cancel();
        Navigator.of(context).pop();
        return;
      }
      Map<String, dynamic> data =
          await httpsVipRepository.queryPayResult(orderId, 2);
      if (mounted)
        setState(() {
          leftSeconds--;
        });
      if (data.containsKey("status")) {
        int status = data['status'];
        if (status == 2) {
          //支付成功，返回刷新
          if (orderId == data['order_id']) {
            isTimerRunning = false;
            widget.onPaySuccess.call(true);
            _timer?.cancel();
            Navigator.of(context).pop();
          } else {
            //非法订单支付
            debugPrint("提交订单号和支付订单号不匹配");
          }
        } else {
          //待支付，继续等待
          debugPrint("status不是2，等待用户支付");
        }
      } else {
        debugPrint("支付返回没有status");
      }
    });
  }

  Future<String> _getQrCodeData() async {
    try {
      // 发送GET请求获取qrCode的data参数
      Response response = await Dio().get(widget.consumeUrl);
      if (response.statusCode == 200) {
        String prepayId = response.data['data']['prepayId'];
        orderId = prepayId;
        // orderId = prepayId.split(":")[2];
        logger.d("返回支付数据: $response.data['data']");
        return response.data['data']
            ['returnCode']; // 假设返回的数据中有qrCodeData字段，存储qrCode的数据
      } else {
        throw Exception('Failed to load QR code data');
      }
    } catch (e) {
      print('Error fetching QR code data: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFF383838),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20),
            child: Center(
              child: Text(
                "订单有效时长5分钟，请及时支付，剩余支付时间：${leftSeconds}s",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text("支付金额："),
              Text(
                "¥${widget.price / 100}",
                style: TextStyle(
                    color: Color(0xFF17B4BE),
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
              )
            ],
          ),
          SizedBox(
            height: 15,
          ),
          FutureBuilder<String>(
            future: _qrCodeDataFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return CircularProgressIndicator(); // 显示加载中的指示器
              } else if (snapshot.hasError) {
                return Text('Error: ${snapshot.error}');
              } else {
                return Container(
                  color: Colors.white,
                  child: PrettyQrView(
                    qrImage: QrImage(
                      QrCode.fromData(
                        data: snapshot.data!,
                        //生成的二维码更加清晰
                        errorCorrectLevel: QrErrorCorrectLevel.Q,
                      ),
                    ),
                    decoration: const PrettyQrDecoration(),
                  ),
                  width: 200,
                  height: 200,
                );
              }
            },
          ),
          Expanded(
              child: Center(
            child: Text("请使用微信扫码支付"),
          )),
        ],
      ),
    );
  }
}
