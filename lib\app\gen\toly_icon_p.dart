import 'package:flutter/widgets.dart';

// Power By 张风捷特烈--- Generated file. Do not edit.
// 欢迎支持： https://github.com/toly1994328/FlutterUnit
class TolyIconP {
  TolyIconP._();

  static const IconData add = IconData(0xe60e, fontFamily: "TolyIconP");
  static const IconData cloud = IconData(0xe7d9, fontFamily: "TolyIconP");
  static const IconData home = IconData(0xe647, fontFamily: "TolyIconP");
  static const IconData hat = IconData(0xe642, fontFamily: "TolyIconP");
  static const IconData exit = IconData(0xe650, fontFamily: "TolyIconP");
  static const IconData video = IconData(0xe630, fontFamily: "TolyIconP");
}
