import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:motion_toast/motion_toast.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';

import '../../api_https/impl/https_mission.dart';
import '../../model/vip/mission.dart';

class DeskMissionPanel extends StatefulWidget {
  DeskMissionPanel({Key? key}) : super(key: key);

  @override
  State<DeskMissionPanel> createState() => _DeskMissionPanelState();
}

class _DeskMissionPanelState extends State<DeskMissionPanel> {
  late Future<MissionPegg?> _futureMission;

  @override
  void initState() {
    _futureMission = HttpsMission().getMissionPegg();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).myPoints),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: FutureBuilder<MissionPegg?>(
            future: _futureMission,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                // Async operation is still in progress
                return Container(
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(),
                );
              } else if (snapshot.hasError) {
                // Error handling
                return Text('Error: ${snapshot.error}');
              } else {
                var missionPegg = snapshot.data!;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Flexible(
                    //   child: Center(
                    //     child: Text(
                    //       "皮蛋充值",
                    //       style: TextStyle(
                    //           fontSize: 24, fontWeight: FontWeight.bold),
                    //     ),
                    //   ),
                    //   flex: 1,
                    // ),
                    Flexible(
                      child: _buildLeftAllPegg(missionPegg),
                      flex: 3,
                    ),
                    Flexible(
                      child: _buildRightMission(missionPegg),
                      flex: 5,
                    )
                  ],
                );
              }
            }),
      ),
    );
  }

  _buildRightMission(MissionPegg missionPegg) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: missionPegg.mission
          .map((Mission mission) => _buildMissionItem(mission))
          .toList(),
    );
  }

  ///单个任务的Item
  Widget _buildMissionItem(Mission mission) {
    String statusText = mission.complete >= mission.condition
        ? AppLocalizations.of(context).claimed
        : AppLocalizations.of(context).claim;
    return Center(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                mission.name,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Image.asset('assets/images/logo/pegg.png', width: 40),
                  SizedBox(width: 10),
                  Text(
                    '+${mission.award}',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ],
          ),
          Spacer(),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0), // 设置圆角半径
                ),
                backgroundColor: mission.complete >= mission.condition
                    ? Colors.grey
                    : Color(0xFF12CDD9),
                padding: EdgeInsets.all(0),
                minimumSize: Size(60, 30)),
            onPressed: () async {
              if (mission.complete >= mission.condition) {
                return;
              }
              // 领取皮蛋，首先看会员是否已过期，过期则不让领取皮蛋
              var user = GlobalInfo.instance.user;
              if (user.vipEnd == 0) {
                MotionToast.warning(description: Text('您的云端会员已过期，请及时充值!'))
                    .show(context);
                return;
              }
              // 看任务是不是需要会员才能领取

              // 领取后刷新页面
              bool success = await HttpsMission()
                  .submitMission(mid: mission.mid, pegg: mission.award);
              if (success) {
                setState(() {
                  _futureMission = HttpsMission().getMissionPegg();
                });
              }
            },
            child: Text(
              statusText,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          )
        ],
      ),
    );
  }

  _buildLeftAllPegg(MissionPegg missionPegg) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/images/logo/pegg.png', width: 100),
            SizedBox(width: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).myPoints,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  '${missionPegg.pegg}',
                  style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                )
              ],
            )
          ],
        ),
        SizedBox(
          height: 30,
        ),
        // SizedBox(
        //   child: ElevatedButton(
        //     style: ElevatedButton.styleFrom(
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(5.0), // 设置圆角半径
        //       ),
        //       backgroundColor: Color(0xFF12CDD9),
        //     ),
        //     onPressed: () async {
        //       MotionToast.warning(description: Text('暂请前往手机版充值皮蛋!'))
        //           .show(context);
        //     },
        //     child: Text(
        //       "皮蛋充值",
        //       textAlign: TextAlign.center,
        //       style: TextStyle(color: Colors.white, fontSize: 16),
        //     ),
        //   ),
        //   width: 120,
        //   height: 35,
        // )
      ],
    );
  }
}
