import 'package:flutter/material.dart';
import 'package:app/app/router/unit_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/app/model/videos/video_resp.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pieces_ai/app/model/user_info_global.dart';
import '../../../../authentication/models/user.dart' as pieces_user;
import '../../../../utils/analytics_helper.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class WatchHistoryPage extends StatefulWidget {
  @override
  _WatchHistoryPageState createState() => _WatchHistoryPageState();
}

class _WatchHistoryPageState extends State<WatchHistoryPage> {
  bool _isEditing = false;
  List<PlayRecordSelect> selectedItems = [];

  // 分页相关状态
  int _page = 0;
  int _pageSize = 15;
  bool _hasMore = true;
  bool _isLoadingMore = false;
  bool _isDeleting = false;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<RefreshIndicatorState> _refreshKey = GlobalKey();

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        // 如果退出编辑模式，则清除所有选择
        for (var item in playRecords) {
          item.isSelected = false;
        }
        selectedItems.clear();
      }
    });
  }

  // 删除方法
  Future<void> _deleteSelectedItems() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      List<int> movieIds =
          selectedItems.map((item) => item.videoRespVo.id).toList();
      await deleteWatchHistoryRecords(user, movieIds);

      setState(() {
        playRecords.removeWhere((item) => selectedItems.contains(item));
        selectedItems.clear();
        _isEditing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context).deleteSuccess)),
      );
    } catch (e) {
      logger.e('删除历史记录失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context).deleteFailed)),
      );
    } finally {
      setState(() {
        _isDeleting = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _fetchPlayRecords(); // 初始化时加载数据
    
    // 添加页面浏览埋点
    AnalyticsHelper().trackPageView('watch_history');
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _fetchPlayRecords();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildHistoryItem(PlayRecordSelect item) {
    return InkWell(
      onTap: () {
        logger.d('点击了整个卡片 - ${item}');
        Navigator.of(context)
            .pushNamed(UnitRouter.video_detail, arguments: item.videoRespVo);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                item.videoRespVo.coverImage ?? '',
                width: 150,
                height: 100,
                fit: BoxFit.cover,
                alignment: Alignment.topCenter, // 使图片顶部对齐
                errorBuilder: (context, exception, stackTrace) {
                  return Image.asset(
                    'assets/images/widgets/draft_empty.png',
                    width: 150,
                    height: 100,
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.videoRespVo.name ?? '',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    item.progressText ?? '',
                    style: TextStyle(color: Color(0xFFBDBDBD)),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.videoRespVo.type ?? '',
                    style: TextStyle(color: Color(0xFFBDBDBD)),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            if (_isEditing)
              Checkbox(
                value: item.isSelected,
                onChanged: (bool? value) {
                  setState(() {
                    item.isSelected = value!;
                    if (value) {
                      selectedItems.add(item);
                    } else {
                      selectedItems.remove(item);
                    }
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoader() => Padding(
        padding: EdgeInsets.symmetric(vertical: 16),
        child: Center(child: CircularProgressIndicator()),
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xFF191919),
        leading: IconButton(
          icon: Icon(FontAwesomeIcons.angleLeft, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(AppLocalizations.of(context).playRecord,
            style: TextStyle(color: Colors.white)),
        centerTitle: true,
        actions: [
          TextButton(
            child: Text(
                _isEditing
                    ? AppLocalizations.of(context).done
                    : AppLocalizations.of(context).edit,
                style: TextStyle(color: Colors.white)),
            onPressed: _toggleEdit,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              key: _refreshKey,
              onRefresh: _onRefresh,
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: playRecords.length + (_hasMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == playRecords.length) {
                    return _buildLoader();
                  }
                  return _buildHistoryItem(playRecords[index]);
                },
              ),
            ),
          ),
          // 修改删除按钮的构建部分
          if (_isEditing && selectedItems.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton.icon(
                onPressed: _isDeleting ? null : _deleteSelectedItems,
                icon: _isDeleting
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : Icon(Icons.delete),
                label: Text(
                  _isDeleting
                      ? AppLocalizations.of(context).deleting
                      : '${AppLocalizations.of(context).deleteSelected} (${selectedItems.length})',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  disabledBackgroundColor: Colors.grey,
                ),
              ),
            ),
        ],
      ),
      backgroundColor: Color(0xFF191919),
    );
  }

  List<PlayRecordSelect> playRecords = [];
  final user = GlobalInfo.instance.user;

  Future<void> _fetchPlayRecords({bool clearList = false}) async {
    if (_isLoadingMore || !mounted) return;

    setState(() => _isLoadingMore = true);

    try {
      final newRecords = await getRecentPlayRecords(user, page: _page);

      if (!mounted) return;

      setState(() {
        if (clearList) {
          playRecords = newRecords;
        } else {
          playRecords.addAll(newRecords);
        }

        _page++; // 增加页码

        _hasMore = newRecords.length >= _pageSize; // 根据实际 pageSize 调整
      });
    } catch (e) {
      logger.e('加载失败: $e');
    } finally {
      if (mounted) setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _onRefresh() async {
    if (!mounted) return;

    setState(() {
      _page = 0;
      _hasMore = true;
      _isLoadingMore = false;
    });

    await _fetchPlayRecords(clearList: true);
  }

  Future<List<PlayRecordSelect>> getRecentPlayRecords(pieces_user.User user,
      {int page = 0}) async {
    try {
      logger.d('分页查询: page=$page, pageSize=$_pageSize');
      final response = await Supabase.instance.client
          .from('t_watch_history')
          .select('movie_id, progress, update_time, video(*)')
          .eq('access_token', user.authToken.toString())
          .order('update_time', ascending: false)
          .range(page * _pageSize, (page + 1) * _pageSize - 1);

      final List<PlayRecordSelect> result = [];
      for (final record in response) {
        final progress = record['progress'];
        final videoData = record['video'];

        if (videoData is Map<String, dynamic>) {
          final videoRespVo = VideoRespVo.fromJson(videoData);
          final progressText = getProgressText(progress);

          result.add(PlayRecordSelect(
            progressText: progressText,
            videoRespVo: videoRespVo,
            isSelected: false,
          ));
        } else {
          logger.w('视频数据格式不正确: $videoData');
        }
      }

      return result;
    } catch (e) {
      logger.e('分页查询错误: $e');
      return [];
    }
  }

  String getProgressText(int progress) {
    return '${AppLocalizations.of(context).progress} $progress%';
  }

  Future<void> deleteWatchHistoryRecords(
      pieces_user.User user, List<int> movieIds) async {
    try {
      logger.d('删除历史记录: movieIds=$movieIds');
      await Supabase.instance.client
          .from('t_watch_history')
          .delete()
          .eq('access_token', user.authToken.toString())
          .inFilter('movie_id', movieIds.cast<dynamic>());
    } catch (e) {
      logger.e('删除历史记录失败: $e');
      rethrow;
    }
  }

  // 添加视频点击埋点
  void _trackVideoClick(int videoId, String videoName) {
    AnalyticsHelper().trackViewItem('video', videoId.toString());
  }
}

class PlayRecordSelect {
  final String progressText;
  VideoRespVo videoRespVo;
  bool isSelected = false;

  PlayRecordSelect({
    required this.progressText,
    required this.videoRespVo,
    this.isSelected = false,
  });
}
