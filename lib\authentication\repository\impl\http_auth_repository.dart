import 'package:flutter/foundation.dart';
import 'package:pieces_ai/utils/http_utils/http_util.dart';

import '../../../utils/http_utils/task_result.dart';
import '../../models/user.dart';
import '../auth_repository.dart';

const baseUrl = 'https://api.pencil-stub.com';
const String kSendSms = '/user/login/get-code';
const String kLogin = '/user/login';
const String userInfo = '/user/info';
const String kRegister = '/register';

class HttpAuthRepository implements AuthRepository {
  @override
  Future<TaskResult<User>> login({
    required String username,
    required String password,
  }) async {
    String errorMsg = "";
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['spec'] = {
      'phone': username,
      'login_code': password,
    };
    try {
      var result = await HttpUtil.instance.client.post(
        baseUrl + kLogin,
        data: param,
      );

      if (result.data != null) {
        if (result.data['code'] == 200) {
          print("登录返回:" + result.data['data'].toString());
          return TaskResult<User>(
            token: result.data['data']['auth_Token'],
            data: User.fromJson(result.data['data']),
            success: result.data['code'] == 200,
          );
        } else {
          return TaskResult<User>(
            token: result.data['msg'],
            data: null,
            success: false,
          );
        }
      }
    } catch (e) {
      errorMsg = e.toString();
    }

    return TaskResult.error(msg: '请求错误: $errorMsg');
  }

  @override
  Future<TaskResult<bool>> register({
    required String email,
    required String code,
  }) async {
    String errorMsg = "";
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['email'] = email;
    param['activeCode'] = code;
    try {
      var result = await HttpUtil.instance.client.post(kRegister, data: param);

      if (result.data != null) {
        return TaskResult.success(data: result.data, token: '');
      }
    } catch (e) {
      errorMsg = e.toString();
    }

    return TaskResult.error(msg: '请求错误: $errorMsg');
  }

  @override
  Future<TaskResult<String>> sendSms({required String phoneNum}) async {
    Map<String, dynamic> param = await HttpUtil.withBaseParam();
    param['uid'] = -1;
    param['spec'] = {'phone': phoneNum};
    var result = await HttpUtil.instance.client
        .post(HttpUtil.apiBaseUrl + kSendSms, data: param);
    if (result.data != null) {
      print('发送验证码返回:' + result.data.toString());
      if (result.data['code'] == 200) {
        return const TaskResult.success(data: "成功", token: '');
      } else {
        return TaskResult.error(msg: result.data['msg']);
      }
    }
    return const TaskResult.error(msg: '请求错误');
  }

  @override
  Future<User> getUserInfo() async {
    Map<String, dynamic> params = await HttpUtil.withBaseParam();
    params['spec'] = {'uid': params['uid']};
    print("getUserInfo参数：" + params.toString());

    var result = await HttpUtil.instance.client.post(
      HttpUtil.apiBaseUrl + userInfo,
      data: params,
    );

    if (result.data != null) {
      if (result.data['code'] == 200) {
        debugPrint("获取服务器用户信息:" + result.data['data'].toString());
        return User.fromJson(result.data['data']);
      } else {
        debugPrint("获取服务器用户信息失败:" + result.data['msg']);
        return User(
            name: 'unLogin',
            pegg: -1,
            gender: 0,
            vType: 0,
            userId: -1,
            vipEnd: -1,
            headIcon:
                'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
            authToken: '');
      }
    }

    return User(
        name: 'unLogin',
        pegg: -1,
        gender: 0,
        vType: 0,
        userId: -1,
        vipEnd: -1,
        headIcon:
            'https://imgs.pencil-stub.com/data/avatar/2021-12-28/e5767cd381874fcb9d89ff350442f3b8.png',
        authToken: '');
  }
}
