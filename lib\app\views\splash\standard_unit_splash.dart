import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:app/app.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:pieces_ai/utils/http_utils/http_util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

import '../../navigation/unit_navigation.dart';
import 'pieces_ai_text.dart';
import '../../../../utils/analytics_helper.dart';

/// 说明: app 闪屏页
var logger = Logger(printer: PrettyPrinter(methodCount: 0));

class StandardUnitSplash extends StatefulWidget {
  const StandardUnitSplash({Key? key}) : super(key: key);

  @override
  _StandardUnitSplashState createState() => _StandardUnitSplashState();
}

class _StandardUnitSplashState extends State<StandardUnitSplash>
    with TickerProviderStateMixin {
  static const int _minCost = 1500;

  int _recorder = 0;

  final Paint paint = Paint()
    ..style = PaintingStyle.stroke
    ..shader = ui.Gradient.linear(
      const Offset(0, 0),
      const Offset(22, 0),
      [Colors.red, Colors.yellow, Colors.blue, Colors.green],
      [1 / 4, 2 / 4, 3 / 4, 1],
      TileMode.mirror,
      Matrix4.rotationZ(pi / 4).storage,
    );

  @override
  void initState() {
    super.initState();
    _recorder = DateTime.now().millisecondsSinceEpoch;
  }

  @override
  Widget build(BuildContext context) {
    final Size winSize = MediaQuery.of(context).size;
    final AppBloc appBloc = BlocProvider.of<AppBloc>(context);

    return FutureBuilder<AppState>(
      future: appBloc.storage.initApp(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasData) {
            appBloc.emit(snapshot.data!);
            _listenStart(context, snapshot.data!);
            logger.d("_listenStart  启动弹框");
          }
          //两秒后启动跳转
          // Future.delayed(const Duration(seconds: 2), () {
          //   Navigator.of(context).pushReplacement(
          //       MaterialPageRoute(builder: (context) => const UnitNavigation()));
          // });
        }

        return Column(
          children: [
            const Spacer(),
            Expanded(
                child: Wrap(
              direction: Axis.vertical,
              alignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Stack(
                  children: [
                    Image.asset(
                      'assets/images/ic_launcher.png',
                      width: 120,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                _buildFlutterUnitText(winSize.height, winSize.width),
              ],
            )),
            const Expanded(
                child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Positioned(
                    bottom: 15,
                    child: Wrap(
                      direction: Axis.vertical,
                      alignment: WrapAlignment.center,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Text("Power By MovieNest",
                            style: UnitTextStyle.splashShadows),
                        // Text("2024 ",
                        //     style: UnitTextStyle.splashShadows),
                      ],
                    )),
              ],
            ))
          ],
        );
      },
    );
  }

  Widget _buildFlutterUnitText(double winH, double winW) {
    return FlutterUnitText(
      text: StrUnit.appName,
      color: Colors.black87,
    );
  }

  showCustomTrackingDialog(SharedPreferences prefs, BuildContext context) {
    //弹窗显示隐私政策
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            elevation: 5,
            title: Text("隐私政策"),
            content: RichText(
              text: TextSpan(
                text: '欢迎使用MovieNest，为了更好地保护您的隐私和个人信息安全，我们根据国家相关法律规定拟定了',
                style: TextStyle(color: Colors.black87),
                children: [
                  TextSpan(
                    text: '《隐私政策》',
                    style: TextStyle(color: Colors.blue),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 处理点击事件，例如打开浏览器
                        Navigator.of(context).pushNamed(
                            UnitRouter.article_detail,
                            arguments:
                                "https://www.pencil-stub.com/html/tweet/tweet_privacy_agreement2.html");
                      },
                  ),
                  TextSpan(
                    text: '和',
                    style: TextStyle(color: Colors.black87),
                  ),
                  TextSpan(
                    text: '《用户协议》',
                    style: TextStyle(color: Colors.blue),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 处理点击事件，例如打开浏览器
                        Navigator.of(context).pushNamed(
                            UnitRouter.article_detail,
                            arguments: "assets/data/user_vip.html");
                      },
                  ),
                  TextSpan(
                    text: '，请您在使用前仔细阅读并确认您同意以上条款。',
                    style: TextStyle(color: Colors.black87),
                  ),
                ],
              ),
            ),
            // content: Text(
            //     "欢迎使用MovieNest，为了更好地保护您的隐私和个人信息安全，我们根据国家相关法律规定拟定了《隐私政策》和《用户协议》，请您在使用前仔细阅读并确认您同意以上条款"),
            actions: <Widget>[
              TextButton(
                child: Text("agree"),
                onPressed: () {
                  // GlobalConfiguration().addValue("first_login", false);
                  prefs.setBool("first_login", false);
                  Navigator.of(context).pop();
                  Navigator.of(context).pushReplacementNamed(UnitRouter.nav);
                },
              ),
              TextButton(
                child: Text("disagree"),
                onPressed: () {
                  SystemNavigator.pop();
                },
              )
            ],
          );
        });
    //不使用showDialog，使用一个类似的widget
  }

  showIosCustomTrackingDialog(SharedPreferences prefs) {
    //弹窗显示隐私政策
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            elevation: 5,
            title: Text(AppLocalizations.of(context).privacyPolicy),
            content: RichText(
              text: TextSpan(
                text: AppLocalizations.of(context).welcomeToMovieNest,
                style: TextStyle(color: Colors.black87),
                children: [
                  TextSpan(
                    text:
                        '<' + AppLocalizations.of(context).privacyPolicy + '>',
                    style: TextStyle(color: Colors.blue),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 处理点击事件，例如打开浏览器
                        Navigator.of(context).pushNamed(
                            UnitRouter.article_detail,
                            arguments: "assets/data/Privacy.html");
                      },
                  ),
                  TextSpan(
                    text: 'and',
                    style: TextStyle(color: Colors.black87),
                  ),
                  TextSpan(
                    text:
                        '<' + AppLocalizations.of(context).userAgreement + '>',
                    style: TextStyle(color: Colors.blue),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 处理点击事件，例如打开浏览器
                        Navigator.of(context).pushNamed(
                            UnitRouter.article_detail,
                            arguments: "assets/data/user_vip.html");
                      },
                  ),
                  TextSpan(
                    text: ',' + AppLocalizations.of(context).pleaseRead,
                    style: TextStyle(color: Colors.black87),
                  ),
                ],
              ),
            ),
            // content: Text(
            //     "欢迎使用MovieNest，为了更好地保护您的隐私和个人信息安全，我们根据国家相关法律规定拟定了《隐私政策》和《用户协议》，请您在使用前仔细阅读并确认您同意以上条款"),
            actions: <Widget>[
              TextButton(
                child: Text("next"),
                onPressed: () async {
                  // GlobalConfiguration().addValue("first_login", false);
                  // Wait for dialog popping animation
                  await Future.delayed(const Duration(milliseconds: 100));
                  // Request system's tracking authorization dialog
                  var status = await AppTrackingTransparency
                      .requestTrackingAuthorization();
                  if (status == TrackingStatus.authorized) {
                    prefs.setBool("first_login", false);
                    Navigator.of(context).pop();
                    Navigator.of(context).pushReplacementNamed(UnitRouter.nav);
                  } else {
                    prefs.setBool("first_login", false);
                    Navigator.of(context).pop();
                    Navigator.of(context).pushReplacementNamed(UnitRouter.nav);
                  }
                  //初始化友盟
                  // UmengCommonSdk.initCommon("", "5fb4fc8a257f6b73c0970a37", "TWEET_IOS_Pro");
                },
              ),
            ],
          );
        });
  }

  // 监听资源加载完毕，启动，触发事件
  void _listenStart(BuildContext context, AppState state) async {
    HttpUtil.instance.rebase(PathUnit.baseUrl);
    int cost = DateTime.now().millisecondsSinceEpoch - _recorder;
    
    // 启动耗时小于 _minCost 时，等待 delay 毫秒
    int delay = cost < _minCost ? _minCost - cost : 0;

    //看是否首次登录，如果是首次登录，弹出隐私政策提示
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var firstLogin = prefs.getBool("first_login");
    logger.d("firstLogin:$firstLogin");
    
    // 发送APP启动埋点
    await AnalyticsHelper().trackAppOpen(isColdStart: true);
    
    // 添加 Firebase Analytics 测试事件
    try {
      await FirebaseAnalytics.instance.logEvent(
        name: 'app_launch_test',
        parameters: {
          'launch_time': DateTime.now().toIso8601String(),
          'is_first_login': (firstLogin == null || firstLogin) ? 'true' : 'false',
          'platform': Platform.operatingSystem,
          'os_version': Platform.operatingSystemVersion,
        },
      );
      print('Firebase Analytics 测试事件发送成功');
    } catch (e) {
      print('Firebase Analytics 测试事件发送失败: $e');
    }
    
    if (firstLogin == null || firstLogin) {
      // 发送首次安装打开埋点
      await AnalyticsHelper().trackFirstOpen();
      
      if (Platform.isIOS) {
        //请求跟踪授权
        final status = await AppTrackingTransparency.requestTrackingAuthorization();
        // Show a custom explainer dialog before the system dialog
        if (status == TrackingStatus.authorized) {
          prefs.setBool("first_login", false);
        } else {
          prefs.setBool("first_login", false);
        }
        await showIosCustomTrackingDialog(prefs);
      } else if (Platform.isAndroid) {
        showCustomTrackingDialog(prefs, context);
      }
    } else {
      Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const UnitNavigation()));
    }
  }
}
