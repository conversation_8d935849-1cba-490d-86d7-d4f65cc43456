import '../../utils/http_utils/task_result.dart';
import '../models/user.dart';

abstract class AuthRepository {
  // 用户登录接口
  Future<TaskResult<User>> login({
    required String username,
    required String password,
  });

  Future<User> getUserInfo();

  // 用户注册接口
  // 邮箱注册
  Future<TaskResult<bool>> register({
    required String email,
    required String code,
  });

  // 发送手机验证信息
  Future<TaskResult<String>> sendSms({
    required String phoneNum,
  });
}
