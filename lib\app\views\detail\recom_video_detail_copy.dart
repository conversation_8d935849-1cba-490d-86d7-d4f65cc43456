// import 'package:app/app/router/unit_router.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:logger/logger.dart';
// import 'package:media_kit/media_kit.dart';
// import 'package:media_kit_video/media_kit_video.dart';
// import 'package:pieces_ai/app/model/user_info_global.dart';
// import 'package:pieces_ai/app/model/videos/video_resp.dart';
// import 'package:pieces_ai/components/custom_widget/hotel_booking/hotel_app_theme.dart';
// import 'package:share_plus/share_plus.dart';
//
// import '../../api_https/impl/https_recom_videos.dart';
// import '../../data/vip_features.dart';
//
// var logger = Logger(printer: PrettyPrinter(methodCount: 0));
//
// class VideoDetail extends StatefulWidget {
//   final VideoRespVo recomVideo;
//
//   const VideoDetail({Key? key, required this.recomVideo}) : super(key: key);
//
//   @override
//   State<VideoDetail> createState() => _VideoDetailState();
// }
//
// class _VideoDetailState extends State<VideoDetail>
//     with TickerProviderStateMixin, WidgetsBindingObserver {
//   // 添加一个标志跟踪页面是否已销毁
//   bool _disposed = false;
//   final double infoHeight = 364.0;
//   double opacity1 = 0.0;
//   double opacity2 = 0.0;
//   double opacity3 = 0.0;
//
//   bool _isDescriptionExpanded = false;
//
//   late Future<List<VideoRespVo>> _initialLoadFuture;
//   final HttpsRecomVideos _httpsRecomVideos = HttpsRecomVideos();
//
//   // late final videoPlayerController;
//   //
//   // late final chewieController;
//   List<SubtitleTrack> _subtitleTracks = [];
//   int _selectedIndex = 0;
//
//   late final player = Player();
//
//   // Create a [VideoController] to handle video output from [Player].
//   late final controller = VideoController(player);
//
//   int currentPosition = 1;
//
//   // 修改用户
//   final user = GlobalInfo.instance.user;
//
//   @override
//   void initState() {
//     // Register observer to detect app lifecycle changes
//     WidgetsBinding.instance.addObserver(this);
//     logger.d("播放地址是${widget.recomVideo.playLink}");
//     _initPlayer();
//
//     if (user.userId != -1) {
//       _httpsRecomVideos
//           .getCollectionStatus(widget.recomVideo.id, user.authToken.toString())
//           .then((value) {
//         setState(() {
//           _isCollected = value > 0;
//         });
//       });
//     }
//
//     _initialLoadFuture = _httpsRecomVideos.loadSearchVideoList(0, 15, '');
//     super.initState();
//   }
//
//   // Handle app lifecycle state changes
//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) {
//     if (_disposed) return;
//
//     if (state == AppLifecycleState.paused) {
//       // App is in background
//       logger.d("应用进入后台，暂停播放");
//       player.pause();
//     } else if (state == AppLifecycleState.resumed) {
//       // App is in foreground
//       logger.d("应用恢复前台，继续播放");
//       player.play();
//     }
//   }
//
//   // Page visibility changes (already implemented)
//   @override
//   void deactivate() {
//     super.deactivate();
//     logger.d("页面被覆盖，暂停播放");
//     player.pause();
//   }
//
//   // Add page resuming functionality
//   @override
//   void activate() {
//     super.activate();
//     if (!_disposed) {
//       logger.d("页面恢复可见，继续播放");
//       player.play();
//     }
//   }
//
//   _initPlayer() async {
//     // 如果页面已销毁，立即返回
//     if (_disposed) return;
//
//     // Load the saved playback position
//     final savedPosition = await _httpsRecomVideos.loadPlaybackPosition(
//         widget.recomVideo.id, user.authToken.toString());
//     logger.d("加载播放位置是$savedPosition");
//
//     // 再次检查页面是否已销毁
//     if (_disposed) return;
//
//     try {
//       // First open the media
//       await player.open(
//         Media(widget.recomVideo.playLink, start: savedPosition),
//       );
//
//       // 检查组件是否已销毁
//       if (_disposed) {
//         // 如果已销毁，确保停止播放
//         player.stop();
//         return;
//       }
//
//       // Set up position listener
//       player.stream.position.listen((position) {
//         // 确保组件未销毁时才更新状态
//         if (!_disposed && mounted) {
//           currentPosition = position.inSeconds;
//         }
//       });
//
//       // 字幕处理
//       if (!_disposed && widget.recomVideo.subtitle != null) {
//         _subtitleTracks.add(await SubtitleTrack.uri(
//           widget.recomVideo.subtitle!,
//           title: 'English',
//         ));
//         if (!_disposed) {
//           player.setSubtitleTrack(_subtitleTracks[0]);
//         }
//       }
//     } catch (e) {
//       logger.e("视频初始化错误: $e");
//     }
//   }
//
//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     // 设置已销毁标志
//     _disposed = true;
//
//     // 确保停止播放
//     player.stop();
//
//     // Calculate progress
//     int progress = 0;
//     if (player.state.duration.inSeconds > 0) {
//       progress =
//           ((currentPosition / player.state.duration.inSeconds) * 100).round();
//     }
//
//     // 已登录，可添加观看记录
//     if (user.userId != -1) {
//       _httpsRecomVideos.addViewHistory(widget.recomVideo.id, currentPosition,
//           progress, user.authToken.toString());
//     }
//
//     player.dispose();
//     super.dispose();
//   }
//
//   // Share movie function
//   void _shareMovie() {
//     final String movieTitle = widget.recomVideo.name;
//     final String shareMessage =
//         "Check out this great movie: $movieTitle on MovieNest App!";
//     Share.share(shareMessage);
//   }
//
//   bool _isCollected = false;
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: HotelAppTheme.buildDarkTheme().scaffoldBackgroundColor,
//       child: Scaffold(
//         backgroundColor: Colors.transparent,
//         body: Stack(
//           children: <Widget>[
//             Column(
//               children: <Widget>[
//                 AspectRatio(
//                   aspectRatio: 1.2,
//                   child: _buildVideoPlayer(),
//                 ),
//               ],
//             ),
//             Positioned(
//               top: (MediaQuery.of(context).size.width / 1.2) - 24.0,
//               bottom: 0,
//               left: 0,
//               right: 0,
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: Colors.black,
//                 ),
//                 child: SingleChildScrollView(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: <Widget>[
//                       _buildVipCardWidget(context),
//                       Padding(
//                         padding: const EdgeInsets.only(
//                             top: 22.0, left: 12, right: 12, bottom: 12),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Expanded(
//                                   child: Text(
//                                     widget.recomVideo.name,
//                                     textAlign: TextAlign.left,
//                                     style: TextStyle(
//                                       fontWeight: FontWeight.w600,
//                                       fontSize: 25,
//                                       letterSpacing: 0.27,
//                                       color: Colors.white,
//                                     ),
//                                   ),
//                                 ),
//                                 Row(
//                                   children: [
//                                     IconButton(
//                                       icon: Icon(
//                                         _isCollected
//                                             ? Icons.favorite
//                                             : Icons.favorite_border,
//                                         color: _isCollected
//                                             ? Colors.red
//                                             : Colors.white,
//                                       ),
//                                       onPressed: () {
//                                         if (user.userId == -1) {
//                                           Navigator.of(context)
//                                               .pushNamed(UnitRouter.login);
//                                           return;
//                                         }
//
//                                         if (_isCollected) {
//                                           _httpsRecomVideos
//                                               .deleteCollection(
//                                                   widget.recomVideo.id,
//                                                   user.authToken.toString())
//                                               .then((value) {
//                                             setState(() {
//                                               _isCollected = false;
//                                             });
//                                           });
//                                         } else {
//                                           _httpsRecomVideos
//                                               .addCollection(
//                                                   widget.recomVideo.id,
//                                                   user.authToken.toString())
//                                               .then((value) {
//                                             setState(() {
//                                               _isCollected = true;
//                                             });
//                                           });
//                                         }
//                                       },
//                                     ),
//                                     IconButton(
//                                       icon: Icon(Icons.share,
//                                           color: Colors.white),
//                                       onPressed: _shareMovie,
//                                     ),
//                                   ],
//                                 ),
//                               ],
//                             ),
//                             SizedBox(height: 8),
//                             // Movie metadata row
//                             Wrap(
//                               spacing: 8,
//                               runSpacing: 8,
//                               children: [
//                                 if (widget.recomVideo.year != 0)
//                                   _buildInfoChip('${widget.recomVideo.year}'),
//                                 if (widget.recomVideo.region != null)
//                                   _buildInfoChip(widget.recomVideo.region!),
//                                 if (widget.recomVideo.type.isNotEmpty)
//                                   _buildInfoChip(widget.recomVideo.type),
//                                 if (widget.recomVideo.duration > 0)
//                                   _buildInfoChip(
//                                       '${(widget.recomVideo.duration / 60).floor()}h ${widget.recomVideo.duration % 60}m'),
//                               ],
//                             ),
//                             SizedBox(height: 16),
//                             // Rating and premium badge
//                             Row(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Container(
//                                   padding: EdgeInsets.symmetric(
//                                       horizontal: 8, vertical: 4),
//                                   decoration: BoxDecoration(
//                                     color: Colors.amber,
//                                     borderRadius: BorderRadius.circular(4),
//                                   ),
//                                   child: Row(
//                                     mainAxisSize: MainAxisSize.min,
//                                     children: [
//                                       Icon(Icons.star,
//                                           color: Colors.black, size: 16),
//                                       SizedBox(width: 4),
//                                       Text(
//                                         widget.recomVideo.score
//                                                 ?.toStringAsFixed(1) ??
//                                             'N/A',
//                                         style: TextStyle(
//                                           color: Colors.black,
//                                           fontWeight: FontWeight.bold,
//                                           fontSize: 14,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(width: 16),
//                                 if (!widget.recomVideo.isFree)
//                                   Container(
//                                     padding: EdgeInsets.symmetric(
//                                         horizontal: 8, vertical: 4),
//                                     decoration: BoxDecoration(
//                                       color: Colors.red[700],
//                                       borderRadius: BorderRadius.circular(4),
//                                     ),
//                                     child: Text(
//                                       'Premium',
//                                       style: TextStyle(
//                                         color: Colors.white,
//                                         fontSize: 12,
//                                         fontWeight: FontWeight.bold,
//                                       ),
//                                     ),
//                                   ),
//                               ],
//                             ),
//                             SizedBox(height: 16),
//                             if (widget.recomVideo.description != null)
//                               Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     'Description',
//                                     style: TextStyle(
//                                       color: Colors.white,
//                                       fontSize: 18,
//                                       fontWeight: FontWeight.bold,
//                                     ),
//                                   ),
//                                   SizedBox(height: 8),
//                                   LayoutBuilder(
//                                     builder: (context, constraints) {
//                                       // Calculate if text would overflow
//                                       final textSpan = TextSpan(
//                                         text: widget.recomVideo.description,
//                                         style: TextStyle(
//                                           color: Colors.grey[300],
//                                           fontSize: 14,
//                                           height: 1.5,
//                                         ),
//                                       );
//                                       final textPainter = TextPainter(
//                                         text: textSpan,
//                                         textDirection: TextDirection.ltr,
//                                         maxLines: 5,
//                                       );
//                                       textPainter.layout(
//                                           maxWidth: constraints.maxWidth);
//
//                                       final bool isTextOverflowing =
//                                           textPainter.didExceedMaxLines;
//
//                                       return Column(
//                                         crossAxisAlignment:
//                                             CrossAxisAlignment.start,
//                                         children: [
//                                           Text(
//                                             widget.recomVideo.description!,
//                                             style: TextStyle(
//                                               color: Colors.grey[300],
//                                               fontSize: 14,
//                                               height: 1.5,
//                                             ),
//                                             maxLines: _isDescriptionExpanded
//                                                 ? null
//                                                 : 5,
//                                             overflow: _isDescriptionExpanded
//                                                 ? null
//                                                 : TextOverflow.ellipsis,
//                                           ),
//                                           if (isTextOverflowing)
//                                             TextButton(
//                                               onPressed: () {
//                                                 setState(() {
//                                                   _isDescriptionExpanded =
//                                                       !_isDescriptionExpanded;
//                                                 });
//                                               },
//                                               child: Row(
//                                                 mainAxisSize: MainAxisSize.min,
//                                                 children: [
//                                                   Text(
//                                                     _isDescriptionExpanded
//                                                         ? 'Show less'
//                                                         : 'Show more',
//                                                     style: TextStyle(
//                                                         color: Colors.blue),
//                                                   ),
//                                                   Icon(
//                                                     _isDescriptionExpanded
//                                                         ? Icons
//                                                             .keyboard_arrow_up
//                                                         : Icons
//                                                             .keyboard_arrow_down,
//                                                     color: Colors.blue,
//                                                     size: 16,
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                         ],
//                                       );
//                                     },
//                                   ),
//                                 ],
//                               ),
//                           ],
//                         ),
//                       ),
//                       Padding(
//                         padding: EdgeInsets.only(left: 12, right: 12),
//                         child: Text(
//                           'You may also like',
//                           textAlign: TextAlign.left,
//                           style: TextStyle(
//                             fontWeight: FontWeight.w600,
//                             fontSize: 22,
//                             letterSpacing: 0.27,
//                             color: Colors.white,
//                           ),
//                         ),
//                       ),
//                       SizedBox(height: 8),
//                       FutureBuilder<List<VideoRespVo>>(
//                         future: _initialLoadFuture,
//                         builder: (context, snapshot) {
//                           if (snapshot.connectionState ==
//                               ConnectionState.waiting) {
//                             return Center(
//                                 child: CircularProgressIndicator(
//                               color:
//                                   HotelAppTheme.buildDarkTheme().primaryColor,
//                             ));
//                           } else if (snapshot.hasError) {
//                             return Center(child: Text('Error loading data'));
//                           } else {
//                             List<VideoRespVo> _items = snapshot.data!;
//                             logger.i('Video list length: ${_items.length}');
//                             return Container(
//                               height: (_items.length / 3).ceil() * 220.0,
//                               child: GridView.builder(
//                                 gridDelegate:
//                                     SliverGridDelegateWithFixedCrossAxisCount(
//                                   crossAxisCount: 3,
//                                   childAspectRatio: 0.6,
//                                   mainAxisSpacing: 8.0,
//                                   crossAxisSpacing: 8.0,
//                                 ),
//                                 physics: const NeverScrollableScrollPhysics(),
//                                 padding: EdgeInsets.symmetric(horizontal: 8),
//                                 itemCount: _items.length,
//                                 itemBuilder: (context, index) {
//                                   return _buildItem(_items[index]);
//                                 },
//                               ),
//                             );
//                           }
//                         },
//                       )
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             Padding(
//               padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
//               child: SizedBox(
//                 width: AppBar().preferredSize.height,
//                 height: AppBar().preferredSize.height,
//                 child: Material(
//                   color: Colors.transparent,
//                   child: InkWell(
//                     borderRadius:
//                         BorderRadius.circular(AppBar().preferredSize.height),
//                     child: Icon(
//                       Icons.arrow_back_ios,
//                       color: Colors.white,
//                     ),
//                     onTap: () {
//                       Navigator.pop(context);
//                     },
//                   ),
//                 ),
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildInfoChip(String text) {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
//       decoration: BoxDecoration(
//         color: Colors.grey[800],
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Text(
//         text,
//         style: TextStyle(color: Colors.grey[300], fontSize: 12),
//       ),
//     );
//   }
//
//   _buildItem(VideoRespVo item) {
//     return Ink(
//       child: InkWell(
//         onTap: () {
//           Navigator.of(context)
//               .pushNamed(UnitRouter.video_detail, arguments: item);
//         },
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Cover image
//             Flexible(
//               child: Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(4.0),
//                   image: DecorationImage(
//                     image: CachedNetworkImageProvider(item.coverImage ?? ""),
//                     fit: BoxFit.cover,
//                   ),
//                 ),
//               ),
//               flex: 8,
//             ),
//             // Movie details
//             Flexible(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Name
//                   Text(
//                     item.name ?? "",
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontSize: 13.0,
//                       fontWeight: FontWeight.bold,
//                     ),
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                   Text(
//                     item.description ?? "",
//                     style: TextStyle(
//                       fontSize: 11.0,
//                       color: Colors.grey[600],
//                     ),
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ],
//               ),
//               flex: 2,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   _buildVideoPlayer() {
//     return SizedBox(
//       width: MediaQuery.of(context).size.width,
//       height: MediaQuery.of(context).size.width * 9 / 16,
//       child: Padding(
//         padding: EdgeInsets.only(
//           top: MediaQuery.of(context).padding.top,
//           bottom: 35,
//         ),
//         child: MaterialVideoControlsTheme(
//           normal: MaterialVideoControlsThemeData(
//             displaySeekBar: true,
//             seekBarHeight: 3.0,
//             buttonBarButtonSize: 24.0,
//             buttonBarButtonColor: Colors.white,
//             topButtonBar: [
//               const Spacer(),
//               MaterialDesktopCustomButton(
//                 onPressed: () {
//                   showModalBottomSheet(
//                     context: context,
//                     backgroundColor: Color(0xFF282828),
//                     shape: RoundedRectangleBorder(
//                       borderRadius:
//                           BorderRadius.vertical(top: Radius.circular(16)),
//                     ),
//                     builder: (context) => _buildSubtitleBottomSheet(),
//                   );
//                 },
//                 icon: const Icon(Icons.settings),
//               ),
//             ],
//           ),
//           fullscreen: const MaterialVideoControlsThemeData(
//             displaySeekBar: false,
//             automaticallyImplySkipNextButton: false,
//             automaticallyImplySkipPreviousButton: false,
//           ),
//           child: Scaffold(
//             body: Video(
//               controller: controller,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildSubtitleBottomSheet() {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Container(
//           padding: EdgeInsets.symmetric(vertical: 16),
//           child: Text(
//             'Subtitles',
//             style: TextStyle(
//               color: Colors.white,
//               fontSize: 16,
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//         ),
//         Divider(
//           color: Colors.grey[800],
//           height: 1,
//         ),
//         ListView.builder(
//           shrinkWrap: true,
//           itemCount: _subtitleTracks.length,
//           itemBuilder: (context, index) {
//             final track = _subtitleTracks[index];
//             return _buildSubtitleOption(
//               title: track.title ?? 'Subtitle ${index + 1}',
//               isSelected: _selectedIndex == index,
//               onTap: () {
//                 _selectedIndex = index;
//                 logger.d('Selected subtitle track: ${track..uri}');
//                 player.setSubtitleTrack(track);
//                 Navigator.pop(context);
//               },
//             );
//           },
//         ),
//         SizedBox(height: 8),
//       ],
//     );
//   }
//
//   Widget _buildSubtitleOption({
//     required String title,
//     required bool isSelected,
//     required VoidCallback onTap,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//         child: Row(
//           children: [
//             Expanded(
//               child: Text(
//                 title,
//                 style: TextStyle(
//                   color: Colors.white,
//                   fontSize: 14,
//                 ),
//               ),
//             ),
//             if (isSelected)
//               Icon(
//                 Icons.check,
//                 color: Colors.white,
//                 size: 20,
//               ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   // 创建VIP卡片组件的方法
//   Widget _buildVipCardWidget(BuildContext context) {
//     // VIP特性列表
//     List<VipFeature> vipFeatures = getVipFeatures(context);
//
//     return GestureDetector(
//       onTap: () {
//         if (user.userId != -1) {
//           //跳转到VIP购买页面
//           Navigator.of(context).pushNamed(UnitRouter.vip_center);
//         } else {
//           Navigator.of(context).pushNamed(UnitRouter.login);
//         }
//       },
//       child: Container(
//         color: Color.fromARGB(255, 48, 33, 54),
//         padding: const EdgeInsets.only(top: 8, left: 12, right: 12),
//         child: Column(
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         AppLocalizations.of(context).vipCardTitle,
//                         style: TextStyle(
//                             color: Color(0xFFF5DFB1),
//                             fontSize: 17,
//                             fontWeight: FontWeight.bold),
//                       ),
//                       Text(
//                         AppLocalizations.of(context).vipCardDescription,
//                         style:
//                             TextStyle(color: Color(0xFF9A90A0), fontSize: 11),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.symmetric(vertical: 4, horizontal: 10),
//                   decoration: BoxDecoration(
//                     gradient: LinearGradient(
//                       colors: [Color(0xFFFBDFA6), Color(0xFFDBB360)],
//                     ),
//                     borderRadius: BorderRadius.circular(13),
//                   ),
//                   child: Text(
//                     AppLocalizations.of(context).vipCardButton,
//                     style: TextStyle(
//                         color: Color.fromARGB(255, 48, 33, 54),
//                         fontWeight: FontWeight.bold),
//                   ),
//                 ),
//               ],
//             ),
//             SizedBox(height: 8), // 添加间距
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子组件之间保持一定的间距
//               children: List.generate(vipFeatures.length, (index) {
//                 return Expanded(
//                   child: Container(
//                     constraints:
//                         BoxConstraints(maxWidth: 90, minHeight: 70), // 设置最大宽度
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.start, // 确保内容顶部对齐
//                       children: [
//                         Image.asset(
//                           vipFeatures[index].imagePath,
//                           height: 40,
//                           width: 40, // 调整图片大小以适应布局
//                         ),
//                         Text(
//                           vipFeatures[index].text,
//                           style:
//                               TextStyle(fontSize: 8, color: Color(0xFF9A90A0)),
//                           textAlign: TextAlign.center,
//                         ),
//                       ],
//                     ),
//                   ),
//                 );
//               }),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
